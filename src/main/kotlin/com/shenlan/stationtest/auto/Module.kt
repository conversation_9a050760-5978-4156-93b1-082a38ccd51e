package com.shenlan.stationtest.auto

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.shenlan.*
import com.shenlan.stationtest.util.*
import org.apache.commons.beanutils.BeanUtils
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.nio.file.Files
import java.nio.file.Paths

@JsonIgnoreProperties(value = arrayOf("systemId", "displayName", "navigateUrl", "menuTarget", "ifVisible", "expand", "ifEnabled", "resourceType", "remark", "allowEdit", "serialNumber"))
class Module : BaseModel {
    var parentId: String? = null
    var systemId: String = ""
    var menuCode: String? = null
    var menuName: String = ""
    var displayName: String? = null
    var menuType: String? = null
    var navigateUrl: String? = null
    var menuTarget: String? = null
    var ifVisible: Int = 0
    var expand: Int = 0
    var ifEnabled: Int = 0
    var resourceType: String? = null
    var remark: String? = null
    var allowEdit: Int = 0
    var serialNumber: Int? = null
    var children: MutableList<Module> = mutableListOf()

    var ifCheck = false

    constructor()
}

@Mapper
interface ModuleMapper : BaseMapper<Module> {

    @Select("select * from tbl_module order by menucode ")
    fun getAll(): MutableList<Module>
}

object Modu {
    var moduleList = getBean(ModuleMapper::class.java).getAll()
    var topList = initTop()

    fun
    fun initTop(): MutableList<Module> {
        var tmpList = getBean(ModuleMapper::class.java).getAll()
        var topList = tmpList.filter { it.menuType == "1" }.toMutableList()
        topList.forEach {
            recu(it)
        }
        return topList
    }

    fun recu(module: Module) {
        module.children = getChildrenDirectList(module.id)
        module.children.forEach {
            recu(it)
        }
    }

    fun getChildrenDirectList(parentId: String): MutableList<Module> {
        var list = mutableListOf<Module>()
        moduleList.forEach {
            if (it.parentId == parentId) {
                list.add(BeanUtils.cloneBean(it) as Module)
            }
        }
        return list
    }

    fun getTrueList(moduleList: MutableList<Module>): MutableList<String> {
        var rlt = mutableListOf<String>()
        moduleList.forEach {
            getTrueList(it, rlt)
        }
        return rlt
    }

    fun getTrueList(module: Module, rlt: MutableList<String>) {
        if (module.ifCheck) {
            rlt.add(module.id)
        }
        module.children.forEach {
            getTrueList(it, rlt)
        }
    }

    fun setTrue(moduleList: MutableList<Module>, rlt: List<String>) {
        moduleList.forEach {
            setTrue(it, rlt)
        }
    }
    fun setTrue(module: Module, rlt: List<String>) {
        if (rlt.contains(module.id)) {
            module.ifCheck = true
            module.children.forEach {
                setTrue(it, rlt)
            }
        }
    }

    fun readFromTxt() {//1	顶部菜单 2	导航菜单 3	功能菜单 4	功能按钮
        var list = Files.readAllLines(Paths.get("E:\\workspace3\\markmiss\\menu.txt")).filter { it != "" }
        var menuList = mutableListOf<Module>()
        list.forEach {
            println(it)
            Module().apply {
                var name = it.split(" ")
                id = uuid()
                menuCode = name[0]
                menuName = name[1]
                menuType = name[2]
                menuList.add(this)
            }
        }
        menuList.forEach {
            it.children = getChildren(it.menuCode!!, menuList)
            it.children.forEach { module ->
                module.parentId = it.id
            }
        }

        menuList.forEach {
            if (it.menuCode!!.length == 1) {
                it.pj()
            }
        }
        sqlMapper.update("delete from tbl_module")
        getBean(ModuleMapper::class.java).insertList(menuList)
    }

    fun getChildren(menuCode: String, menuList: List<Module>): MutableList<Module> {
        var moduleList = mutableListOf<Module>()
        menuList.forEach {
            if (getDotNumber(menuCode) + 1 == getDotNumber(it.menuCode!!) && it.menuCode!!.startsWith(menuCode)) {
                moduleList.add(it)
            }
        }
        return moduleList
    }

    fun getDotNumber(str: String): Int {
        var i = 0
        str.forEach { if (it == '.') i++ }
        return i
    }
}

class ModuleSearch : BaseSearch {
    constructor()
}

@Service
class ModuleService(mapper: ModuleMapper) : BaseService<Module, ModuleMapper>(mapper) {


}

@RestController
@RequestMapping("/api/Module")
class ModuleResource(service: ModuleService) : BaseResource<ModuleSearch, Module, ModuleMapper, ModuleService>(service) {

}
