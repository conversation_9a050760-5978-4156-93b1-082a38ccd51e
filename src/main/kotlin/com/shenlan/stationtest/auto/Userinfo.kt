package com.shenlan.stationtest.auto

import com.alibaba.fastjson.JSON
import com.aliyuncs.CommonRequest
import com.aliyuncs.DefaultAcsClient
import com.aliyuncs.http.MethodType
import com.aliyuncs.profile.DefaultProfile
import com.shenlan.*
import com.shenlan.stationtest.config.AppPro
import com.shenlan.stationtest.util.*
import org.apache.commons.beanutils.BeanUtils
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.*

class Userinfo : BaseModel {
    var username: String = ""
    var loginName: String? = null
    var proAreaIds: String = ""
    var unitId: String = ""
    var unitName: String? = null
    var deptId: String? = null
    var deptName: String? = null
    var password: String? = null
    var email: String? = null
    var mobilePhone: String = ""
    var ifEnabled: Int = 1   //0:禁用,1:启用
    var ifInit: Int = 1   //是否是新用户：0:否,1:是
    var remark: String? = null
    var roleIds: String = ""
    var rolenames: String = ""
    var orgName: String = ""

    constructor()
}

class UserPassword {
    var oldPassword: String? = ""
    var newPassword: String? = ""
}

@Mapper
interface UserinfoMapper : BaseMapper<Userinfo> {

    @Select(value = ["<script>", """
        select * from (select a.id,a.username,loginName,proAreaIds,a.unitId,
        (SELECT orgShort from tbl_organize where id =a.unitId) unitName,a.deptId,(SELECT orgShort from tbl_organize where id =a.deptId) deptName,
password,email,mobilePhone,ifEnabled,remark,(SELECT group_concat(rolename SEPARATOR '，') from tbl_roleinfo where id in (select targetid from tbl_permission where resourcetype='TBL_USERINFO' and resourceid=a.id and targettype='TBL_ROLEINFO')) rolenames from tbl_userinfo a
) a where 1=1
<if test="username != null and username !=''">
AND userName like CONCAT('%', #{username}, '%')
</if>
<if test="unitName != null and unitName !=''">
AND unitName like CONCAT('%', #{unitName}, '%')
</if>
<if test="phone != null and phone !=''">
AND mobilephone like CONCAT('%', #{phone}, '%')
</if>
<if test="orgId != null and orgId !=''">
AND unitId = #{orgId}
</if>
${'$'}{orgIdsSQL}
        """, "</script>"])
    override fun getList(search: BaseSearch): List<Userinfo>


    @Select("select a.id,a.username,loginName,proAreaIds,a.unitId,(SELECT orgShort from tbl_organize where id =a.unitId) unitName,a.deptId,(SELECT orgShort from tbl_organize where id =a.deptId) deptName,password,email,mobilePhone,ifEnabled,remark,(SELECT group_concat(rolename SEPARATOR '，') from tbl_roleinfo where id in (select targetid from tbl_permission where resourcetype='TBL_USERINFO' and resourceid=a.id and targettype='TBL_ROLEINFO')) rolenames from tbl_userinfo a where id=#{param1}")
    override fun getInfo(id: String): Userinfo?

    @Select("select * from tbl_userinfo a where mobilePhone=#{param1} limit 1")
    fun getInfoByMobile(mobile: String): Userinfo?

    @Select("select * from tbl_userinfo a where username=#{param1} limit 1")
    fun getInfoByUsername(username: String): Userinfo?
}

class UserinfoSearch : BaseSearch {
    var username: String? = null
    var unitName: String? = null
    var phone: String? = null
    var ifAll: String? = null
    var orgId: String = ""

    constructor()

    var childrenOrgId2: String? = null

    var orgIdsSQL = ""
        get() {
            if (Organ.organizeList.isEmpty()) {
                Organ.initTopOrganize()
            }
            var orgIdList = mutableListOf<String>()
            var tempOrgId = getUser().orgId
            if (orgId.isNotEmpty()) {
                tempOrgId = orgId
            }
            var topOrganize = BeanUtils.cloneBean(Organ.organizeList.first { it.id == tempOrgId }) as Organize
            Organ.recu(topOrganize)
            Organ.getOrgIdsInStr(topOrganize, orgIdList)
//            orgIdList.getInStr().pj()
            return "and unitId in (" + orgIdList.getInStr() + ")"
        }

}

@Service
class UserinfoService(mapper: UserinfoMapper) : BaseService<Userinfo, UserinfoMapper>(mapper) {

//    override fun getList(page: BaseSearch): Result {
//        page as UserinfoSearch
//
//        return super.getList(page)
//    }

    override fun save(model: Userinfo): Result {
        var error = validate(model)
        if (error.notEmpty()) {
            return Result.getError(error)
        }
        if (model.id.notEmpty()) {
            delete(model.id)
        }
        if (model.id.isEmpty()) model.id = uuid()
        model.password = AesUtil.aesEncrypt(model.password!!,AesUtil.AES_KEY)
        if (1 == mapper.insert(model)) {
            insertChildren(model)
        }
        return Result.getSuccess(model.id)
    }

    override fun validate(model: Userinfo): String {
        if (model.ifInsert) {
//            model.password = AesUtil.aesEncrypt("FDC@2022",AesUtil.AES_KEY)
            if(model.mobilePhone.isNotEmpty()) {
                if (1 == sqlMapper.queryInt("select count(1) from tbl_userinfo where mobilephone='${model.mobilePhone}'"))
                    return "手机号已存在"
            }
//            if(model.loginName.isNotEmpty()) {
//                if (1 == sqlMapper.queryInt("select count(1) from tbl_userinfo where loginName='${model.loginName}'"))
//                    return "账号已存在"
//            }
        } else {
            if(model.mobilePhone.isNotEmpty()) {
                if (1 == sqlMapper.queryInt("select count(1) from tbl_userinfo where mobilephone='${model.mobilePhone}' and id !='${model.id}'"))
                    return "手机号已存在"
            }
//            if(model.loginName.isNotEmpty()) {
//                if (1 == sqlMapper.queryInt("select count(1) from tbl_userinfo where loginName='${model.loginName}'and id !='${model.id}'"))
//                    return "账号已存在"
//            }
        }
        return super.validate(model)
    }

    override var insertChildren: (Userinfo) -> Unit = { user ->
        //多机构改成单机构
        var permissionList = mutableListOf<Permission>()
        Permission().apply {
            id = uuid()
            resourceType = "TBL_USERINFO"
            resourceId = user.id
            targetType = "TBL_ORGANIZE"
            targetId = user.unitId
            serialNumber = 0
            permissionList.add(this)
        }

        user.roleIds.split(",").map {
            Permission().apply {
                id = uuid()
                resourceType = "TBL_USERINFO"
                resourceId = user.id
                targetType = "TBL_ROLEINFO"
                targetId = it
                permissionList.add(this)
            }
        }
        permissionMapper.insertList(permissionList)
    }

    override var getChildren: (Userinfo) -> Unit = { user ->
        //        user.organize = deepClone(getUser().organize) as Organize
//        var organizeList = sqlMapper.queryList<String>("select targetid from tbl_permission where resourcetype='TBL_USERINFO' and resourceid='${user.id}' and targettype='TBL_ORGANIZE'")
//        Organ.setTrue(user.organize!!, organizeList)
        user.roleIds = roleinfoMapper.getListByUserid(user.id).map { it.id }.joinToString(",")
    }

    override var deleteChildren: (Userinfo) -> Unit = { user ->
        sqlMapper.update("delete from tbl_permission where resourcetype='TBL_USERINFO' and resourceid='${user.id}' and targettype='TBL_ORGANIZE'")
        sqlMapper.update("delete from tbl_permission where resourcetype='TBL_USERINFO' and resourceid='${user.id}' and targettype='TBL_ROLEINFO'")
    }

}

@RestController
@RequestMapping("/api/Userinfo")
class UserinfoResource(service: UserinfoService) : BaseResource<UserinfoSearch, Userinfo, UserinfoMapper, UserinfoService>(service) {
    @PostMapping("updatePassword")
    fun updatePassword(@RequestBody userPassword: UserPassword): Result {
        var user = getBean(UserinfoMapper::class.java).getInfoByMobile(getUser().mobilePhone)
        var password = AesUtil.aesEncrypt(userPassword.newPassword!!,AesUtil.AES_KEY)
        if (user != null) {
//            if (user.ifInit == 1) {
//                if (userPassword.oldPassword != "FDC@2022") { return Result.getError("原密码输入错误，请重新输入") }
//                if (userPassword.newPassword == "FDC@2022") { return Result.getError("新密码与初始密码相同，请重新输入") }
//                sqlMapper.update("update tbl_userinfo set password='${password}', ifInit=0 where mobilephone='${getUser().mobilePhone}'")
//            } else {
//                if (getUser().password != AesUtil.aesEncrypt(userPassword.oldPassword!!,AesUtil.AES_KEY)) {
//                    return Result.getError("原密码输入错误，请重新输入")
//                }
//                if (getUser().password == AesUtil.aesEncrypt(userPassword.newPassword!!,AesUtil.AES_KEY)) {
//                    return Result.getError("新密码与旧密码相同，请重新输入")
//                }
//                sqlMapper.update("update tbl_userinfo set password='${password}' where mobilephone='${getUser().mobilePhone}'")
//            }
            if (getUser().password != AesUtil.aesEncrypt(userPassword.oldPassword!!,AesUtil.AES_KEY)) {
                return Result.getError("原密码输入错误，请重新输入")
            }
            if (getUser().password == AesUtil.aesEncrypt(userPassword.newPassword!!,AesUtil.AES_KEY)) {
                return Result.getError("新密码与旧密码相同，请重新输入")
            }
            sqlMapper.update("update tbl_userinfo set password='${password}' where mobilephone='${getUser().mobilePhone}'")
        }
        return Result.success
    }

    @GetMapping("updateDefultPassword/{userId}")
    fun updateDefultPassword(@PathVariable userId: String): Result {
        var password = AesUtil.aesEncrypt("FDC@2022",AesUtil.AES_KEY)
        sqlMapper.update("update tbl_userinfo set password='${password}' where id='${userId}'")
//        Outerapi.sendSms(getBean(UserinfoMapper::class.java).getInfo(userId)!!.mobilePhone, "SMS_222466378", JSONObject().apply { this.put("password", "FDC@2022") })
        sendSmspassword(getBean(UserinfoMapper::class.java).getInfo(userId)!!.mobilePhone)
        return Result.success
    }

    fun sendSmspassword(number: String): Boolean {//重置密码
        try {
            "".log.info("sendsms")
            val profile = DefaultProfile.getProfile("cn-hangzhou", "LTAI4FdqhciqpaNSEk1fjAd4", "******************************")
            val client = DefaultAcsClient(profile)
            val request = CommonRequest()
            request.setMethod(MethodType.POST)
            request.setDomain(AppPro.smsUrl)
            request.setVersion("2017-05-25")
            request.setAction("SendSms")
            request.putQueryParameter("RegionId", "cn-hangzhou")
            request.putQueryParameter("PhoneNumbers", number)
            request.putQueryParameter("SignName", "深蓝信息")
            request.putQueryParameter("TemplateCode", "SMS_222466378")
            request.putQueryParameter("TemplateParam", "{\"password\":\"FDC@2022\"}")
            val response = client.getCommonResponse(request)
            "".log.info("aliyun response ${response.data}, mobile:${number}")
            val code = JSON.parseObject(response.data).get("Code")
            if (code == "OK") {
                return true
            }
        } catch (e: Exception) {
            "".log.info(e.message)
            return false
        }
        return false
    }

}
