package com.shenlan.stationtest.auto

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.aliyuncs.CommonRequest
import com.aliyuncs.DefaultAcsClient
import com.aliyuncs.http.MethodType
import com.aliyuncs.profile.DefaultProfile
import com.shenlan.Result
import com.shenlan.stationtest.auto.ais.ChModel
import com.shenlan.stationtest.auto.ais.StationParse
import com.shenlan.stationtest.config.AppPro
import com.shenlan.stationtest.config.Constants
import com.shenlan.stationtest.config.User
import com.shenlan.stationtest.config.getUserByMobile
import com.shenlan.stationtest.util.*
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.apache.ibatis.annotations.Update
import org.springframework.web.bind.annotation.*
import java.util.*
import javax.servlet.http.HttpServletRequest

@Mapper
interface SqlMapper {

    @Select("\${sql}")
    fun queryInt(@Param("sql") sql: String): Int?

    @Select("\${sql}")
    fun queryStr(@Param("sql") sql: String): String?

    @Select("\${sql}")
    fun queryMap(@Param("sql") sql: String): HashMap<String, String>

    @Update("\${sql}")
    fun update(@Param("sql") sql: String)

    //    @Select("\${sql}")
    //    fun getSql(@Param("sql") sql: String): Any
    //
    @Select("\${sql}")
    fun queryListMap(@Param("sql") sql: String): List<HashMap<String, Any>>

    @Select("\${sql}")
    fun <T> queryList(@Param("sql") sql: String): List<T>

    @Select("\${sql}")
    fun <T> query(@Param("sql") sql: String): T?
}

@Mapper
interface OpenMapper {

    @Select("""select id, username,password,mobilephone,(select targetid from tbl_permission b where resourceId=a.id and resourceType='TBL_USERINFO' and targetType = 'TBL_ORGANIZE' order by serialnumber limit 0,1) orgId,
(select rolecode from tbl_permission b,tbl_roleinfo c where b.targetid=c.id and resourceId=a.id and resourceType='TBL_USERINFO' and targetType = 'TBL_ROLEINFO' limit 0,1)
rolecode from tbl_userinfo a where mobilephone=#{param1}
    """)
    fun getUserByMobile(mobile: String): User?

    @Select("""select menucode from tbl_module where id in (
select distinct targetid from tbl_permission where resourceType ='TBL_ROLEINFO' and targetType = 'TBL_MODULE' and resourceId in (
select targetid from tbl_permission where resourceType = "TBL_USERINFO" and  resourceId =#{param1} and targetType = "TBL_ROLEINFO"
))ORDER BY menucode
        """)
    fun getMenuList(userid: String): List<String>

    @Select("select targetid from tbl_permission where resourcetype='TBL_ROLEINFO' and targettype='TBL_MODULE' and resourceid=(select id from tbl_roleinfo where rolename='域登录角色') order by targetid")
    fun getMenuListByRoleName(roleName: String): List<String>

    @Select(" select count(1) from tbl_userInfo WHERE mobilePhone = #{number} ")
    fun getByMobilePhone(number: String): Int
    
    @Select("select count(1) from tbl_userInfo WHERE id =#{userId} and ifEnabled = '1' ")
    fun checkUserinfoIfEnabled(userId: String): Int

    @Select("select count(1) from tbl_roleInfo WHERE roleCode =#{roleId} and ifEnabled = '1' ")
    fun checkRoleinfoIfEnabled(roleId: String): Int

    @Select("select count(1) from tbl_organize WHERE id =#{orgId} and ifEnabled = '1'  ")
    fun checkOrginfoIfEnabled(orgId: String): Int
}

fun sendSms(number: String, code: String): Boolean {
    try {
        "".log.info("sendsms")
        val profile = DefaultProfile.getProfile("cn-hangzhou", "LTAI4FdqhciqpaNSEk1fjAd4", "******************************")
        val client = DefaultAcsClient(profile)
        val request = CommonRequest()
        request.setMethod(MethodType.POST)
        request.setDomain(AppPro.smsUrl)
        request.setVersion("2017-05-25")
        request.setAction("SendSms")
        request.putQueryParameter("RegionId", "cn-hangzhou")
        request.putQueryParameter("PhoneNumbers", number)
        request.putQueryParameter("SignName", "深蓝信息")
        request.putQueryParameter("TemplateCode", "SMS_175330236")
        request.putQueryParameter("TemplateParam", "{\"code\":\"${code}\"}")
        val response = client.getCommonResponse(request)
        "".log.info("aliyun response ${response.data}, mobile:${number}")
        val code = JSON.parseObject(response.data).get("Code")
        if (code == "OK") {
            return true
        }
    } catch (e: Exception) {
        "".log.info(e.message)
        return false
    }
    return false
}

@RestController
@RequestMapping("/api/open")
class OpenResource {
    @GetMapping("/csrf")
    fun csrf(): Result = Result.success

    @GetMapping("shutdown")
    fun shutdown() { //        Application.context?.close()
        log.info("shutdown")
        System.exit(1)
    }

    @GetMapping("getAccount")
    fun getAccount(): Result {
        return Result.getSuccess(getUser())
    }

    @GetMapping("getAllDict")
    fun getAllDict(): Result {
        Dict.dictList = Dict.init()
        return Result.getSuccess(Dict.dictList)
    }

    @GetMapping("getLocationList")
    fun getLocationList() = Result.getSuccess(Dict.initLocationList())

    @GetMapping("/getCode/{number}")
    fun getCode(@PathVariable number: String): Result {
        if (getBean(OpenMapper::class.java).getByMobilePhone(number) == 0) {
            return Result.getError("手机号码尚未注册")
        }
        var code = (Random().nextInt(9000) + 1000).toString()
        if (!sendSms(number, code)) {
            return Result.getSuccessInfo("验证码发送失败")
        } else {
            Constants.loginCodeMap.set(number, LoginCode("", code, System.currentTimeMillis()))
        }
        return Result.getSuccessInfo("验证码已发送，请注意查收")
    }

    @GetMapping("/checkLogin/{number}/{code}/{type}")//type 0 验证码登录 1 密码登录 2域登录
    fun checkLogin(@PathVariable number: String, @PathVariable code: String, @PathVariable type: String): Result {
//        var code = AesUtil.aesDecrypt(code1, AesUtil.AES_KEY)!!
        if (type == "0") {
            if (getBean(OpenMapper::class.java).getByMobilePhone(number) == 0) {
                return Result.getError("手机号码尚未注册")
            }
            // 获取用户
            var user = getUserByMobile(number)
//            user.pj()
            if (getBean(OpenMapper::class.java).checkUserinfoIfEnabled(user!!.id) == 0) {
                return Result.getError("该用户已被禁用")
            }
            if (getBean(OpenMapper::class.java).checkRoleinfoIfEnabled(user!!.roleCode) == 0) {
                return Result.getError("该角色已被禁用")
            }
            if (getBean(OpenMapper::class.java).checkOrginfoIfEnabled(user!!.orgId) == 0) {
                return Result.getError("该机构已被禁用")
            }
            var loginCode = Constants.loginCodeMap.get(number)
            if (loginCode == null) {
                return Result.getError("请获取验证码")
            } else {
                if (loginCode.code != code) {
                    return Result.getError("验证码不匹配")
                }
                if (loginCode.isTimeOut()) {
                    return Result.getError("验证码已失效，请重新获取验证码")
                }
            }
        } else if (type == "1") {
            return checkLoginPassword(number, code)
        } else {
            if (LdapAuth.authLdap(number, code)) {
                Constants.loginCodeMap.set(number, LoginCode("", code, System.currentTimeMillis()).apply { this.type = type })
            } else {
                return Result.Companion.getError("用户名或密码错误")
            }
        }
        return Result.success
    }

    fun checkLoginPassword(number: String, code: String): Result {
        var user = getBean(UserinfoMapper::class.java).getInfoByMobile(number)
        if (user == null) user = getBean(UserinfoMapper::class.java).getInfoByUsername(number)
        if (user == null) {
            return Result.getError("账号尚未注册")
        } else {
            if (user.ifEnabled == 0) {
                return Result.getError("该用户已被禁用")
            }
            var password = AesUtil.aesEncrypt(code,AesUtil.AES_KEY)
            if (user.password != password) {
                return Result.getError("密码错误")
            }
            Constants.loginCodeMap.set(number, LoginCode("", code, System.currentTimeMillis()))
        }

        return Result.success
    }

    //    @GetMapping("/getNavigation")
    //    fun getNavigation() = Result.getSuccess(Navi.getNavigation())
    //
    @GetMapping("/getModuleList")
    fun getModuleList() = Result.getSuccess(Modu.topList)
    //
    //    @GetMapping("/getWebsocket")
    //    fun getWebsocket(): Result {
    //        return Result.getSuccess(AppPro.websocketUrl)
    //    }
    //
    //    @GetMapping("/getFrontValue")
    //    fun getFrontValue(): Result {
    //        var map = mutableMapOf<String, String>()
    //        map["websocketUrl"] = AppPro.websocketUrl
    //        map["tiandituUrl"] = AppPro.tiandituUrl
    //        map["seachartUrl"] = AppPro.seachartUrl
    //        map["chinaseachartUrl"] = AppPro.chinaseachartUrl
    //        return Result.getSuccess(map)
    //    }

    //    @Autowired
    //    lateinit var sessionRegistry: SessionRegistry

    //    @GetMapping("/logoutUser/{userName}")
    //    fun logoutUser(@PathVariable userName: String): Result {
    //        var sessionRegistry = getBean(SessionRegistry::class.java)
    //        sessionRegistry.allPrincipals.forEach { user ->
    //            if (user is AppUser) {
    //                if (user.username == userName) {
    //                    sessionRegistry.getAllSessions(user, false)?.let {
    //                        it.forEach {
    //                            it.expireNow()
    //                            log.info("${userName} logout")
    //                        }
    //                    }
    //                }
    //            }
    //        }
    //        return Result.success
    //    }

    @GetMapping("/setValue/{param}/{value}")
    fun setValue(@PathVariable param: String, @PathVariable value: String): Result { //        BeanUtils.setProperty(AppPro,"bdCommander","123")

        return Result.success
    }

//    @PostMapping("executeSql")
//    fun executeSql(@RequestBody json: JSONObject): Result {
//        val sql = json.getString("sql")
//        if (sql.isEmpty()) {
//            Result.getError("参数不能为空")
//        }
//        try {
//            return Result.getSuccess(sqlMapper.queryListMap(sql))
//        } catch (e: Exception) {
//            return Result.getError(e.message!!)
//        }
//    }

    @GetMapping("/sendSms")
    fun sendSmstest() { //        sendSms("13242078026", "11")
    }

    @GetMapping("/getAppVersion")
    fun getAppVersion(): Result {
        return Result.getSuccess(sqlMapper.queryStr("select JsonValue from tbl_config where configCode='APP_VERSION'"))
    }

    @GetMapping("/getFrontValue")
    fun getFrontValue(): Result {
        var map = mutableMapOf<String, String>() //        map["websocketUrl"] = AppPro.websocketUrl
        map["tiandituUrl"] = AppPro.tiandituUrl
        map["seachartUrl"] = AppPro.seachartUrl //        map["chinaseachartUrl"] = AppPro.chinaseachartUrl
        return Result.getSuccess(map)
    }

    var parse = StationParse()

    @GetMapping("/getAisDataByStationCode/{stationCode}")
    fun getAisDataByStationCode(@PathVariable stationCode: String, request: HttpServletRequest): Result {
        val ifcleardataStr = request.getHeader("IFCLEARDATA")
        var ifcleardata = true
        if (ifcleardataStr != null && ifcleardataStr.equals("no")) {
            ifcleardata = false
        }
        var list = mutableListOf<ChModel>()
        stationCode.split(",").forEach {
            if (StationUtil.dataMap.containsKey(it)) {
                var model = StationUtil.dataMap[it]!!
                if (model.vdmList.size > 0) {
                    var chmodel = parse.dealList(model.vdmList, model.vdmDateList, it)
                    chmodel.stationId = it
                    if (ifcleardata) {
                        model.vdmList.clear()
                        model.vdmDateList.clear()
                        log.info("非测试环境清除数据!")
                    } else {
                        log.info("测试环境不进行清除数据!")
                    }
                    list.add(chmodel)
                }
            }
        }
        return Result.getSuccess(AisDataModel().apply { chModelList = list })
    } //    @GetMapping("/getAisDataByStationCode/{stationCode}")
    //    fun getAisDataByStationCode(@PathVariable stationCode: String): Result {
    //        var model = AisDataModel()
    //        model.chModelList.add(ChModel().apply {
    //            aispositionList.add(Chposition())
    //            aisvirtualatonList.add(Aisvirtualaton())
    //            aisshipList.add(Aisship())
    //        })
    //        return Result.getSuccess(model)
    //    }
}

class AisDataModel {
    var chModelList = mutableListOf<ChModel>()
} //@RestController
//@RequestMapping("/api/operator")
//class OperatorResource {
//    @GetMapping("getStatus")
//    fun getStatus(): Result = Result.success
//
//    //关闭系统
////    var password = "{\"password\":\"SHENLAN@2016\"}"
//    @GetMapping("shutdown")
//    fun shutdown() {
////        log.info("shutdown")
////        if (json.toString().equals(password)) {
//        Application.context?.close()
////        }
//    }
//}