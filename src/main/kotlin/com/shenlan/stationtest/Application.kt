package com.shenlan.stationtest

import com.shenlan.stationtest.auto.Modu
import com.shenlan.stationtest.auto.ais.OpenMinaServerManage
import com.shenlan.stationtest.util.*
import org.springframework.boot.SpringApplication
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.context.ConfigurableApplicationContext
import org.springframework.context.annotation.Configuration
import org.springframework.scheduling.annotation.EnableScheduling
import java.net.InetAddress
import java.text.SimpleDateFormat
import java.util.*
import kotlin.math.log


object DefaultProfileUtil {
    fun addDefaultProfile(app: SpringApplication) {
        val defProperties = HashMap<String, Any>()
        defProperties["spring.profiles.default"] = "dev"
        app.setDefaultProperties(defProperties)
    }
}

@SpringBootApplication
@Configuration
@EnableScheduling
class Application {
    companion object {
        var context: ConfigurableApplicationContext? = null
    }
}

fun main(args: Array<String>) {
    try {
        Sql.init()
        val app = SpringApplication(Application::class.java)
        DefaultProfileUtil.addDefaultProfile(app)


        val applicationContext = app.run("")
        Application.context = applicationContext
        val env = applicationContext.environment
        val protocol = "http"
        "".log.info("\n  ----------------------------------------------------------\n\t" +
                "Application '{}' is running! Access URLs:\n\t" +
                "Local: \t\t{}://localhost:{}\n\t" +
                "External: \t{}://{}:{}\n\t" +
                "Profile(s): \t{}\n----------------------------------------------------------",
                env.getProperty("spring.application.name"),
                protocol,
                env.getProperty("server.port"),
                protocol,
                InetAddress.getLocalHost().hostAddress,
                env.getProperty("server.port"),
                env.activeProfiles)
        init()
    } catch (e: Exception) {
        e.printStackTrace()
    }
    "".log.info(Date().toString())
   "".log.info(SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(Date()))

}

fun init() {
    initMapper()
    StationUtil.startServer()
    OpenMinaServerManage.init()
    Modu.init()
//    ThreadManager.init()
//    Dict.init()
//    devuser=null  //热重载后 搜索查不到航标数据 是因为每一个查询当成了分页查询机构只取了前15个所以查不到
//    PageInterceptor.init() 不是这个原因

}
