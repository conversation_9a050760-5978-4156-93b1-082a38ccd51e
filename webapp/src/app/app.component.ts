import { Router } from '@angular/router';
import { Component, OnDestroy } from '@angular/core';
import { ReuseTabService } from './layout/services';

@Component({
  selector: 'app-root',
  template: `<router-outlet></router-outlet>`,
})
export class AppComponent implements OnDestroy {

  constructor(private router: Router, private reuseService: ReuseTabService) { }
  ngOnDestroy(): void {
  }
  ngOnInit() {
    // this.router.navigateByUrl('/admin')
  }
}
