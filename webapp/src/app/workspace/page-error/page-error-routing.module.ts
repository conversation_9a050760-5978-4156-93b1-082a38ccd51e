import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { NotFound404Component } from './not-found404/not-found404.component';
import { SessionOutComponent } from './session-out/session-out.component';

const routes: Routes = [
  { path: '', redirectTo: '404', pathMatch: 'full' },
  { path: '401', component: SessionOutComponent, data: { reuse: false } },
  {
    path: '404',
    component: NotFound404Component,
    data: { reuse: false, title: 'Not Found' },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class PageErrorRoutingModule {}
