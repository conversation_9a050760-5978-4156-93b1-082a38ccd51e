.session-out-container {
  width: 100%;
  height: 100%;
  position: fixed;
  background: url("/assets/img/error/401.gif") no-repeat;
  background-attachment: fixed;
  background-size: cover;
  -webkit-background-size: cover;
  -o-background-size: cover;
  top: 0;
  left: 0;
  background-position-y: bottom;
  & > div {
    position: absolute;
    transform: translate(-50%, -50%);
    top: 50%;
    left: 50%;
    margin-top: -100px;
    & > b {
      font-family: SourceHanSansCN-Regular;
      font-size: 16px;
      color: #333;
      font-weight: 400;
    }
    & > a {
      color: #2f71f5;
      font-size: 16px;
      font-family: SourceHanSansCN-Medium;
      font-weight: 600;
      border: 2px solid #2f71f5;
      border-radius: 4px;
      padding: 1px 11px;
      display: inline-flex;
    }
  }
}
