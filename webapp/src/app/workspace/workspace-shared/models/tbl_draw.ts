import { BaseSearch } from "src/app/shared/models";
import { grapType } from "../../map/layer-popup-component/map-poltting/poltting-type";

export class TblDrawSearch extends BaseSearch {
	pageRecord: number = 10
	name?: string;// 标绘名称
}
export class TblDraw {
	active?: boolean; // 显示与隐藏
	editActive?: boolean;// 编辑中
	id?: string;
	drawType?: string; //0点1线2面
	groupName?: string; // 分组名称
	name?: string;// 标绘名称
	picName: string;// 图标名称
	shapeType?: grapType; // 
	rail?: number;// 半径
	color: string;// 颜色值
	drawCoordinate?: string;
	userId?: string;
	userName?: string;
	sysCreated?: string;
	sysUpdated?: string;
	sysDeleted?: number;
	constructor(color: string, picName: string) {
		this.color = color
		this.picName = picName
	}
}