import { BaseSearch } from "src/app/shared/models";

export class TblShip {
	id?: string;
	shipName?: string;  // 中文船名1
	shipNameEn?: string;  // 英文船名
	imo?: string; //1
	mmsi?: string; //1
	callsign?: string;  // 船舶呼号 //1
	shipPortCode?: string;
	shipFlagCode?: string;
	markCode?: string;
	shipTypeCode?: string;  //  货物/船舶类型代码
	grossTonnage?: number;
	netTonnage?: number;
	deadWeight?: number;
	enginePower?: number;
	ratedSeat?: number;
	overallLength?: number; // 船舶总长  米
	mouldedBreadth?: number; // 船舶总宽  米
	shipLength?: any;
	shipWidth?: any;
	rateOfTurn?: any;
	mouldedDepth?: number;
	shipLineCode?: string;
	shipAreaCode?: string;
	buildDate?: string;
	owner?: string;
	ownerAddress?: string;
	ownerTelephone?: string;
	ownerCorporation?: string;
	operator?: string;
	operatorAddress?: string;
	operatorTelephone?: string;
	operatorCorporation?: string;
	eta?: string; // 详细预计到达时间 
	reportTime?: string; // 详细更新时间
	draught?: number; // 最大静态吃水  米
	destination?: string; //目的地 0 ？
	sysUpdated?: string; // 更新时间  ？
	dataSource?: number;
	sysCreated?: string;
	sysDeleted?: number;

	heading?: number; // 船道向   度

	reteOfTurn?: number; // 转向率  度/分

	shipTypeName?: string; // 类型 1
	navigationStatusCode?: string; // 航行状态字段 1
	navigationStatusName?: string; // 航行状态文字介绍 1
	latitude?: any; //维度
	longitude?: any; // 经度
	// shipLength?:string; //船长
	// shipWidth?:string; //船宽
	positionAccuracy?: number; //位置准确度 1高 0低 默认0
	courseOverGround?: number;// 对地航向  度
	speedOverGround?: number; //对地航速  节

	// 船迹向 ？
	chuanjixiang?: string;

	// 白名单  
	// 报警记录
	alarmRecordCount?: number;
	// 事故记录
	accidentRecordCount?: number;
	remark?: string; // 备注
	groupName?: string; // 分组名称
	markType?: string; //船舶类型分别  空普通 1报警2白名单3事故

	shipIcon?: string; // 船舶图标
	shipMarkIcon?: string; // 船舶类型图标  白名单图标
	shipMarkIconAlarm?: string; // 报警图标
	markship?: MARKSHIP; // 传递的白名单数据
	latlng?: L.LatLng;

}
export class TblShipSearch extends BaseSearch {
	// navigationStatusCodeList?:string; // 船舶类型
	shipLengthMin?: string; //船舶长度
	shipLengthMax?: string;
	shipBreadthMax?: string
	shipBreadthMin?: string
	shipSpeedMin?: string;
	shipSpeedMax?: string;
	// easyTypeCodeList?:string // 船舶状态
	pageRecord: number = 10  // 当前页最多显示十条
	keyword?: string; // 船舶名称
	easyTypeCodeList?: Array<any>; // 船舶类型  多选
	navigationStatusCodeList?: Array<any> // 船舶状态 多选
	markType?: string; // 空普通全部 1报警2白名单3事故
}

//   筛选后
export class TblShipSearch2 extends BaseSearch {
	// navigationStatusCodeList?:string; // 船舶类型
	shipLengthMin?: string; //船舶长度
	shipLengthMax?: string;
	shipWidthdMin?: string
	shipWidthMin?: string
	shipSpeedMin?: string;
	shipSpeedMax?: string;
	// easyTypeCodeList?:string // 船舶状态
	pageRecord: number = 5  //  筛选 后 当前页最多显示五条
	keyword?: string; // 船舶名称
	easyTypeCodeList?: Array<any>; // 船舶类型  多选
	navigationStatusCodeList?: Array<any> // 船舶状态 多选
}

// 白名单数据类型
interface MARKSHIP {
	areaIds?: string;  // 关联保护区  可多选 
	enableType?: string; // 生效类型  永久生效0  自定义 1 
	endTime?: string; // 自定义生效时间
	startTime?: string;
	id?: string; // 船舶id
	markType?: string; // 船舶类型分别  空普通 1报警2白名单3事故
	mmsi?: string;
	shipTypeCode?: string; // 货物/船舶类型代码  参照 windpower-ship-data.ts
	shipName?: string; // 船舶名称

	groupName?: string; // 分组名称
	remark?: string; // 备注
}