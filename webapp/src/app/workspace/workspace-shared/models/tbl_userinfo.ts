import { BaseSearch } from 'src/app/shared/models';

export class TblUserinfo {
  id?: string;
  username?: string; // 用户名称
  loginName?: string; // 登录名
  proAreaIds?: string;
  unitName?: string; // 所属单位
  deptName?: string; //所属部门
  password?: string; //密码
  email?: string; // 邮箱
  mobilePhone?: string; // 手机号码
  ifEnabled?: number; // 状态 0启用 1禁用
  remark?: string; //备注
  sysCreated?: string;
  sysUpdated?: string;
  sysDeleted?: number;

  rolenames?: string; // 暂加 用户角色
  roleIds?: string; // 用户角色 id
  parendId?: string
  unitId?: string; // 新增单位id
  deptId?: string; // 新增部门id
}

export class UserSearch extends BaseSearch { }
