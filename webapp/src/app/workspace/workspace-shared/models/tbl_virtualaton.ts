export class TblVirtualaton { 
	id?: string;
	stationId?: string;
	stationName?: string;
	aidsName?: string;
	aidsNameEn?: string;
	mmsi?: string;
	typeCode?: string;
	typeName?: string;
	targetingCode?: string;
	targetingName?: string;
	ts?: number;
	attribute?: string;
	latitude?: number;
	longitude?: number;
	position?: string;
	sizeA?: number;
	sizeB?: number;
	sizeC?: number;
	sizeD?: number;
	config?: string;
	split?: number;
	utcA?: number;
	slotTimeA?: number;
	slotTimeAddA?: number;
	utcB?: number;
	slotTimeB?: number;
	slotTimeAddB?: number;
	userId?: string;
	userName?: string;
	ifEnable?: string;
	areaId?: string;
	areaName?: string;
	sysCreated?: string;
	sysUpdated?: string;
	sysDeleted?: number;
}