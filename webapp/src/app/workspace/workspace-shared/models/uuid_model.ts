export class UUIDModel {
    uuid?: string;
    id?: string;
    latD?: number;
    latM?: number;
    latS?: number;
    lngD?: number;
    lngM?: number;
    lngS?: number;
    name?: string;
    // 电子围栏、海底电缆 根据名称来判断，相同名称使用同一个blockId
    // 航标、风机 每条数据都使用同一个 blockId
    blockId?: string;
    latitude?: number;
    longitude?: number;
    latlngStr?: string; // 详情里面展示使用
    serialNumber?: number
    // [key: string]: any;
}