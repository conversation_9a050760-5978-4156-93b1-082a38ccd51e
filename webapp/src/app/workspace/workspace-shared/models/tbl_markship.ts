// 标记船舶
export class TblMarkship { 
	id?: string;
	mmsi?: string;
	shipName?: string;  // 船舶名称
	markType?: string; // 标记类型  船舶类型 
	areaIds?: string;
	areaNames?: string; 
	groupName?: string;  // 分组名称
	remark?: string;  // 备注
	enableType?: string; // 生效类型  0 永久 1 自定义
	startTime?: string;
	endTime?: string;
	sysCreated?: string;
	sysUpdated?: string;
	sysDeleted?: number;

	shipTypeCode?:string; // 船类型  0-8 货船..客船..其他
}
// 轨迹查询
export class TblMarkHistory{
	endTime?:string; //结束时间  
	startTime?:string; //开始时间 
	mmsi?:string; 
}