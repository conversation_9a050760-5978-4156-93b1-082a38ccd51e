import { UUIDModel } from "./uuid_model";

/**
 *
 * 航标
 * @export
 * @class TblNavigation
 */
export class TblNavigation extends UUIDModel {
	id?: string;
	name?: string; // 航标名称
	areaId?: string;// 保护区主键
	crawlBlockId?: string;// 电子围栏blockId
	navigationId?: string;// 航标区域编号
	navigationType?: string; // 航标类别
	navigationName?: string; // 航标类别
	latitude?: number; // 纬度
	longitude?: number; // 经度
	colorCode?: string; // 闪光颜色
	colorName?: string; // 闪光颜色
	lightRhythmCode?: string; // 闪光节奏code
	lightRhythmName?: string;// 闪光节奏名称
	lightCycle?: string;// 闪光周期
	lightHigh?: number; // 灯高
	lightRange?: string; // 射程
	lightStructure?: string; // 构造
	remark?: string; // 备注
	userId?: string;
	userName?: string;
	sysCreated?: string;
	sysUpdated?: string;
	sysDeleted?: number;
	statuslist?: TblNavigationStatus
	mmsi?: string;// 虚拟航标mmsi（必填）
	aisNameEn?: string;// 虚拟航标英文名称（必填）
}

export class TblNavigationStatus {
	alarmContent?: string
	alarmParam?: string
	alarmStartTime?: string
	atonId?: string
	batteryTemperature?: number
	batteryVoltage?: number
	cardNumber?: string
	chargingCurrent?: number
	chargingVoltage?: number
	collectionTime?: string
	colorCode?: string
	colorName?: string
	commModeCode?: string
	commModeName?: string
	commStatus?: string
	commStatusName?: string
	coordinates?: string
	datagramId?: string
	daylightValue?: number
	deviceCode?: string
	id?: string
	ifOnline?: number
	ifStandby?: number
	illumination?: string
	illuminationName?: string
	latitude?: number
	ledStatus?: string
	lightQualityCode?: number
	lightQualityName?: string
	lightRange?: number
	lightUp?: string
	lightUpName?: string
	lightUps?: string
	lightWorkCurrent?: number
	lightWorkVoltage?: number
	lightTemperature?: number
	lightHumidity?: number
	longitude?: number
	operatingAttr?: string
	operatingAttrName?: string
	remainingBattery?: number
	selectiveSwitch?: string
	selectiveSwitchName?: string
	shiftDirection?: number
	shiftDistance?: number
	signalStrength?: string
	strengthGrade?: string
	strengthGradeName?: string
	targeting?: string
	targetingName?: string
	workLight?: string
	workLightName?: string
	workModel?: string
	workModelName?: string
}