import { BaseSearch } from 'src/app/shared/models';
import { TblCable } from "./tbl_cable";
import { TblCrawl } from "./tbl_crawl";
import { TblFan } from "./tbl_fan";
import { TblNavigation } from "./tbl_navigation";
import { UUIDModel } from "./uuid_model";
export interface CableWarmLatlng {
	x: number // lng
	y: number // lat 
	z: string
}
export interface CableWarm {
	name: string
	cableWarmList: Array<CableWarmLatlng>
}

export class TblProarea extends UUIDModel {
	id?: string;
	areaName?: string; // 保护区名称
	cableDistance?: number; // 海底电缆保护区范围
	fanDistance?: number; // 风机保护范围
	crawlList?: Array<TblCrawl> = []; // 电子围栏
	cableList?: Array<TblCable> = []; // 海底电缆
	navigationList?: Array<TblNavigation> = [];// 航标
	cableWarmLists?: Array<CableWarm> = []; // 海底电缆外围（后端生成）
	fanList?: Array<TblFan> = [];// 风机
	userId?: string;
	recordTime?: string;// 记录时间
	userName?: string;
	sysCreated?: string;
	sysUpdated?: string;
	sysDeleted?: number;
}

export class TblProareaSearch extends BaseSearch {
	ifPage?: boolean = false;
	id?: string;     //当searchLevel为1时，需要传保护区主键，其余情况为null
	blockId?: string; //区域主键（电子围栏区域；海底电缆区域；航标区域；风机区域）
	searchLevel?: string = '1';  //1:保护区；2:电子围栏（2-1）；3:海底电缆（3-1）；4:航标（4-1）；5:风机（5-1）
	keyword?: string;
	areaType?: string = '0';   //0:全部；1:电子围栏；2:海底电缆；3:航标；4:风机
	areaId?: string;     //当searchLevel为1时为null，其余情况需要传保护区主键
}