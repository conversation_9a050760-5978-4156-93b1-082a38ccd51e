export class TblAlarmsetting {
	id?: string;
	noticeType?: number;//通知设置类型 //0:系统,1:一级报警,2:二级报警,3:三级报警
	sysAlarmType?: string;//系统报警类型
	ifNotice?: string; //是否通知 0:否,1:是 // 系统报警中 是否通知运维人员
	ifSms?: string; //是否短信通知 0:否,1:是
	smsGroup?: string; // 短信通知组 
	ifVoice?: string; // 是否语音通知 0:否,1:是
	voiceGroup?: string; // 语音通知组
	ifEmail?: string;//是否邮箱通知 0:否,1:是
	emailGroup?: string;//邮箱通知组
	userId?: string;
	userName?: string;
	sysCreated?: string;
	sysUpdated?: string;
	sysDeleted?: number;


	// 页面使用
	isSMSChecked?: boolean;
	isVoiceChecked?: boolean;
	isEmailChecked?: boolean;

	constructor(sys?: TblAlarmsetting) {
		if (sys) {
			this.id = sys.id
			this.ifEmail = sys.ifEmail
			this.ifNotice = sys.ifNotice
			this.ifSms = sys.ifSms
			this.voiceGroup = sys.voiceGroup
			this.emailGroup = sys.emailGroup
			this.ifVoice = sys.ifVoice
			this.noticeType = sys.noticeType
			this.smsGroup = sys.smsGroup
			this.sysAlarmType = sys.sysAlarmType
		}
	}

}