import { extend } from "leaflet";
import { BaseSearch } from "src/app/shared/models";

export class TblAlarmrecord {
	id?: string;
	mmsi?: string;
	shipId?: string;
	shipName?: string;
	shipTypeCode?: string;
	areaId?: string;
	areaName?: string;
	alarmSettingId?: string;
	alarmType?: string;
	alarmTypeText?: string; // 前端定义
	alarmContent?: string;
	alarmTime?: string;
	alarmStatus?: string;
	alarmStatusText?: string;// 前端定义
	color?: string;// 前端定义
	shipIcon?: string;// 前端定义
	removeTime?: string;
	sysCreated?: string;
	sysUpdated?: string;
	sysDeleted?: number;
}

export class TblAlarmrecordSearch extends BaseSearch {
	keyword?: string //mmsi或船名
	areaName?: string  //保护区名称
	alarmType?: string[]//1:一级报警;2:二级报警;3:三级报警
	alarmStatus?: string//0报警中1已解除
	startTime?: string  //起始时间 YYYY-MM-DD HH:mm:ss
	endTime?: string   //截止时间 YYYY-MM-DD HH:mm:ss
}