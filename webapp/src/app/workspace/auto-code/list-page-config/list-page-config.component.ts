import { Component, OnInit } from '@angular/core';
import { ListTableItem } from 'src/app/shared/models';
import { FormTabelItems } from '../form-template/form-mock';
import { ListTabelItems } from '../list-template/list-mock';
import { ListPageItem } from './list-page-item';

@Component({
  selector: 'app-list-page-config',
  templateUrl: './list-page-config.component.html',
  styleUrls: ['./list-page-config.component.less']
})
export class ListPageConfigComponent implements OnInit {
  form: ListPageItem
  switchValue: boolean = true
  isSave: boolean = false
  listTableItems: Array<ListTableItem> = []
  constructor() {
    this.form = { title: '报警记录', listTableItems: ListTabelItems, serachTableItems: FormTabelItems }
    this.save()
  }

  ngOnInit(): void {
  }


  reset() {
    this.isSave = false
  }

  save() {
    console.log(this.form)
    if (this.form && this.form.listTableItems && this.form.listTableItems.length) {
      this.isSave = true
      this.listTableItems = this.form.listTableItems.map(ele => {
        const item: ListTableItem = {}
        item.property = ele.property
        item.title = ele.label
        item.type = 'text'
        switch (ele.type) {
          case 'serial':
            item.type = 'serial'
            break;
          case 'checkbox':
            item.type = 'checkbox'
            break;
          case 'btn':
            item.type = 'btns'
            break;
          case 'link':
            item.type = 'link'
            break;
          case 'static':
            item.type = 'text'
            break;
        }
        return item
      })
      console.log(this.listTableItems);

    }
  }
}
