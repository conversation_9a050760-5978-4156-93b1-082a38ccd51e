<form #validateForm="ngForm">
    <div class="list-page-container">
        <div class="base-config">
            <h2>基础配置</h2>
            <div class="form-item">
                <label for="">页面标题：</label>
                <input style="width:200px" type="text" name="title" nz-input [(ngModel)]="form.title" />
            </div>
            <div class="form-item">
                <label for="">搜索表单：</label>
                <nz-switch name="switchValue" [(ngModel)]="switchValue"></nz-switch>
            </div>
        </div>
        <app-form-template *ngIf="switchValue" title="搜索tabel配置" [handleTableItemList]="form.serachTableItems!">
        </app-form-template>
        <app-list-template title="列表table配置" [listTableItemList]="form.listTableItems!"></app-list-template>
        <div class="handle-area">
            <button sl-button slType="danger" (click)="reset()">重置</button>
            <button sl-button slType="success" (click)="save()">保存</button>
        </div>
    </div>
    <div class="list-page-prew" *ngIf="isSave">
        <sl-list-table [listTableItems]="listTableItems"></sl-list-table>
    </div>
</form>