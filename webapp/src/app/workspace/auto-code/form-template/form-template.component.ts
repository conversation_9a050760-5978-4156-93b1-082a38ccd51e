import { Component, Input, OnInit } from '@angular/core';
import { uuid } from 'src/app/shared/utils';
import { FormTabelItems } from './form-mock';
import { FormTemplateItem } from './form-template-item';

@Component({
  selector: 'app-form-template',
  templateUrl: './form-template.component.html',
  styleUrls: ['./form-template.component.less']
})
export class FormTemplateComponent implements OnInit {
  @Input() title: string = 'form 表单配置'
  @Input() handleTableItemList: Array<Array<FormTemplateItem>> = [[{ uuid: uuid(), property: '', type: 'input', label: '' }]]

  constructor() { }

  ngOnInit(): void {
  }

  private mockTest() {
    this.handleTableItemList = FormTabelItems
  }
  trackByItem(index: number, item: any) {
    return item.uuid
  }
  addColumn(row: number, column: number) {
    this.handleTableItemList[row].push({ uuid: uuid(), property: '', type: 'input', label: '' })
  }
  addRow() {
    const subItem: FormTemplateItem = { uuid: uuid(), property: '', type: 'input', label: '' }
    const item = [subItem]
    this.handleTableItemList.push(item)
  }
  delColum(row: number, column: number) {
    this.handleTableItemList[row].splice(column, 1)
  }
  delRow(row: number) {
    this.handleTableItemList.splice(row, 1)
  }
}
