import { FormTemplateItem } from './form-template-item';
import { uuid } from 'src/app/shared/utils';
export const FormTabelItems: Array<Array<FormTemplateItem>> =
    [
        [{ uuid: uuid(), type: 'input', label: '船舶名称', property: 'shipName', labelWidth: 120, controlWidth: 200 },
        { uuid: uuid(), type: 'select', label: '关联保护区', property: 'areaId', labelWidth: 120, controlWidth: 200 },
        { uuid: uuid(), type: 'select', label: '报警类型', property: 'alarmType', labelWidth: 120, controlWidth: 200 },
        { uuid: uuid(), type: 'date-picker', label: '报警时间', property: 'alarmTime', labelWidth: 120, controlWidth: 200 }]
    ]