.handle-table-config {
  .handle-title {
    background: #2d74ef;
    display: flex;
    align-items: center;
    font-size: 16px;
    color: #fff;
    height: 32px;
    padding: 4px 12px;
  }
  .handle-body {
    padding: 0 10px 10px 10px;
    border-left: 1px solid #ddd;
    border-right: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
    margin-bottom: 10px;
    table {
      width: 100%;
      tr {
        border-top: 1px solid #ddd;
        td {
          padding: 4px 6px;
          border-left: 1px solid #ddd;
          &:last-child {
            border-right: 1px solid #ddd;
          }
          &.serial {
            width: 50px;
            text-align: center;
          }
          &.type {
            width: 200px;
          }
          &.handle {
            width: 300px;
            button + button {
              margin-left: 12px;
            }
            text-align: center;
          }
        }
        &.title {
          td {
            text-align: center;
            background: #eef4fe;
          }
        }
        &.handle {
          border-top: none;
          td {
            padding: 8px 12px;
            border-left: none;
            border-right: none;
          }
        }
        &:last-child {
          border-bottom: 1px solid #ddd;
        }
      }
    }
  }
}
