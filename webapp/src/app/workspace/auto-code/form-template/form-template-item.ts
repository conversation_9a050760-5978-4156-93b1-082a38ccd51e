import { uuid } from 'src/app/shared/utils/uuid';
export type formItemType = 'select' | 'textarea' | 'radio' | 'checkbox' | 'input' | 'date' | 'date-picker'
export class FormTemplateItem {
    type: formItemType = 'input'
    label: string = '标签名'
    property: string;
    labelWidth?: number = 120
    controlWidth?: number = 200
    uuid: string = uuid()
    constructor(property: string, label: string = '标签名') {
        this.label = label
        this.property = property
        this.labelWidth = 200
    }
}