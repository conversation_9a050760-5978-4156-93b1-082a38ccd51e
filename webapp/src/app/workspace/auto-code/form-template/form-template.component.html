<form #validateForm="ngForm">
    <div class="handle-table-config">
        <div class="handle-title">{{title}}</div>
        <div class="handle-body">
            <table>
                <tr class="handle">
                    <td colspan="9">
                        <button sl-button slType="primary" (click)="addRow()">新增行</button>
                    </td>
                </tr>
                <tr class="title">
                    <td class="serial">行</td>
                    <td class="serial">列</td>
                    <td>label名称</td>
                    <td class="type">control件类型</td>
                    <td>字段名</td>
                    <td>label宽度</td>
                    <td>control宽度</td>
                    <td class="handle">列操作</td>
                    <td class="handle">行操作</td>
                </tr>
                <ng-container *ngFor="let item of handleTableItemList;index as i;">
                    <tr class="item" *ngFor="let subItem of item;trackBy:trackByItem;index as j;">
                        <td class="serial" [attr.rowspan]="item.length" *ngIf="j==0">{{i+1}}</td>
                        <td class="serial">{{j+1}}</td>
                        <td>
                            <input type="text" nz-input [ngModelOptions]="{standalone:true}" [(ngModel)]="subItem.label"
                                placeholder="请输入label名称" />
                        </td>
                        <td class="type">
                            <nz-select nzPlaceHolder="请选择控件类型" [ngModelOptions]="{standalone:true}" style="width: 100%;"
                                [(ngModel)]="subItem.type">
                                <nz-option nzValue="input" nzLabel="Input"></nz-option>
                                <nz-option nzValue="select" nzLabel="Select"></nz-option>
                                <nz-option nzValue="date-picker" nzLabel="DatePicker"></nz-option>
                            </nz-select>
                        </td>
                        <td>
                            <input [ngModelOptions]="{standalone:true}" type="text" nz-input
                                [(ngModel)]="subItem.property" placeholder="请输入属性名" />
                        </td>
                        <td>
                            <nz-input-number [nzMin]="90" [ngModelOptions]="{standalone:true}" type="text" nz-input
                                [(ngModel)]="subItem.labelWidth" placeholder="请输入标签长度"></nz-input-number>
                        </td>
                        <td>
                            <nz-input-number [nzMin]="50" [ngModelOptions]="{standalone:true}" type="text" nz-input
                                [(ngModel)]="subItem.controlWidth" placeholder="请输入控件长度"></nz-input-number>
                        </td>
                        <td class="handle">
                            <button sl-button slType="danger" (click)="delColum(i,j)">删除列</button>
                            <button sl-button slType="primary" (click)="addColumn(i,j)">新增列</button>
                        </td>
                        <td class="handle" [attr.rowspan]="item.length" *ngIf="j==0">
                            <button sl-button slType="primary" (click)="delRow(i)">删除行</button>
                        </td>
                    </tr>
                </ng-container>
                <tr *ngIf="handleTableItemList.length==0">
                    <td colspan="9">
                        <nz-empty></nz-empty>
                    </td>
                </tr>
            </table>
        </div>

    </div>
</form>