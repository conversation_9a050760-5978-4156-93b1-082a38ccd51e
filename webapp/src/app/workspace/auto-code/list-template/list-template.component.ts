import { Component, OnInit, Input } from '@angular/core';
import { ListTemplateItem, listColumnType } from './list-template-item';
import { ListTabelItems } from './list-mock';

@Component({
  selector: 'app-list-template',
  templateUrl: './list-template.component.html',
  styleUrls: ['./list-template.component.less']
})
export class ListTemplateComponent implements OnInit {

  @Input() title: string = '列表table配置'
  @Input() listTableItemList: Array<any> = [new ListTemplateItem('序号', 'serial', 50)]

  get isSerialDisabled() {
    return this.listTableItemList.some(ele => ele.type === 'serial')
  }

  get listLen() {
    return this.listTableItemList.length
  }

  typeList: Array<{ text: string, value: listColumnType }> = [
    { text: '文本', value: 'text' },
    { text: 'a标签超链接', value: 'link' },
    { text: 'button 按钮', value: 'btn' },
    { text: 'Checkbox复选框', value: 'checkbox' },
  ]

  constructor() { }

  ngOnInit(): void {
    // this.mockTest()
  }


  private mockTest() {
    this.listTableItemList = ListTabelItems
  }

  trackByItem(index: number, item: any) {
    return item.uuid
  }
  addColumn() {
    this.listTableItemList.push(new ListTemplateItem(''))
  }

  delColumn(index: number) {
    this.listTableItemList.splice(index, 1)
  }

  moveToNext(index: number) {
    if (this.listLen - 1 > index) {
      this.swapIndex(index, index + 1)
    }
  }

  moveToPre(index: number) {
    if (index > 0) {
      this.swapIndex(index, index - 1)
    }
  }

  /**
   * 交换位置
   * @private
   * @param {number} index
   * @param {number} swapIndex
   * @memberof ListTemplateComponent
   */
  private swapIndex(index: number, swapIndex: number) {
    const item = this.listTableItemList[index]
    const swapItem = this.listTableItemList[swapIndex]
    this.listTableItemList[index] = swapItem
    this.listTableItemList[swapIndex] = item
  }
}
