import { uuid } from 'src/app/shared/utils';
import { ListTemplateItem } from './list-template-item';
export const ListTabelItems: Array<ListTemplateItem> = [
    { uuid: uuid(), type: 'serial', label: '序号', columnWidth: 50 },
    { uuid: uuid(), type: 'link', label: '船舶名称', columnWidth: 310, property: 'shipName' },
    { uuid: uuid(), type: 'text', label: 'MMSI', columnWidth: 200, property: 'mmsi' },
    { uuid: uuid(), type: 'text', label: '关联保护区', columnWidth: 230, property: 'areaName' },
    { uuid: uuid(), type: 'text', label: '报警类型', columnWidth: 150, property: 'alarmType' },
    { uuid: uuid(), type: 'text', label: '报警类容', columnWidth: 360, property: 'alarmContent' },
    { uuid: uuid(), type: 'text', label: '报警时间', columnWidth: 200, property: 'alarmTime' },
    { uuid: uuid(), type: 'text', label: '报警状态', columnWidth: 120, property: 'alarmStatus' },
    { uuid: uuid(), type: 'text', label: '解除时间', columnWidth: 200, property: 'liftTime' },

]