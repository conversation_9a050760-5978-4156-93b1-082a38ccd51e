import { uuid } from 'src/app/shared/utils/uuid';
export type listColumnType = 'serial' | 'checkbox' | 'text' | 'link' | 'btn' | 'static'
export class ListTemplateItem {
    type: listColumnType = 'text';
    uuid: string;
    columnWidth: number;
    label: string;
    property?: string;
    clickCb?: (data?: any) => void;
    callCb?: (data?: any) => any;
    hoverCb?: (data?: any) => void;
    iconName?: string;
    constructor(label: string, type: listColumnType = 'text', columnWidth: number = 0, property?: string) {
        this.type = type
        this.label = label
        this.uuid = uuid()
        this.columnWidth = columnWidth
        if (property) this.property = property
    }
}