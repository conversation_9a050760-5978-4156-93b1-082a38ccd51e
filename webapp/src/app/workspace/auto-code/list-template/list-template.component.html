<form #validateForm="ngForm">
    <div class="list-table-config">
        <div class="handle-title">{{title}}</div>
        <div class="handle-body">
            <table>
                <tr class="handle">
                    <td colspan="5">
                        <button sl-button slType="primary" (click)="addColumn()">新增列</button>
                    </td>
                </tr>
                <tr class="title">
                    <td class="serial">序号</td>
                    <td class="type">类型</td>
                    <td class="label">label标签</td>
                    <td>字段名</td>
                    <td>列宽度</td>
                    <td class="handle">操作</td>
                </tr>
                <tr class="item" *ngFor="let item of listTableItemList;index as i;trackBy:trackByItem">
                    <td class="serial">{{i+1}}</td>
                    <td class="type">
                        <nz-select [ngModelOptions]="{standalone:true}" style="width: 100%;" [(ngModel)]="item.type">
                            <nz-option *ngFor="let item of typeList;" [nzValue]="item.value" [nzLabel]="item.text">
                            </nz-option>
                            <nz-option nzValue="serial" nzLabel="序号" [nzDisabled]="isSerialDisabled"></nz-option>
                        </nz-select>
                    </td>
                    <td>
                        <input [ngModelOptions]="{standalone:true}" type="text" nz-input [(ngModel)]="item.label"
                            placeholder="label标签" />
                    </td>
                    <td>
                        <input [disabled]="item.type==='serial'" [ngModelOptions]="{standalone:true}" type="text"
                            nz-input [(ngModel)]="item.property" placeholder="请输入属性名" />
                    </td>

                    <td>
                        <nz-input-number [nzMin]="0" [ngModelOptions]="{standalone:true}" type="text" nz-input
                            [(ngModel)]="item.columnWidth" placeholder="请输入列长度"></nz-input-number>
                    </td>
                    <td class="handle">
                        <button sl-button slType="danger" (click)="delColumn(i)">删除</button>
                        <button sl-button slType="primary" (click)="moveToNext(i)">下移</button>
                        <button sl-button slType="primary" (click)="moveToPre(i)">上移</button>
                    </td>
                </tr>
                <tr *ngIf="listTableItemList.length==0">
                    <td colspan="6">
                        <nz-empty></nz-empty>
                    </td>
                </tr>
            </table>
            <div class="tip" style="margin-top:10px;">
                <p style="font-size:12px;color:red;">1、默认序号只能只有一列,且没有字段属性</p>
                <p style="font-size:12px;color:red;">2、“列宽度”如果设置为0，代表不固定列宽，会自适应宽度</p>
            </div>
        </div>

    </div>
</form>