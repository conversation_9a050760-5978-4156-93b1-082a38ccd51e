import { NzEmptyModule } from 'ng-zorro-antd/empty';
import { SlButtonModule } from './../../shared/modules/sl-button/sl-button.module';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { FormsModule } from '@angular/forms';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number'
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzSwitchModule } from 'ng-zorro-antd/switch';

import { AutoCodeRoutingModule } from './auto-code-routing.module';
import { ListTemplateComponent } from './list-template/list-template.component';
import { FormTemplateComponent } from './form-template/form-template.component';
import { ListPageConfigComponent } from './list-page-config/list-page-config.component';
import { SlTableModule } from 'src/app/shared/modules/sl-table/sl-table.module';


@NgModule({
  declarations: [
    ListTemplateComponent,
    FormTemplateComponent,
    ListPageConfigComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    NzInputModule,
    NzSelectModule,
    SlButtonModule,
    NzEmptyModule,
    AutoCodeRoutingModule,
    NzInputNumberModule,
    NzSwitchModule,
    SlTableModule
  ]
})
export class AutoCodeModule { }
