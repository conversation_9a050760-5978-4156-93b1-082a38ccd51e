import { SlButtonModule } from './../../shared/modules/sl-button/sl-button.module';
import { SlCheckBoxModule } from './../../shared/modules/sl-checkbox/sl-checkbox.module';
import { SlModalModule } from './../../shared/modules/sl-modal/sl-modal.module';
import { FormsModule } from '@angular/forms';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { DemoRoutingModule } from './demo-routing.module';
import { SlCheckboxUsageComponent } from './components/sl-checkbox-usage/sl-checkbox-usage.component';
import { DemoComponent } from './demo.component';
import { SlModalUsageComponent } from './components/sl-modal-usage/sl-modal-usage.component';
import { SlButtonUsageComponent } from './components/sl-button-usage/sl-button-usage.component';


@NgModule({
  declarations: [
    SlCheckboxUsageComponent,
    DemoComponent,
    SlModalUsageComponent,
    SlButtonUsageComponent
  ],
  imports: [
    CommonModule,
    DemoRoutingModule,
    FormsModule,
    SlModalModule,
    SlCheckBoxModule,
    SlButtonModule
  ]
})
export class DemoModule { }
