import { SlButtonUsageComponent } from './components/sl-button-usage/sl-button-usage.component';
import { SlModalUsageComponent } from './components/sl-modal-usage/sl-modal-usage.component';
import { DemoComponent } from './demo.component';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { SlCheckboxUsageComponent } from './components';

const routes: Routes = [
  { path: '', component: DemoComponent, data: { title: 'Demo' } },
  { path: 'button', component: SlButtonUsageComponent, data: { title: 'Button 按钮' } },
  { path: 'checkbox', component: SlCheckboxUsageComponent, data: { title: 'Checkbox 复选框' } },
  { path: 'modal', component: SlModalUsageComponent, data: { title: 'Modal 模态框' } }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class DemoRoutingModule { }
