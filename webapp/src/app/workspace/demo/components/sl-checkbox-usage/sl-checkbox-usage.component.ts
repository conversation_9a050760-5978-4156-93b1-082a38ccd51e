import { Component, OnInit } from '@angular/core';
import { SlCheckboxItem } from 'src/app/shared/modules/sl-checkbox';

@Component({
  selector: 'app-sl-checkbox-usage',
  templateUrl: './sl-checkbox-usage.component.html',
  styleUrls: ['./sl-checkbox-usage.component.less']
})
export class SlCheckboxUsageComponent implements OnInit {

  checkboxOptions: Array<SlCheckboxItem> = [
    { text: 'Apple', value: '0', checked: false },
    { text: 'Orange', value: '1', checked: false },
    { text: 'Pear', value: '2', checked: false },
    { text: 'Peach', value: '3', checked: false },
    { text: 'Strawberry', value: '4', checked: false },
    { text: 'Grape', value: '5', checked: false },
  ]

  constructor() { }

  ngOnInit(): void {

  }



}
