<div class="container">
    <div class="item">
        <h2 class="title">按钮状态</h2>
        <div class="body">
            <button sl-button slType="default">Default</button>
            <button sl-button>Default2</button>
            <button sl-button slType="primary">Primary</button>
            <button sl-button slType="danger">Danger</button>
            <button sl-button slType="success">Success</button>
            <button sl-button slType="success" slDisabled>Disabled</button>
        </div>
    </div>
    <div class="item">
        <h2 class="title">按钮大小</h2>
        <div class="description">
            <p>默认按钮高度为28px</p>
            <p>按钮最小宽度为84px</p>
        </div>
        <div class="body">
            <button sl-button slType="primary" slSize="lg">大号尺寸</button>
            <button sl-button slType="primary">默认尺寸</button>
            <button sl-button slType="primary" slSize="sm">小号尺寸</button>
        </div>
    </div>
</div>