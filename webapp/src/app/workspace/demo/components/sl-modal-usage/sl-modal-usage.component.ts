import { SlModalService } from './../../../../shared/modules/sl-modal/sl-modal.service';
import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-sl-modal-usage',
  templateUrl: './sl-modal-usage.component.html',
  styleUrls: ['./sl-modal-usage.component.less']
})
export class SlModalUsageComponent implements OnInit {

  constructor(private slModalService: SlModalService) { }

  ngOnInit(): void {
  }


  confirmTest() {
    this.slModalService.openPromptModal({
      type: 'confirm',
      content: '确认保存吗？',
      okCb: () => {
        console.log('保存回调');
      },
      cancelCb: () => {
        console.log('取消回调');
      }
    })
  }


  successTest() {
    this.slModalService.openPromptModal({
      type: 'success',
      content: '保存成功',
      okCb: () => {
        console.log('确定按钮回调');
      }
    })
  }

  errorTest() {
    this.slModalService.openPromptModal({
      type: 'error',
      content: '保存失败',
      okCb: () => {
        console.log('确定按钮回调');
      }
    })
  }
}
