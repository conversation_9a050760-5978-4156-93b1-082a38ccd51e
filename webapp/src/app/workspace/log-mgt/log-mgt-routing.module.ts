import { NoticeLogComponent } from './notice-log/notice-log.component';
import { OperationLogComponent } from './operation-log/operation-log.component';
import { UserLogComponent } from './user-log/user-log.component';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  { path: 'user', component: UserLogComponent, data: { title: '用户日志' } },
  { path: 'operation', component: OperationLogComponent, data: { title: '操作日志' } },
  { path: 'notice', component: NoticeLogComponent, data: { title: '通知日志' } },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class LogMgtRoutingModule { }
