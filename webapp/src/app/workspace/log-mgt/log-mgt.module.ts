import { SharedModule } from './../../shared/shared.module';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { LogMgtRoutingModule } from './log-mgt-routing.module';
import { UserLogComponent } from './user-log/user-log.component';
import { OperationLogComponent } from './operation-log/operation-log.component';
import { NoticeLogComponent } from './notice-log/notice-log.component';
@NgModule({
  declarations: [UserLogComponent, OperationLogComponent, NoticeLogComponent],
  imports: [CommonModule, LogMgtRoutingModule, SharedModule],
})
export class LogMgtModule {}
