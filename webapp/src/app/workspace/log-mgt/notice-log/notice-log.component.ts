import { Component, OnInit } from '@angular/core';
import { BListPageModel } from 'src/app/shared/models';

@Component({
  selector: 'app-notice-log',
  templateUrl: './notice-log.component.html',
  styleUrls: ['./notice-log.component.less']
})
export class NoticeLogComponent implements OnInit {
  bListPageModel: BListPageModel = {
    title: '通知日志',
    requestUrl: '/api/Notice/getList',
    searchTableItems: [
      [
        {
          label: '报警类型', property: 'alarmType', type: 'select', selectOptions: [
            { text: '系统报警', value: '0' },
            { text: '一级报警', value: '1' },
            { text: '二级报警', value: '2' },
            { text: '三级报警', value: '3' },
          ]
        },
        { label: '通知手机', property: 'noticePhone', type: 'input' },
        { label: '通知邮箱', property: 'noticeEmail', type: 'input' },
        { label: '通知时间', property: 'noticeTime', type: 'datepickerRange', startTimeProperty: 'startNoticeTime', endTimeProperty: 'endNoticeTime' },
      ]
    ],
    listTableItems: [
      { type: 'serial', title: '序号' },
      {
        type: 'text',
        title: '报警类型',
        property: 'alarmType',
        className: 'text-center',
        width: 150,
        textRule: [
          { text: '系统报警', value: '0' },
          { text: '一级报警', value: '1' },
          { text: '二级报警', value: '2' },
          { text: '三级报警', value: '3' },
        ]
      },
      {
        type: 'text',
        title: '通知时间',
        property: 'noticeTime',
        className: 'text-center',
        width: 200,
        showTitle: true,
      },
      {
        type: 'text',
        title: '通知内容',
        property: 'noticeContent',
        showTitle: true,
      },
      {
        type: 'text',
        title: '通知手机',
        width: 356,
        property: 'noticePhone',
        showTitle: true,
      },

      {
        type: 'text',
        title: '通知邮箱',
        width: 355,
        property: 'noticeEmail',
        showTitle: true,
      },
    ],
  };
  constructor() { }

  ngOnInit(): void {
  }

}
