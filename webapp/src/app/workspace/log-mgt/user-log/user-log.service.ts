import { Injectable } from '@angular/core';
import { BaseCURDService } from 'src/app/shared/services/base-curd.service';
import { BaseHttpService } from 'src/app/shared/services/base-http.service';
import { TblUserinfo } from '../../workspace-shared/models/tbl_userinfo';

@Injectable({
  providedIn: 'root',
})
export class UserLogService extends BaseCURDService<TblUserinfo> {
  constructor(protected http: BaseHttpService) {
    super(http, 'api/Loginlog/getList');
  }
}
