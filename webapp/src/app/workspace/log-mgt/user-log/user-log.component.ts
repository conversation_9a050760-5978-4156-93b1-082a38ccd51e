import { Component, OnInit } from '@angular/core';
import { BListPageModel } from 'src/app/shared/models';
import { UserLogService } from './user-log.service';

@Component({
  selector: 'app-user-log',
  templateUrl: './user-log.component.html',
  styleUrls: ['./user-log.component.less'],
})
export class UserLogComponent implements OnInit {
  constructor(private userLogService: UserLogService) { }
  bListPageModel: BListPageModel = {
    title: '用户日志',
    requestUrl: '/api/Loginlog/getList',
    searchTableItems: [
      [
        {
          label: '用户名称',
          property: 'userName',
          type: 'input',
          // search: false
        },
      ],
    ],
    listTableItems: [
      {
        type: 'serial',
        title: '序号',
      },
      {
        type: 'text',
        title: '用户名称',
        property: 'userName',
        className: 'text-center',
        width: 355,
      },
      {
        type: 'text',
        title: '角色名称',
        property: 'roleName',
        className: 'text-center',
        width: 355,
      },
      {
        type: 'text',
        title: '所属单位',
        property: 'orgName',
        className: 'text-center',
        width: 355,
      },
      {
        type: 'text',
        title: 'IP地址',
        property: 'ipAddress',
        className: 'text-center',
        width: 355,
      },
      {
        type: 'text',
        title: '登录时间',
        property: 'syscreated',
        className: 'text-center',
        width: 356,
      },
    ],
  };
  ngOnInit(): void { }
}
