import { Component, OnInit } from '@angular/core';
import { BListPageModel } from 'src/app/shared/models';
import { OperationLogService } from './operation-log.service';

@Component({
  selector: 'app-operation-log',
  templateUrl: './operation-log.component.html',
  styleUrls: ['./operation-log.component.less'],
})
export class OperationLogComponent implements OnInit {
  constructor(private service: OperationLogService) { }
  ngOnInit(): void { }
  handleVisible: boolean = false;
  infoVisible: boolean = false;
  isEdit: boolean = false;
  bListPageModel: BListPageModel = {
    title: '操作日志',
    requestUrl: '/api/Accesslog/getList',
    searchTableItems: [
      [{ label: '用户名称', property: 'userName', type: 'input' }],
    ],
    listTableItems: [
      { type: 'serial', title: '序号' },
      {
        type: 'text',
        title: '用户名称',
        property: 'userName',
        className: 'text-center',
        width: 355,
      },
      {
        type: 'text',
        title: '角色名称',
        property: 'roleName',
        className: 'text-center',
        width: 355,
        showTitle: true,
      },
      {
        type: 'text',
        title: '操作时间',
        width: 355,
        className: 'text-center',
        property: 'syscreated',
        showTitle: true,
      },
      {
        type: 'text',
        title: '操作模块',
        width: 356,
        className: 'text-center',
        property: 'className',
        showTitle: true,
      },

      {
        type: 'text',
        title: '操作内容',
        width: 355,
        className: 'text-center',
        property: 'methodName',
        showTitle: true,
      },
    ],
  };
}
