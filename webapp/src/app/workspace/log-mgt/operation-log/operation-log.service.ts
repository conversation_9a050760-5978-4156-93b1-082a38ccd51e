import { Injectable } from '@angular/core';
import { BaseCURDService } from 'src/app/shared/services/base-curd.service';
import { BaseHttpService } from 'src/app/shared/services/base-http.service';

// 先用角色数据代替
import { TblRoleinfo } from 'src/app/workspace/workspace-shared/models/tbl_roleinfo';
@Injectable({
  providedIn: 'root',
})
export class OperationLogService extends BaseCURDService<TblRoleinfo> {
  constructor(protected http: BaseHttpService) {
    super(http, 'api/role');
  }
}
