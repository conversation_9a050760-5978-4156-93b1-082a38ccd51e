  <table class="sl-table-handle">
    <tr>
      <td class="label" style="width: 100px">
        <span>用户名称：</span>
      </td>
      <td class="contnet">
        <span>{{ userInfo.username }}</span>
      </td>
    </tr>
    <tr>
      <td class="label" style="width: 100px">
        <span>用户角色：</span>
      </td>
      <td class="contnet">
        <span class="control">{{ userInfo.rolenames }}</span>
      </td>
    </tr>
    <tr>
      <td class="label" style="width: 100px">
        <span>所属单位：</span>
      </td>
      <td class="contnet">
        <span>{{ userInfo.unitName }}</span>
      </td>
    </tr>
    <tr>
      <td class="label" style="width: 100px">
        <span>所属部门：</span>
      </td>
      <td class="contnet">
        <span>{{ userInfo.deptName }}</span>
      </td>
    </tr>
    <tr>
      <td class="label" style="width: 100px">
        <span>手机号码：</span>
      </td>
      <td class="contnet">
        <span>{{ userInfo.mobilePhone }}</span>
      </td>
    </tr>
    <tr>
      <td class="label" style="width: 100px">
        <span>电子邮箱：</span>
      </td>
      <td class="contnet">
        <span>{{ userInfo.email }}</span>
      </td>
    </tr>
    <tr>
      <td class="label" style="width: 100px">
        <span>备注：</span>
      </td>
      <td class="textarea">
        <span class="control">{{ userInfo.remark }}</span>
      </td>
    </tr>
    <tr>
      <td class="label" style="width: 100px">
        <span>状态：</span>
      </td>
      <td class="contnet-status">
        <i [class]="userInfo.ifEnabled?'sl-enable-16':'sl-disable-16'"></i>
        <span class="status" [style.color]="userInfo.ifEnabled?'#17913D':'#333'">
          {{ userInfo.ifEnabled ? "启用" : "禁用" }}
        </span>
      </td>
    </tr>
  </table>
