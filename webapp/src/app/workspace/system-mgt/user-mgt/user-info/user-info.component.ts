import { Component, Input, OnInit } from '@angular/core';
import { TblUserinfo } from 'src/app/workspace/workspace-shared/models/tbl_userinfo';

@Component({
  selector: 'app-user-info',
  templateUrl: './user-info.component.html',
  styleUrls: ['./user-info.component.less'],
})
export class UserInfoComponent implements OnInit {
  @Input() userInfo!:TblUserinfo;

  constructor() {}

  ngOnInit(): void {}
  onChange(event: any) {}
}
