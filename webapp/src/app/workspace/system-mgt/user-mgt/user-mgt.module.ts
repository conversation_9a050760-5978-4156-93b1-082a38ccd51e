import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { UserMgtRoutingModule } from './user-mgt-routing.module';
import { UserListComponent } from './user-list/user-list.component';
import { NzInputModule } from 'ng-zorro-antd/input';
import { SlButtonModule } from 'src/app/shared/modules/sl-button/sl-button.module';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzPaginationModule } from 'ng-zorro-antd/pagination';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { SharedModule } from 'src/app/shared/shared.module';
import { UserHandleComponent } from './user-handle/user-handle.component';
import { UserInfoComponent } from './user-info/user-info.component';
import { FormsModule } from '@angular/forms';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzTreeSelectModule } from 'ng-zorro-antd/tree-select';
import { NzIconModule } from 'ng-zorro-antd/icon';
// ZORRO组件库
const NZ_MODULE = [
  NzInputModule,
  NzButtonModule,
  NzPaginationModule,
  NzSelectModule,
  NzModalModule,
  NzRadioModule,
  NzTreeSelectModule,
  NzIconModule
];
@NgModule({
  declarations: [UserListComponent, UserHandleComponent, UserInfoComponent],
  imports: [
    CommonModule,
    UserMgtRoutingModule,
    FormsModule,
    SlButtonModule,
    SharedModule,
    ...NZ_MODULE,
  ],
})
export class UserMgtModule { }
