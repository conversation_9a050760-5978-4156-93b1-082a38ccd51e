import { Component, OnInit } from '@angular/core';
import { Account, BListPageModel } from 'src/app/shared/models';
import { SlModalService } from 'src/app/shared/modules/sl-modal/sl-modal.service';
import { SpinService } from 'src/app/shared/services/spin.service';
import { UserService } from '../user.service';
import { TblUserinfo } from 'src/app/workspace/workspace-shared/models/tbl_userinfo';
import { Principal } from 'src/app/shared/services/principal.service';
import { decryptAES, encryptAES } from 'src/app/shared/utils';
@Component({
  selector: 'app-user-list',
  templateUrl: './user-list.component.html',
  styleUrls: ['./user-list.component.less'],
})
export class UserListComponent implements OnInit {
  isVisible = false;
  // 模拟数据
  tempList: Array<any> = [];
  // 用户详细数据
  handleVisible: boolean = false;
  infoVisible: boolean = false;
  isEdit: boolean = false;
  user: TblUserinfo = new TblUserinfo();
  bListPageModel: BListPageModel = {
    title: '用户管理',
    requestUrl: '/api/Userinfo/getList',
    // 头部按钮组
    headHandleBtnList: [
      {
        text: '新增',
        type: 'primary',
        iconClass: 'sl-plus-16',
        className: 'flex',
        // menuCode: '3.2.1',
        cb: () => {
          this.user = new TblUserinfo();
          this.handleVisible = true;
          this.isEdit = false;
        },
      },
    ],
    // 请求头  ipt  label名字 + 绑定的字段 property  + 绑定的类型 input
    searchTableItems: [
      [
        {
          label: '用户名称',
          property: 'username',
          type: 'input',
        },
        // {
        //   label: '所属单位',
        //   property: 'unitName',
        //   type: 'input',
        // },
      ],
    ],
    // 表格数据
    listTableItems: [
      { type: 'serial', title: '序号' },
      {
        type: 'text',
        title: '用户名称',
        property: 'username',
        className: 'text-center',
        width: 210,
        showTitle: true,
      },
      // {
      //   type: 'text',
      //   title: '所属单位',
      //   property: 'unitName',
      //   className: 'text-center',
      //   width: 278,
      //   showTitle: true,
      // },
      {
        type: 'text',
        title: '手机号码',
        property: 'mobilePhone',
        className: 'text-center',
        width: 182,
        showTitle: true,
      },
      // {
      //   type: 'text',
      //   title: '电子邮箱',
      //   property: 'email',
      //   className: 'text-center',
      //   width: 320,
      //   showTitle: true,
      // },
      // {
      //   type: 'text',
      //   title: '所属角色',
      //   property: 'rolenames',
      //   className: 'text-center',
      //   width: 280,
      //   showTitle: true,
      // },
      {
        type: 'text',
        title: '状态',
        className: 'text-center',
        property: 'ifEnabled',
        showTitle: true,
        width: 200,
        // iconClass: ifEnabled===0?'sl-disable-16':'sl-enable-16',
        textRule: [
          {
            text: '禁用', value: 0, iconClass: 'sl-disable-16', color: '#333'
          },
          {
            text: '启用', value: 1, iconClass: 'sl-enable-16', color: '#333'
          }
        ],
      },
      {
        type: 'btns',
        title: '操作',
        width: 306,
        buttons: [
          {
            text: '查看',
            className: 'success-text',
            // menuCode: '3.2.4',
            cb: (data: TblUserinfo) => {
              this.getInfo(data.id!, () => {
                this.infoVisible = true;
              });
            },
          },
          {
            text: '编辑',
            className: 'primary-text',
            // menuCode: '3.2.2',

            cb: (data: TblUserinfo) => {
              this.isEdit = true;
              this.getInfo(data.id!, () => {
                this.handleVisible = true
              });
            },
          },
          {
            text: '删除',
            className: 'danger-text',
            // menuCode: '3.2.3',
            cb: (data: TblUserinfo) => {
              this.slModalService.openPromptModal({
                type: 'confirm',
                content: '确定删除该用户吗?',
                okCb: () => {
                  this.service
                    .delete(data.id!)
                    .then((res) => {
                      // 删除成功  回调 提示
                      this.slModalService.openPromptModal({
                        type: 'success',
                        content: '删除成功!',
                        okCb: () => {
                          const refreshCb = this.bListPageModel.refreshCb;
                          if (refreshCb) {
                            refreshCb(this.bListPageModel.search);
                          }
                        },
                      });
                    })
                    .catch((err) => {
                      console.error(err);
                      this.slModalService.openPromptModal({
                        type: 'error',
                        content: err || '删除失败!',
                      });
                    });
                },
              });
            },
          },
          // {
          //   text: '重置密码',
          //   className: 'warn-text',
          //   menuCode: '3.2.5',
          //   cb: (data: TblUserinfo) => {
          //     this.slModalService.openPromptModal({
          //       type: 'confirm',
          //       content: '确定重置密码吗?',
          //       okCb: () => {
          //         this.resetPass(data.id!)
          //       },
          //     });
          //   },
          // },
        ],
      },
    ],
  };
  constructor(
    private spinService: SpinService,
    private service: UserService,
    private slModalService: SlModalService,
    private principal: Principal
  ) { }
  account: Account = new Account();
  ngOnInit(): void { 
    this.principal.identity().then((res) => {
      this.account = res
    })
  }

  /**
   * 根据用户id获取详情信息
   * @param id 
   * @param cb 
   */
  getInfo(id: string, cb?: () => any) {
    this.spinService.showText();
    this.service
      .getInfo(id)
      .then((res: TblUserinfo) => {
        this.spinService.close();
        // 编辑传递数据到user 
        this.user = res;
        this.user.password = decryptAES(res.password!)
        cb && cb();
      })
      .catch((err) => {
        this.spinService.close();
      });
  }

  /**
   * 保存角色
   */
  save() {
    this.spinService.showText()
    const {roleid, orgId, organize} = this.account;
    this.user.roleIds = 'f1536c48a01346c5abe6e76727b1a6d8';
    this.user.unitId = orgId;
    this.service.save(this.user).then((res) => {
      this.spinService.close();
      if (res) this.handleVisible = false
      const refreshCb = this.bListPageModel.refreshCb;
      if (refreshCb) {
        refreshCb(this.bListPageModel.search)
      };
    }).catch((err) => {
      this.spinService.close()
      this.slModalService.openPromptModal({
        type: 'error',
        content: err || '保存失败!',
      });
    })
  }

  /**
   * 重置密码
   */
  resetPass(id: string) {
    this.service.updateDefultPassword(id).then((res) => {
      this.spinService.close();
    })
      .catch((err) => {
        this.spinService.close();
        this.slModalService.openPromptModal({
          type: 'error',
          content: err || '重置密码失败!',
        });
      });
  }
}
