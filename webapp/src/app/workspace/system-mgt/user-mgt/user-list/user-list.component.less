.user-mgt-container {
  background-color: #fff;
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.16);
  border-radius: 4px;
  width: 100%;
  //   height: calc(100% - 41px);
  height: 801px;

  // 头部标题区
  .header {
    width: 100%;
    height: 48px;
    padding: 0 20px;

    &>span {
      color: #333;
      line-height: 48px;
      font-weight: 500;
      font-size: 16px;
      padding-left: 5px;
    }

    border-bottom: 1px solid #777;
  }

  // 主体区
  .body {
    width: 100%;
    height: 753px;

    .body-header {
      height: 76px;
      width: 100%;
      line-height: 76px;
      padding: 0 26px;
      position: relative;

      &>label {
        display: inline-block;
        width: 74px;
        text-align: right;
        padding-right: 10px;
        color: #333;
      }

      .header-btn {
        display: inline-block;
        margin-left: 60px;

        &>button {
          margin: 0 15px;
        }

        &>button:last-child {
          position: absolute;
          right: 10px;
          top: 30px;
        }

      }
    }

    // 表格区
    .body-list {
      height: 600px;

      table {
        width: 100%;
        border: 1px solid rgba(215, 219, 232, 1);

        tr {
          height: 36px;

          &.body-list-title {
            background-color: #EEF4FE;
            border: 1px solid rgba(215, 219, 232, 1);
          }

          td {
            color: #333;
            font-size: 14px;
            font-weight: 400;
            text-align: center;

            &:first-child {
              width: 64px;
            }

            &>div.body-list-opera {
              display: flex;
              justify-content: space-around;
            }

            &:nth-child(2),
            &:nth-child(7) {
              width: 210px;
            }

            &:nth-child(3) {
              width: 280px;
            }

            &:nth-child(4) {
              width: 182px;
            }

            &:nth-child(5) {
              width: 320px;
            }

            &:nth-child(6) {
              width: 280px;
            }

            &:nth-child(8) {
              width: 306px;
            }
          }
        }
      }
    }

    // 底部分页
    .body-page {
      height: 76px;
      background-color: #fff;
      display: flex;
      justify-content: space-between;
      padding: 0 20px;
    }
  }
}
