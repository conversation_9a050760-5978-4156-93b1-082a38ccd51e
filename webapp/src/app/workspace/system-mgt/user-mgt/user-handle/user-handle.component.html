<form #validateForm="ngForm">
  <table class="sl-table-handle">
    <tr>
      <td class="label required" style="width: 100px">
        <span>用户名称：</span>
      </td>
      <td class="contnet">
        <input
          type="text"
          nz-input
          name="userName"
          [(ngModel)]="user.username"
          placeholder="请输入用户名称"
          [slValidate]="{ required: true, label: '角色名称' }"
          autocomplete="off"
        />
      </td>
    </tr>
    <!-- <tr>
      <td class="label required" style="width: 100px">
        <span>用户角色：</span>
      </td>
      <td class="contnet rolename">
        <nz-tree-select
          [(ngModel)]="testrolename"
          [nzNodes]="nodesRole"
          nzCheckable
          nzPlaceHolder="请选择用户角色"
          style="width: 319px"
          name="testrolename"
          [slValidate]="{ required: true, label: '用户角色' }"
          (ngModelChange)="onRoleNameChange($event)"
          ></nz-tree-select>
          <a href="javascript:void(0)" *ngIf="userRole">
            <i nz-icon nzType="down" nzTheme="outline" class="icon">
            </i>
          </a>
      </td>
    </tr> -->
    <!-- <tr>
      <td class="label required" style="width: 100px">
        <span>所属单位：</span>
      </td>
      <td class="contnet">
        <nz-tree-select
        [(ngModel)]="user.unitName"
        [nzNodes]="nodesOrg1"
        nzShowSearch
        nzPlaceHolder="请选择所属单位"
        style="width: 319px"
        name="user.unitName"
        [slValidate]="{ required: true, label: '所属单位' }"
        nzVirtualHeight="150px"
        (ngModelChange)="onUnitNameChange($event,nodesOrg1)"
        nzDefaultExpandAll
      ></nz-tree-select>
      </td>
    </tr>
    <tr>
      <td class="label" style="width: 100px">
        <span>所属部门：</span>
      </td>
      <td class="contnet">
        <nz-tree-select
          [(ngModel)]="user.deptName"
          [nzNodes]="nodesOrg2"
          nzShowSearch
          nzPlaceHolder="请选择所属部门"
          nzVirtualHeight="150px"
          style="width: 319px"
          name="user.deptName"
          (ngModelChange)="onDeptNameChange($event,nodesOrg2)"
          nzDefaultExpandAll
        ></nz-tree-select>
      </td>
    </tr> -->
    <tr>
      <td class="label required" style="width: 100px">
        <span>手机号码：</span>
      </td>
      <td class="contnet">
        <input
          type="text"
          nz-input
          name="mobilePhone"
          [(ngModel)]="user.mobilePhone"
          placeholder="请输入手机号码"
          [slValidate]="{ required: true, label: '手机号码',type:'phone',errorTip:'手机号码不符合规则' }"
          autocomplete="off"
          minlength="11"
          maxlength="11"
        />
      </td>
    </tr>
    <tr>
      <td class="label required" style="width: 100px">
        <span>密码：</span>
      </td>
      <td class="contnet">
        <input
          type="text"
          nz-input
          name="password"
          [(ngModel)]="user.password"
          placeholder="请输入密码"
          [slValidate]="{ required: true, label: '密码',type:'pwd',errorTip:'密码不符合规则' }"
          autocomplete="off"
        />
      </td>
    </tr>
    <!-- <tr>
      <td class="label" style="width: 100px">
        <span>电子邮箱：</span>
      </td>
      <td class="contnet">
        <input
          type="text"
          nz-input
          name="email"
          [(ngModel)]="user.email"
          placeholder="请输入电子邮箱"
          [slValidate]="{  label: '邮箱',type:'email',errorTip:'邮箱不符合规则' }"
          autocomplete="off"
        />
      </td>
    </tr>
    <tr>
      <td class="label" style="width: 100px">
        <span>备注：</span>
      </td>
      <td class="textarea">
        <textarea
          name="userText"
          [(ngModel)]="user.remark"
          style="width: 100%;padding:2px 11px 20px;"
          placeholder="请输入备注"
          rows="3"
          nz-input
          type="text"
          ></textarea>
      </td>
    </tr> -->
    <tr>
      <td class="label required" style="width: 100px">
        <span>状态：</span>
      </td>
      <td class="contnet">
        <!-- <input
          type="radio"
          name="userState"
          id="true"
          [slValidate]="{ required: true, label: '备注' }"
        />启用
        <input
          type="radio"
          name="userState"
          id="false"
          [slValidate]="{ required: true, label: '备注' }"
        />禁用 -->
        <nz-radio-group [(ngModel)]="user.ifEnabled" name="state-radio">
          <label nz-radio [nzValue]="1">启用</label>
          <label nz-radio [nzValue]="0">禁用</label>
        </nz-radio-group>
      </td>
    </tr>
  </table>
</form>
