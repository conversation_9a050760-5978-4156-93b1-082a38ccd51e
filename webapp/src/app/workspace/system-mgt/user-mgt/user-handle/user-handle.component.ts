import { utf8Encode } from '@angular/compiler/src/util';
import { Component, Host, Input, OnInit, SimpleChanges, ViewChild } from '@angular/core';
import { NgForm } from '@angular/forms';
import * as _ from 'lodash';
import { isTemplateRef } from 'ng-zorro-antd/core/util';
import { SlModalComponent } from 'src/app/shared/modules/sl-modal/sl-modal/sl-modal.component';
import { OrganizeService } from 'src/app/shared/services/organize.service';
import { TblUserinfo } from 'src/app/workspace/workspace-shared/models/tbl_userinfo';
import { UserService } from '../user.service';

interface TreeNode {
  id?: string
  orgTypeCode?: string
  title?: string
  orgShort?: string
  parentShortName?: string
  parentId?: string
  children?: Array<TreeNode>
  key?: string
  isLeaf?: boolean
}
@Component({
  selector: 'app-user-handle',
  templateUrl: './user-handle.component.html',
  styleUrls: ['./user-handle.component.less'],
})
export class UserHandleComponent implements OnInit {
  // DOM节点NgForm
  @ViewChild(NgForm) validateForm!: NgForm;
  // list传递过来的user
  @Input() user!: TblUserinfo
  @Input() isEdit!: boolean  // false 新增 true 编辑
  state: string = 'A'
  testrolename!: Array<string>  // 用户角色
  // 拿到modal宿主元素
  constructor(@Host() private modal: SlModalComponent,
    private orgService: OrganizeService,
    private userService: UserService) { }
  ngAfterViewInit(): void {
    // 父组件的form拿到该组件的validateForm
    this.modal.form = this.validateForm;
  }
  role: TblUserinfo = new TblUserinfo();
  // 用户角色是否有值
  get userRole(): boolean {
    return this.testrolename && this.testrolename.length ? false : true
  }
  ngOnInit(): void {
    if (!this.isEdit) {
      this.user.ifEnabled = 1 // 默认按钮 
    }
    // this.getRoleTree()
    // this.getOrg()
    // if (this.user.roleIds) {
    //   const names = this.user.roleIds!
    //   this.testrolename = names.split(',')
    // }
  }
  // 树状图 节点数据
  nodesRole: Array<any> = [] //所属角色
  nodesOrg1: Array<any> = [] //所属单位
  nodesOrg2: Array<any> = [] // 所属部门 
  nodesOrgList: Array<any> = [] // 部门单位数据合
  /**
   * 角色名称 选择
   * @param event 
   */
  onRoleNameChange(event: Array<string>): void {
    let str = event.join(',')
    this.user.roleIds = str
  }
  // 获取角色名称 树状信息
  private getRoleTree() {
    this.userService.roleList().then(res => {
      res.forEach((ele: any) => {
        ele.title = ele.roleName,
          ele.key = ele.id,   // 后台保存要id 
          ele.isLeaf = true
      })
      this.nodesRole = res
    })
  }
  // 获取单位信息
  private getOrg() {
    this.orgService.getOrganize().then(res => {
      // 复制 
      this.nodesOrgList = [_.cloneDeep(res)]
      this.nodesOrg1 = [_.cloneDeep(res)]
      this.nodesOrg1 = this.recursion(this.nodesOrg1)

      if (this.user.unitName) {
        const data = _.cloneDeep(this.nodesOrgList)
        this.getDeptForUnit(data, this.user.unitName!)
      }
    })
  }

  // TreeNode
  private recursion(data: Array<any>) {
    for (let i = 0; i < data.length; i++) {
      data[i].key = data[i].orgShort
      if (data[i].orgTypeCode == '02') {
        data.splice(i, 1);
        i--;
      }
      if (data[i]?.children.length == 0) {
        data[i].isLeaf = true
      }
      if (data[i]?.children) {
        data[i].children = this.recursion(data[i].children!)
      }
    }
    return data
  }

  // 所选单位选择
  onUnitNameChange(unitName: string, nodesOrg1: Array<any>) {
    const data = _.cloneDeep(this.nodesOrgList)
    this.idByName(nodesOrg1, unitName, 'unitId')
    this.getDeptForUnit(data, unitName)
  }

  // 拿到对应 id
  private idByName(list: Array<any>, key: string, type: 'unitId' | 'deptId') {
    list.forEach((ele: any) => {
      if (key === ele.key) {
        type === 'unitId' ? this.user.unitId = ele.id : this.user.deptId = ele.id
        // this.userId = ele.id  // 拿到Id
      };
      if (ele.children && ele.children.length) {
        ele = this.idByName(ele.children, key, type)
      }
    })
  }

  // 选择单位后显示的部门
  private getDeptForUnit(data: Array<any>, unitName: string) {
    this.nodesOrg2 = []
    this.recurUnitName(data, unitName)
  }

  // 所选部门选择
  onDeptNameChange(event: string, nodesOrg2: Array<any>) {
    this.idByName(nodesOrg2, event, 'deptId')
  }

  private recurUnitName(item: Array<any>, unitName: string) {

    // 部门没 children code 02 parent....
    item.forEach(ele => {
      if (ele.children && ele.children.length) {
        ele = this.recurUnitName(ele.children, unitName)
      } else {
        if (ele.parentShortName == unitName && ele.orgTypeCode === '02') {
          this.nodesOrg2.push(ele!)
        }
      }
    })
    this.nodesOrg2.length ? this.nodesOrgChan(this.nodesOrg2) : ''
  }
  private nodesOrgChan(data: Array<any>) {
    data.map(item => {
      return item.key = item.orgShort
    })
  }
}
