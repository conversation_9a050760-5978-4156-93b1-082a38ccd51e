import { Injectable } from '@angular/core';
import { BaseSearch } from 'src/app/shared/models';
import { BaseCURDService } from 'src/app/shared/services/base-curd.service';
import { BaseHttpService } from 'src/app/shared/services/base-http.service';
import { TblUserinfo } from '../../workspace-shared/models/tbl_userinfo';
@Injectable({
  providedIn: 'root',
})
export class UserService extends BaseCURDService<TblUserinfo> {
  baseSearch: BaseSearch = new BaseSearch()
  constructor(protected http: BaseHttpService) {
    super(http, 'api/Userinfo');
  }
  /**
 * 重置密码
 * @param id
 * @returns
 */
  updateDefultPassword(id: string) {
    return this.http.get(
      `/api/Userinfo/updateDefultPassword/${id}`
    );
  }
  /**
* 修改密码
* @param password
* @returns
*/
  updatePassword(data: { oldPassword: string, newPassword: string }) {
    return this.http.post(`/api/Userinfo/updatePassword`, data);
  }
  // 获取角色信息接口
  roleList(): Promise<any> {
    return this.http.post(`/api/Roleinfo/getList`, { ifPage: false })
  }
  // 获取单位信息接口  注意get 请求
  orgList(): Promise<any> {
    return this.http.get('/api/Organize/getOrganize')
  }
}
