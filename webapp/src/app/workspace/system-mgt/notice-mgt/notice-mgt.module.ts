import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { NoticeMgtRoutingModule } from './notice-mgt-routing.module';
import { NoticeMgtComponent } from './notice-mgt.component';
import { SlValidateModule } from 'src/app/shared/modules/sl-validate/sl-validate.module';
import { FormsModule } from '@angular/forms';
import { SharedModule } from 'src/app/shared/shared.module';


@NgModule({
  declarations: [
    NoticeMgtComponent
  ],
  imports: [
    CommonModule,
    NoticeMgtRoutingModule,
    NzCheckboxModule,
    NzRadioModule,
    SlValidateModule,
    FormsModule,
    SharedModule
  ]
})
export class NoticeMgtModule { }
