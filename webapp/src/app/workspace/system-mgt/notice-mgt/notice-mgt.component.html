<div class="notice-setting-container">
    <div class="title">
        <i class="sl-location-16"></i>
        <span>通知管理</span>
    </div>
    <ul class="tabset">
        <ng-container *ngFor="let item of tabList;index as i;trackBy: trackByItem">
            <li *slHasAnyAuthority="item.menuCode">
                <a (click)="switchTab(i)" [class.active]="i==currentTabIndex"
                    href="javascript:void(0)">{{item.title}}</a>
            </li>
        </ng-container>
    </ul>
    <div class="contetn">
        <form #validateForm="ngForm">
            <table>
                <ng-container [ngTemplateOutlet]="isSysSettingMode?settingMode:notSettingMode">
                </ng-container>
                <tr *ngIf="sysSetting.ifNotice=='1'">
                    <td class="label">通知形式</td>
                    <td class="control">
                        <label [class.sl-ant-disabled-checkbox]="isDisabled" nz-checkbox [nzDisabled]="isDisabled"
                            [(nzChecked)]="sysSetting.isSMSChecked"
                            (nzCheckedChange)="noticeTypeChange($event, 'ifSms')">短信通知</label>
                        <label style="margin-left: 65px;" [class.sl-ant-disabled-checkbox]="true" nz-checkbox
                            [nzDisabled]="true" [(nzChecked)]="sysSetting.isVoiceChecked"
                            (nzCheckedChange)="noticeTypeChange($event, 'ifVoice')">语音通知</label>
                        <label style="margin-left: 65px;" [class.sl-ant-disabled-checkbox]="isDisabled" nz-checkbox
                            [nzDisabled]="isDisabled" [(nzChecked)]="sysSetting.isEmailChecked"
                            (nzCheckedChange)="noticeTypeChange($event, 'ifEmail')">邮件通知</label>
                    </td>
                </tr>
                <tr *ngIf="sysSetting.isSMSChecked">
                    <td class="label">短信通知组</td>
                    <td class="control">
                        <textarea name="smsGroup" nz-input rows="3"
                            [slValidate]="{label: '短信通知组',  type: 'phone',multiple:true }"
                            [placeholder]="hasEditAuthority?'请输入短信通知号码（多行请按“Enter”键换行切换）':''"
                            [(ngModel)]="sysSetting.smsGroup" [disabled]="isDisabled"></textarea>
                    </td>
                </tr>
                <tr *ngIf="sysSetting.isVoiceChecked">
                    <td class="label">语音通知组</td>
                    <td class="control">
                        <textarea name="voiceGroup" nz-input rows="3"
                            [slValidate]="{label: '语音通知组',type: 'phone',multiple:true }"
                            [placeholder]="hasEditAuthority?'请输入语音通知号码（多行请按“Enter”键换行切换）':''"
                            [(ngModel)]="sysSetting.voiceGroup" [disabled]="isDisabled"></textarea>
                    </td>
                </tr>
                <tr *ngIf="sysSetting.isEmailChecked">
                    <td class="label">邮件通知组</td>
                    <td class="control">
                        <textarea name="emailGroup" nz-input rows="3"
                            [slValidate]="{label: '邮件通知组',  type: 'email',multiple:true}"
                            [placeholder]="hasEditAuthority?'请输入通知邮箱（多行请按“Enter”键换行切换）':''"
                            [(ngModel)]="sysSetting.emailGroup" [disabled]="isDisabled"></textarea>
                    </td>
                </tr>
            </table>
        </form>
    </div>
</div>
<!--系统报警-->
<ng-template #settingMode>
    <tr>
        <td class="label">系统报警类型</td>
        <td class="control">
            <label [class.sl-ant-disabled-checkbox]="isDisabled" [style.marginLeft.px]="item.marginLeft" nz-checkbox
                [nzDisabled]="isDisabled" *ngFor="let item of alarmTypeList; index as i" [(nzChecked)]="item.checked">
                {{ item.label}}
            </label>
        </td>
        <ng-template [ngTemplateOutlet]="handleArea"></ng-template>
    </tr>
    <tr>
        <td class="label">通知运维人员</td>
        <td class="control">
            <nz-radio-group [(ngModel)]="sysSetting.ifNotice" (ngModelChange)="ifNoticeChange($event)"
                [nzDisabled]="isDisabled" class="sl-ant-disabled-radio" name="notice">
                <label nz-radio nzValue="1">是</label>
                <label nz-radio nzValue="0" style="margin-left: 99px;">否</label>
            </nz-radio-group>
        </td>
    </tr>
</ng-template>

<!--一级二级三级报警-->
<ng-template #notSettingMode>
    <tr>
        <td class="label">是否启用通知</td>
        <td class="control">
            <nz-radio-group [(ngModel)]="sysSetting.ifNotice" (ngModelChange)="ifNoticeChange($event)"
                [nzDisabled]="isDisabled" class="sl-ant-disabled-radio" name="ifNotice">
                <label nz-radio nzValue="1">是</label>
                <label nz-radio nzValue="0" style="margin-left: 99px;">否</label>
            </nz-radio-group>
        </td>
        <ng-template [ngTemplateOutlet]="handleArea"></ng-template>
    </tr>
</ng-template>
<!--编辑操作-->
<ng-template #handleArea>
    <td *slHasAnyAuthority="currentTabItem.editMenuCode" [attr.rowspan]="isSysSettingMode?6:5" style="width: 306px"
        class="text-center">
        <ng-container *ngIf="!currentTabItem.editable; else editableBlock">
            <a href="javascript:void(0)" (click)="openEdit()">编辑</a>
        </ng-container>
        <ng-template #editableBlock>
            <button class="sl-default-btn" (click)="closeEdit()" style="margin-right: 10px">
                取消
            </button>
            <button class="sl-primary-btn" (click)="save(validateForm)">
                保存
            </button>
        </ng-template>
    </td>
</ng-template>