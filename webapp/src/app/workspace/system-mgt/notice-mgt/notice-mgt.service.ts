import { BaseHttpService } from 'src/app/shared/services/base-http.service';
import { Injectable } from '@angular/core';
import { BaseCURDService } from 'src/app/shared/services/base-curd.service';
import { TblAlarmsetting } from '../../workspace-shared/models/tbl_alarmsetting';

@Injectable({
  providedIn: 'root'
})
export class NoticeMgtService extends BaseCURDService<TblAlarmsetting> {

  constructor(protected http: BaseHttpService) {
    super(http, '/api/Alarmsetting')
  }

}
