import { Component, OnInit } from '@angular/core';
import { NgForm } from '@angular/forms';
import * as _ from 'lodash';
import { SlModalService } from 'src/app/shared/modules/sl-modal/sl-modal.service';
import { SlValidateService } from 'src/app/shared/modules/sl-validate/sl-validate.service';
import { Principal } from 'src/app/shared/services/principal.service';
import { TblAlarmsetting } from '../../workspace-shared/models/tbl_alarmsetting';
import { NoticeMgtService } from './notice-mgt.service';
interface AlarmType {
  label: string
  checked: boolean
  marginLeft: number
}

interface AlarmTab {
  title: string
  noticeType: number
  alarmSetting: TblAlarmsetting
  menuCode: string
  editMenuCode: string
  editable: boolean // 编辑按钮是否打开
}


/**
 *  
 * 分步保存
 * 每一个tab 页面只保存自己的数据
 * @export
 * @class NoticeMgtComponent
 * @implements {OnInit}
 */
@Component({
  selector: 'app-notice-mgt',
  templateUrl: './notice-mgt.component.html',
  styleUrls: ['./notice-mgt.component.less']
})
export class NoticeMgtComponent implements OnInit {
  // 当前的设置数据
  sysSetting: TblAlarmsetting = new TblAlarmsetting()

  // editable: boolean = false

  // 邮箱通知是否是选中状态
  isEmailChecked: boolean = false;
  // 短信通知是否选中状态
  isSMSChecked: boolean = false;
  // 电话语音通知是否选中状态
  isPhoneVoiceChecked: boolean = false;
  // 语音播报是否选中
  isWebVoiceChecked: boolean = false;

  // 当前的tab下标
  currentTabIndex: number = 0
  // 数据
  tabList: Array<AlarmTab> = [
    { title: '系统报警', noticeType: 0, alarmSetting: new TblAlarmsetting({}), menuCode: '3.4.1', editMenuCode: '3.4.1.1', editable: false },
    { title: '一级报警', noticeType: 1, alarmSetting: new TblAlarmsetting(), menuCode: '3.4.2', editMenuCode: '3.4.2.1', editable: false },
    { title: '二级报警', noticeType: 2, alarmSetting: new TblAlarmsetting(), menuCode: '3.4.3', editMenuCode: '3.4.3.1', editable: false },
    { title: '三级报警', noticeType: 3, alarmSetting: new TblAlarmsetting(), menuCode: '3.4.4', editMenuCode: '*******', editable: false },
  ]

  //原始数据
  originTabList: Array<AlarmTab> = []



  alarmTypeList: Array<AlarmType> = [
    { label: 'AIS基站异常', checked: false, marginLeft: 0 },
    { label: 'AIS大数据服务异常', checked: false, marginLeft: 45 },
  ]

  // 系统设置
  get isSysSettingMode() {
    return this.currentTabIndex === 0
  }

  get currentTabItem() {
    return this.tabList[this.currentTabIndex]
  }

  get hasEditAuthority() {
    return this.principal.hasAuthority(this.currentTabItem.editMenuCode)
  }

  get editableMode() {
    return this.currentTabItem.editable
  }

  get isDisabled() {
    return !this.hasEditAuthority || !this.editableMode
  }



  constructor(
    private service: NoticeMgtService,
    private slValidateService: SlValidateService,
    private slModalService: SlModalService,
    private principal: Principal
  ) { }

  ngOnInit(): void {
    this.getList()
  }

  // 获取所有报警数据
  getList() {
    this.service.getListWithoutPage({ ifPage: false }).then(res => {
      this.tabList.forEach(ele => {
        const alarmSetting = res.find(e => e.noticeType == ele.noticeType)
        if (alarmSetting) {
          alarmSetting.isEmailChecked = alarmSetting.ifEmail == '1'
          alarmSetting.isSMSChecked = alarmSetting.ifSms == '1'
          // alarmSetting.isVoiceChecked = alarmSetting.ifVoice == '1'
          alarmSetting.isVoiceChecked = false// 禁用语音通知
          if (alarmSetting.noticeType == 0) {// 系统设置  
            const arr = alarmSetting.sysAlarmType?.split('')
            arr?.forEach((ele, index) => {
              this.alarmTypeList[index].checked = ele === '1'
            })
          }
          ele.alarmSetting = alarmSetting
        }
      })
      this.originTabList = _.cloneDeep(this.tabList)
      this.sysSetting = this.tabList[this.currentTabIndex].alarmSetting
    })
  }

  // 通知类型改变事件
  noticeTypeChange(checked: boolean, property: string) {
    this.sysSetting[property] = checked ? '1' : '0'
  }
  // 是否通知
  ifNoticeChange(ifNotice: string) {
    if (ifNotice == '0') { // 否
      this.sysSetting.ifSms = '0'
      this.sysSetting.ifVoice = '0'
      this.sysSetting.ifEmail = '0'
      this.sysSetting.smsGroup = ''
      this.sysSetting.emailGroup = ''
      this.sysSetting.voiceGroup = ''
      this.sysSetting.isEmailChecked = false
      this.sysSetting.isVoiceChecked = false
      this.sysSetting.isSMSChecked = false
    }
  }

  // 切换tab
  switchTab(index: number) {
    if (this.currentTabIndex != index) {
      this.tabList[this.currentTabIndex].alarmSetting = _.cloneDeep(this.sysSetting)
      this.sysSetting = _.cloneDeep(this.tabList[index].alarmSetting)
      this.currentTabIndex = index
    }
  }

  /**
   * 打开编辑
   */
  openEdit() {
    this.tabList[this.currentTabIndex].editable = true
  }

  // 取消编辑
  closeEdit() {
    this.tabList[this.currentTabIndex].editable = false
    // this.tabList = _.cloneDeep(this.originTabList)
    this.tabList[this.currentTabIndex].alarmSetting = _.cloneDeep(this.originTabList[this.currentTabIndex].alarmSetting)
    this.sysSetting = this.tabList[this.currentTabIndex].alarmSetting
  }

  // 保存数据
  save(validateForm: NgForm) {
    const isValidate = this.slValidateService.validateFormWithAlert(validateForm)
    if (isValidate) {
      const setting = new TblAlarmsetting(this.sysSetting)
      if (this.sysSetting.noticeType == 0) {// 系统设置  
        setting.sysAlarmType = this.alarmTypeList.map(ele => ele.checked ? '1' : '0').join('')
      }
      console.log('保存', setting);
      this.slModalService.openPromptModal({
        type: 'confirm',
        content: '确认保存吗？',
        okCb: () => {
          this.service.save(setting).then(res => {
            // this.getList()
            this.tabList[this.currentTabIndex].editable = false
            this.slModalService.openPromptModal({
              type: 'success',
              content: '保存成功'
            })
          }).catch(err => {
            this.slModalService.openPromptModal({
              type: 'error',
              content: err || '保存失败'
            })
          })
        }
      })
    }
  }

  trackByItem(index: number, item: any) {
    return item.noticeType
  }
}
