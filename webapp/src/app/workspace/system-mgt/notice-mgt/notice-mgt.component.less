.notice-setting-container {
  & > .title {
    width: 100%;
    height: 48px;
    border-bottom: 1px solid #eceef4;
    display: flex;
    align-items: center;
    font-size: 16px;
    color: #333333;
    font-weight: 500;
    margin-bottom: 15px;
    i + span {
      margin-left: 6px;
    }
  }
  ul.tabset {
    display: inline-flex;
    background: #f5f7fa;
    li {
      & + li {
        margin-left: 2px;
      }
      a {
        display: flex;
        width: 117px;
        height: 34px;
        justify-content: center;
        align-items: center;
        color: #333;
      }
      &:last-child > a {
        border-top-right-radius: 2px;
        border-bottom-right-radius: 2px;
      }
      &:first-child > a {
        border-top-left-radius: 2px;
        border-bottom-left-radius: 2px;
      }
      a.active {
        background: #2d74ef;
        box-shadow: 0px 0px 4px 2px rgba(45, 116, 239, 0.15);
        color: #fff;
      }
    }
  }
}

form {
  table {
    tr {
      td {
        border: 1px solid rgba(215, 219, 232, 1);
        padding: 4px 6px;
        &.label {
          height: 36px;
          width: 360px;
          text-align: center;
          color: #666;
          background: #eef4fe;
        }
        &:nth-child(even) {
          padding-left: 20px;
        }
        textarea {
          resize: none;
          width: 100%;
          float: left;
          border-radius: 1px;
          border: 1px solid rgba(205, 210, 218, 1);
        }
      }
    }
  }
}
