import { Component, OnInit } from '@angular/core';
import { BListPageModel, Module } from 'src/app/shared/models';
import { SlModalService } from 'src/app/shared/modules/sl-modal/sl-modal.service';
import { SpinService } from 'src/app/shared/services/spin.service';
import { TblRoleinfo } from 'src/app/workspace/workspace-shared/models/tbl_roleinfo';
import { RoleService } from '../role.service';

@Component({
  selector: 'app-role-list',
  templateUrl: './role-list.component.html',
  styleUrls: ['./role-list.component.less']
})
export class RoleListComponent implements OnInit {
  handleVisible: boolean = false;
  infoVisible: boolean = false;
  isEdit: boolean = false;
  role: TblRoleinfo = new TblRoleinfo()
  //模板配置
  bListPageModel: BListPageModel = {
    title: '角色管理',
    requestUrl: '/api/Roleinfo/getList',
    headHandleBtnList: [
      {
        text: '新增',
        type: 'primary',
        iconClass: 'sl-plus-16',
        className: 'flex',
        menuCode: '3.3.1',
        cb: () => {
          this.role = new TblRoleinfo()
          this.handleVisible = true;
          this.isEdit = false;
        },
      }
    ],
    searchTableItems: [
      [
        { label: '角色名称', property: 'roleName', type: 'input', search: false },
      ]
    ],
    listTableItems: [
      { type: 'serial', title: '序号' },
      { type: 'text', title: '角色编号', property: 'roleCode', className: 'text-center', width: 140 },
      {
        type: 'text',
        title: '角色名称',
        property: 'roleName',
        className: 'text-center',
        width: 180,
        showTitle: true
      },
      {
        type: 'text',
        title: '角色描述',
        property: 'description',
        showTitle: true
      },
      {
        type: 'btns',
        title: '操作',
        width: 208,
        buttons: [
          {
            text: '查看',
            className: 'success-text',
            menuCode: '3.3.4',
            cb: (data: TblRoleinfo) => {
              this.getInfo(data.id!, () => {
                this.infoVisible = true;
              })
            },
          },
          {
            text: '编辑',
            className: ' primary-text',
            menuCode: '3.3.2',
            cb: (data: TblRoleinfo) => {
              this.isEdit = true;
              this.getInfo(data.id!, () => {
                this.handleVisible = true;
              })
            },
          },
          {
            text: '删除',
            className: 'danger-text',
            menuCode: '3.3.3',
            cb: (data: TblRoleinfo) => {
              this.slModalService.openPromptModal({
                type: 'confirm',
                content: '确定删除吗？',
                okCb: () => {
                  this.service.delete(data.id!).then(() => {
                    this.slModalService.openPromptModal({
                      type: 'success',
                      content: '删除成功！',
                      okCb: () => {
                        const refreshCb = this.bListPageModel.refreshCb;
                        if (refreshCb) {
                          refreshCb(this.bListPageModel.search)
                        }
                      },
                    });
                  }).catch((err) => {
                    this.slModalService.openPromptModal({
                      type: 'error',
                      content: err || '删除失败！',
                    });
                  });
                },
              });
            },
          },

        ],
      },
    ],
  };
  constructor(
    private service: RoleService,
    private slModalService: SlModalService,
    private spinService: SpinService
  ) { }

  ngOnInit(): void {
  }

  /**
   *
   * 根据角色id获取详情
   * @param {string} id
   * @param {() => any} [cb]
   * @memberof RoleListComponent
   */
  getInfo(id: string, cb?: () => any) {
    this.spinService.showText();
    this.service.getInfo(id).then((role: TblRoleinfo) => {
      this.spinService.close();
      this.role = role
      cb && cb();
    }).catch(err => {
      this.spinService.close();
    })
  }

  /**
   * 校验至少选中一条角色权限
   * @returns 
   */
  beforeSave = (): boolean => {
    const result = this.computedSomeIsSelected(this.role.moduleList || []);
    if (!result) {
      this.slModalService.openPromptModal({ type: 'error', content: '至少选择一项配置权限！' })
      return false
    }
    return true
  }

  /**
   * 
   * 保存角色
   * @memberof RoleListComponent
   */
  save() {
    this.spinService.showText()
    this.service.save(this.role).then(res => {
      this.spinService.close()
      if (res) {
        this.handleVisible = false
        const refreshCb = this.bListPageModel.refreshCb;
        if (refreshCb) {
          refreshCb(this.bListPageModel.search)
        }
        this.slModalService.openPromptModal({ type: 'success', content: '保存成功' })
      }
    }).catch(err => {
      this.spinService.close()
      this.slModalService.openPromptModal({ type: 'error', content: err || '保存失败！' })
    })
  }

  private computedSomeIsSelected(list: Module[]) {
    let result: boolean = false;
    for (let i = 0; i < list.length; i++) {
      const ele = list[i];
      if (ele.ifCheck) {
        result = true;
        break;
      } else {
        if (ele.children && ele.children.length) {
          this.computedSomeIsSelected(ele.children);
        }
      }
    }
    return result;
  }
}
