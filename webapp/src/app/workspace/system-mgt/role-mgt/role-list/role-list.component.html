<slb-list-page [bListPageModel]="bListPageModel"></slb-list-page>

<sl-modal *ngIf="handleVisible" [beforeSave]="beforeSave" [slTitle]=" isEdit?'角色（编辑）': '角色（新增）'"
    [(slVisible)]=" handleVisible" [slWidth]="704" (onOk)="save()">
    <app-role-handle [isEdit]="isEdit" [role]="role"></app-role-handle>
</sl-modal>

<sl-modal *ngIf="infoVisible" [slFooter]="false" [slTitle]="'角色（详细）'" [(slVisible)]="infoVisible" [slWidth]="704">
    <app-role-info [role]="role"></app-role-info>
</sl-modal>