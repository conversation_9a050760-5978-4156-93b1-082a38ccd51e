import { Injectable } from '@angular/core';
import { Module } from 'src/app/shared/models';
import { BaseCURDService } from 'src/app/shared/services/base-curd.service';
import { BaseHttpService } from 'src/app/shared/services/base-http.service';
import { TblRoleinfo } from '../../workspace-shared/models/tbl_roleinfo';

@Injectable({
  providedIn: 'root'
})
export class RoleService extends BaseCURDService<TblRoleinfo> {

  constructor(protected http: BaseHttpService) {
    super(http, 'api/Roleinfo')
  }

  /**
   * 
   * 获取所有的菜单
   * @return {*} 
   * @memberof RoleManageService
   */
  getModuleList(): Promise<Array<Module>> {
    return this.http.get('/api/open/getModuleList')
  }
}
