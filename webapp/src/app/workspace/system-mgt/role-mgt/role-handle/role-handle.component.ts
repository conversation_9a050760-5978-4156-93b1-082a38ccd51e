import { OrganizeService } from './../../../../shared/services/organize.service';
import { NgForm } from '@angular/forms';
import { Component, Host, OnInit, ViewChild, AfterViewInit, Input } from '@angular/core';
import { SlModalComponent } from 'src/app/shared/modules/sl-modal/sl-modal/sl-modal.component';
import { TblRoleinfo } from 'src/app/workspace/workspace-shared/models/tbl_roleinfo';
import { RoleService } from '../role.service';
import { Module } from 'src/app/shared/models/module';

@Component({
  selector: 'app-role-handle',
  templateUrl: './role-handle.component.html',
  styleUrls: ['./role-handle.component.less']
})
export class RoleHandleComponent implements OnInit, AfterViewInit {
  @Input() role: TblRoleinfo = new TblRoleinfo()
  @Input() isEdit: boolean = false;
  @ViewChild(NgForm) validateForm!: NgForm
  // 机构列表
  orgList: Array<any> = []

  // 当前权限菜单
  moduleTreeList: Array<Module> = [];

  constructor(
    @Host() private modal: SlModalComponent,
    private roleService: RoleService,
    private orgSerivce: OrganizeService
  ) { }

  ngAfterViewInit(): void {
    this.modal.form = this.validateForm
  }
  ngOnInit(): void {
    if (this.isEdit && this.role && this.role.id) { // 编辑
      this.moduleTreeList = this.role.moduleList!;
    } else {
      this.roleService.getModuleList().then(res => {
        this.moduleTreeList = res;
      })
    }
    this.getOrganizeList()
  }

  permissionChange($event: Array<Module>) {
    this.role.moduleList = $event;
  }

  /** 获取辖区列表 */
  private getOrganizeList() {
    this.orgSerivce.getOrganize().then((res) => {
      this.orgList = [res]
      this.computedIsLeaf(this.orgList);
    });
  }
  /** 是否时叶子 */
  private computedIsLeaf(res: Array<any>) {
    res.forEach(ele => {
      if (!ele.children || !ele.children.length) {
        ele.isLeaf = true;
      } else {
        this.computedIsLeaf(ele.children)
      }
    })
  }

  private computedSomeIsSelected(list: Module[]) {
    let result: boolean = false;
    for (let i = 0; i < list.length; i++) {
      const ele = list[i];
      if (ele.ifCheck) {
        result = true;
        break;
      } else {
        if (ele.children && ele.children.length) {
          this.computedSomeIsSelected(ele.children);
        }
      }
    }
    return result;
  }
}
