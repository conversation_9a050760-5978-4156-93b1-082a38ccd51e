<form #validateForm="ngForm">
    <table class="sl-table-handle">
        <tr>
            <td class="label required" style="width:100px;">
                <span>角色名称：</span>
            </td>
            <td class="content">
                <input [slValidate]="{required:true,label:'角色名称'}" placeholder="请输入角色名称" type="text" name="roleName"
                    nz-input [(ngModel)]="role.roleName" />
            </td>
        </tr>
        <tr>
            <td class="label required" style="width:100px;">
                <span>所属单位：</span>
            </td>
            <td class="content">
                <nz-tree-select [nzNodes]="orgList" style="width:100%" class="control required"
                    [slValidate]="{required:true,label:'所属单位'}" name="orgId" nzAllowClear nzPlaceHolder="请选择所属单位"
                    [(ngModel)]="role.orgId">
                </nz-tree-select>
            </td>
        </tr>
        <tr>
            <td class="label">
                <span>角色描述：</span>
            </td>
            <td class="content">
                <textarea rows="3" name="description" style="width: 100%;" [(ngModel)]="role.description"></textarea>
            </td>
        </tr>
        <tr>
            <td class="label required">
                <span>角色权限：</span>
            </td>
            <td class="content nest">
                <app-role-permission-tree [height]="350" (permissionChange)="permissionChange($event)"
                    [moduleTreeList]="moduleTreeList">
                </app-role-permission-tree>
            </td>
        </tr>
    </table>
</form>