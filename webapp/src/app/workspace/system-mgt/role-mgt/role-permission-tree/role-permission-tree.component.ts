import { Component, Input, OnInit, OnChanges, SimpleChanges, Output, EventEmitter, AfterViewInit, ViewChild, ElementRef, } from '@angular/core';
import { Module } from 'src/app/shared/models';
@Component({
  selector: 'app-role-permission-tree',
  templateUrl: './role-permission-tree.component.html',
  styleUrls: ['./role-permission-tree.component.less'],
  host: {
    '[style.display]': "'block'",
    '[style.overflow]': "'hidden'",
    '[style.boxSizing]': "'border-box'",
    '[style.width.%]': "100",
    '[style.height.px]': 'height'
  }
})
export class RolePermissionTreeComponent implements OnInit, OnChanges, AfterViewInit {
  @Input() height: number | string = 'auto';
  containerWidth!: number;
  moduleList: Array<any> = [];
  selectedModuleList: Array<any> = [];
  isShowGutter: boolean = false;// 是否显示gutter列（适配滚动条宽度）
  gutterWidth: number = 6; // gutter 6px 预留1px 显示最后一列的线条
  isSelectAll: boolean = false; // 是否全选
  isSomeSelected: boolean = false; // 是否部分选中

  @Input() isAuthConfig: boolean = false; // 是否配置了 ifConfig 属性为true时才能选择
  @Input() allChecked: boolean = false; // 全部选中
  @Input() allExpanded: boolean = false; // 全部展开
  @Input() notSelectabled: boolean = false; // 不可选择的 详细页面使用
  @Input() moduleTreeList: Array<any> = []; // 菜单树（后台返回）

  @Output() permissionChange: EventEmitter<any> = new EventEmitter();

  @ViewChild('tableContainer', { static: true }) tableContainer!: ElementRef<HTMLDivElement>;
  @ViewChild('tableBody', { static: true }) tableBody!: ElementRef<HTMLDivElement>;
  @ViewChild('listTable', { static: true }) listTable!: ElementRef<HTMLTableElement>;
  @ViewChild('listHeaderTable', { static: true }) listHeaderTable!: ElementRef<HTMLTableElement>;
  constructor() { }

  ngOnInit() {

  }
  ngAfterViewInit(): void {
    setTimeout(() => {
      this.computedContainerWidth();
    });
  }
  ngOnChanges(changes: SimpleChanges): void {
    let moduleTreeList = changes['moduleTreeList'] && changes['moduleTreeList'].currentValue
    if (moduleTreeList && moduleTreeList.length) {
      this.moduleList = this.computedModuleList(this.moduleTreeList);
      this.selectedModuleList = this.moduleList.filter(ele => ele.ifCheck);
      if (this.moduleList.length) {
        this.isSelectAll = this.selectedModuleList.length == this.moduleList.length
        if (!this.isSelectAll) {
          this.isSomeSelected = this.selectedModuleList.length > 0;
        } else {
          this.isSomeSelected = false;
        }
      }
      setTimeout(() => {
        this.computedContainerWidth();
      });
    }
  }

  menuNameHasChildren(item: Module) {
    // 4 btn
    return item.children && item.children.length && item.children[0].menuType != '4'
  }

  /**
   * 系统菜单等级
   */
  private computedModuleList(moduleList: Module[], level?: number) {
    let arr: Array<Module> = [];
    if (moduleList && moduleList.length) {
      for (let i = 0; i < moduleList.length; i++) {
        const ele = moduleList[i];
        if (level == undefined) {
          ele['level'] = 0;
          ele.isVisible = 1; // 初始化仅第一级可见
        } else {
          ele['level'] = level + 1;
          ele.isVisible = this.allExpanded ? 1 : 0; // 不可见
        }
        if (ele['level'] == 0) {
          ele.expanded = true; // 展开第一级
        }
        if (ele['level'] == 1) { // 展开第一级
          ele.isVisible = 1;
        }
        arr.push(ele);
        const children = ele['children']
        if (children && children.length) {
          if (this.allExpanded) ele.expanded = this.allExpanded;
          arr = arr.concat(this.computedModuleList(children, ele['level']))
        }
      }
    }
    return arr;
  }

  /**
   *
   * 权限全选/反选
   * @memberof RolePermissionTreeComponent
   */
  selectAll($event: boolean) {
    if (this.notSelectabled) return;
    this.isSelectAll = $event;
    this.isSomeSelected = false;
    this.moduleList.forEach(ele => {
      // if (this.isAuthConfig) {
      //   if (ele.ifConfig) ele.ifCheck = this.isSelectAll;
      // } else {
      //   ele.ifCheck = this.isSelectAll;
      // }
      ele.ifCheck = this.isSelectAll;
    })
    // console.log('selecAll', this.moduleTreeList);
    this.permissionChange.emit(this.moduleTreeList);
  }
  /**
   * 权限选择回调（menuType 1,2,3,4）
   * @param $event boolean
   * @param item module
   */
  checkChange($event: boolean, item: Module) {
    // item['ifUpdate'] = !item['ifUpdate'];
    item.ifCheck = $event;
    if ($event) {
      this.setParentChecked(item);
    } else {
      this.setChildUnChecked(item);
    }
    this.selectedModuleList = this.moduleList.filter(ele => ele.ifCheck);
    this.computedRoleModuleState(this.moduleTreeList, this.selectedModuleList)
    if (this.moduleList.length) {
      this.isSelectAll = this.selectedModuleList.length == this.moduleList.length
      if (!this.isSelectAll) {
        this.isSomeSelected = this.selectedModuleList.length > 0;
      } else {
        this.isSomeSelected = false;
      }
    }
    this.permissionChange.emit(this.moduleTreeList);
  }

  /**
  * 计算当前角色选中的权限
  *
  * @param {Module[]} selectedList
  * @memberof RoleAuthConfigComponent
  */
  private computedRoleModuleState(moduleList: Module[], selectedList: Module[]) {
    if (moduleList && moduleList.length) {
      moduleList.forEach(ele => {
        ele.ifCheck = selectedList.findIndex(e => e.id === ele.id) > -1;
        this.computedRoleModuleState(ele.children!, selectedList)
      })
    }
  }
  /**
   *
   * 让父级选中
   * @private
   * @param {*} item
   * @memberof RolePermissionTreeComponent
   */
  private setParentChecked(item: Module) {
    if (item.parentId) {
      let parentItem = this.moduleList.find(ele => ele.id === item.parentId);
      if (!parentItem.ifCheck) {
        parentItem.ifCheck = true;
        this.setParentChecked(parentItem);
      }
    }
  }

  /**
   *
   * 让子级不选中
   * @private
   * @param {*} item
   * @memberof RolePermissionTreeComponent
   */
  private setChildUnChecked(item: Module) {
    if (item.children && item.children.length) {
      item.children.forEach(ele => {
        ele.ifCheck = false;
        this.setChildUnChecked(ele);
      });
    }
  }


  /**
   * 展开/收缩菜单栏
   * @param item 
   */
  toggleMenu(item: Module) {
    item.expanded = !item.expanded
    item.expanded ? this.expandMenu(item.children!) : this.collapseMenu(item.children!);
    setTimeout(() => {
      this.computedContainerWidth();
    });
  }

  /**
   * 计算容器宽度
   */
  private computedContainerWidth() {
    this.containerWidth = this.tableContainer.nativeElement.offsetWidth;
    this.isShowGutter = this.isShowScroll();
  }

  /**
   * 是否出现滚动条
   */
  private isShowScroll() {
    return this.tableBody.nativeElement.scrollHeight
      > this.tableBody.nativeElement.clientHeight;
  }

  /**
   * 收缩菜单栏
   */
  private collapseMenu(arr: any[]) {
    if (arr && arr.length) {
      arr.forEach(ele => {
        ele['isVisible'] = 0;
        this.collapseMenu(ele.children);
      })
    }
  }

  /**
   * 展开菜单栏
   */
  private expandMenu(arr: any[]) {
    if (arr && arr.length) {
      arr.forEach(ele => {
        ele['isVisible'] = 1;
        if (ele.expanded) { // 如果自身是展开状态，继续展开子级
          this.expandMenu(ele.children);
        }
      })
    }
  }


}
