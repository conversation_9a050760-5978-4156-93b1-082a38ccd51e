@import "/src/styles/mixins";
@border-style:1px solid rgba(215,219,232,1);
.role-permission-tree {
  width: 100%;
  height: 100%;
  overflow: hidden;
  .table-container {
    height: 100%;
    overflow: hidden;
    .table-container__body {
      height: calc(100% - 33px); // 头部高度32px 32+bottom-border 1px
      overflow: hidden;
      position: relative;
      .scrollbars(5px, #909193, #fff);
      .table-container__body__inner {
        overflow-y: auto;
        height: 100%;
      }
    }
    table {
      tr {
        td {
          height: 32px;
          padding: 0;
          white-space: break-spaces;
          border-left: @border-style;
          border-top: @border-style;
          &.checkbox {
            text-align: center;
          }
          &.menu-name {
            .level0.nochild {
              padding-left: 14px;
            }
            & > a {
              color: #333;
              display: inline-flex;
              align-items: center;
              span {
                margin-left: 4px;
              }
            }
          }
          &.last {
            border-right: @border-style;
            padding-left: 12px;
          }
        }
        &.tr-title {
          td {
            background-color: #eef4fe;
            text-align: center;
          }
        }
        &.tr-content:last-child {
          td {
            border-bottom: @border-style;
          }
        }
      }
    }
  }
}
