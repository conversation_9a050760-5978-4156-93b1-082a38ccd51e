<div class="role-permission-tree">
    <div class="table-container" #tableContainer>
        <div class="table-container__title" [style.paddingRight.px]="isShowGutter?5:0">
            <table #listHeaderTable>
                <ng-container [ngTemplateOutlet]="tableCol"></ng-container>
                <tr class="tr-title">
                    <td>系统模块</td>
                    <td class="checkbox">
                        <label [nzDisabled]="notSelectabled" [class.sl-ant-disabled-checkbox]="notSelectabled"
                            name="allSelected" [ngModel]="isSelectAll" [nzIndeterminate]="isSomeSelected" nz-checkbox
                            (ngModelChange)="selectAll($event)"></label>
                    </td>
                    <td class="last">操作权限</td>
                </tr>
            </table>
        </div>
        <div class="table-container__body">
            <div class="table-container__body__inner" #tableBody>
                <table #listTable>
                    <ng-container [ngTemplateOutlet]="tableCol"></ng-container>
                    <ng-container *ngFor="let item of moduleList;index as idx">
                        <tr class="tr-content" *ngIf="item.menuType!='4' && item.isVisible">
                            <td class="menu-name" [ngStyle]="{'paddingLeft':item.level * 18 + 8+'px'}">
                                <a href="javascript:void(0)" (click)="toggleMenu(item)" class="level{{item.level}}"
                                    [ngClass]="menuNameHasChildren(item)?'hover':'nochild'">
                                    <i *ngIf="menuNameHasChildren(item)"
                                        [ngClass]="{'sl-arrow-bottom-16': item.expanded,'sl-arrow-up-16': !item.expanded}">
                                    </i>
                                    <span>{{item.menuName}}</span>
                                </a>
                            </td>
                            <td class="checkbox">
                                <label nz-checkbox [nzDisabled]="notSelectabled"
                                    [class.sl-ant-disabled-checkbox]="notSelectabled" [(ngModel)]="item.ifCheck "
                                    (ngModelChange)="checkChange($event,item)"></label>
                            </td>
                            <td class="last">
                                <ng-container *ngIf="item.children[0]?.menuType == '4'">
                                    <label *ngFor="let subItem of item.children">
                                        <label nz-checkbox [(ngModel)]="subItem.ifCheck "
                                            (ngModelChange)="checkChange($event,subItem)"
                                            [class.sl-ant-disabled-checkbox]="notSelectabled"
                                            [nzDisabled]="notSelectabled">{{subItem.menuName}}
                                        </label></label>
                                </ng-container>
                            </td>
                        </tr>
                    </ng-container>
                </table>
            </div>
        </div>
    </div>
</div>

<ng-template #tableCol>
    <colgroup>
        <col [style.width.px]="containerWidth*.3" />
        <col [style.width.px]="containerWidth*.1" />
        <col [style.width.px]="containerWidth*.6" />
    </colgroup>
</ng-template>