import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { RoleMgtRoutingModule } from './role-mgt-routing.module';
import { RoleListComponent } from './role-list/role-list.component';
import { RoleHandleComponent } from './role-handle/role-handle.component';
import { RoleInfoComponent } from './role-info/role-info.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { FormsModule } from '@angular/forms';
import { RolePermissionTreeComponent } from './role-permission-tree/role-permission-tree.component';
import { NzTreeSelectModule } from 'ng-zorro-antd/tree-select';


@NgModule({
  declarations: [
    RoleListComponent,
    RoleHandleComponent,
    RoleInfoComponent,
    RolePermissionTreeComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    RoleMgtRoutingModule,
    SharedModule,
    NzInputModule,
    NzCheckboxModule,
    NzSelectModule,
    NzTreeSelectModule
  ]
})
export class RoleMgtModule { }
