import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  // 用户模块
  { path: 'user', loadChildren: () => import('./user-mgt/user-mgt.module').then((m) => m.UserMgtModule) },
  // 机构单位模块
  // { path: 'org', loadChildren: () => import('./organization-mgt/organization-mgt.module').then((m) => m.OrganizationMgtModule) },
  // 角色模块
  // { path: 'role', loadChildren: () => import('./role-mgt/role-mgt.module').then((m) => m.RoleMgtModule) },
  // 通知管理
  // { path: 'notice', loadChildren: () => import('./notice-mgt/notice-mgt.module').then((m) => m.NoticeMgtModule) }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class SystemMgtRoutingModule { }
