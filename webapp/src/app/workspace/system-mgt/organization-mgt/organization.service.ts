import { Injectable } from '@angular/core';
import { BaseCURDService } from 'src/app/shared/services/base-curd.service';
import { BaseHttpService } from 'src/app/shared/services/base-http.service';
import { TblOrganize } from 'src/app/workspace/workspace-shared/models/tbl_organize';

@Injectable({
  providedIn: 'root',
})
export class OrganizationService extends BaseCURDService<TblOrganize> {
  constructor(protected http: BaseHttpService) {
    // super(http, 'api/role');
    super(http, '/api/Organize');
  }

  // 得到组织列表得数据
  getOrgList(): Promise<any> {
    return this.http.get('/api/Organize/getOrganize');
  }
  //
}
