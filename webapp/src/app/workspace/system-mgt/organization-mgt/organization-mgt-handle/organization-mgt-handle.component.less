.wrapper {
  width: 455px;
  padding-right: 28px;
  .mgtList {
    tr {
      td {
        text-align:right;
        line-height: 38px;
        input {
          height: 28px;
        }
        span {
          font-family: SourceHanSansCN-Regular;
          font-size: 14px;
          color: #666666;
          letter-spacing: 0;
          font-weight: 400;
        }
      }
      td:last-child {
        width: 341px;
      }
      &:last-child {
        height: 83px;
        vertical-align: top;
        font-family: SourceHanSansCN-Regular;
          font-size: 14px;
          color: #666666;
          letter-spacing: 0;
          font-weight: 400;
      }
    }
  }
  .btnList {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 66px;
    border-top: 1px solid #eff1f6;
  }

}
span::before {
  content: "*";
  color: red;
  width: 10px;
  height: 10px;
  margin-right: 4px;
  display: inline-block;
}
