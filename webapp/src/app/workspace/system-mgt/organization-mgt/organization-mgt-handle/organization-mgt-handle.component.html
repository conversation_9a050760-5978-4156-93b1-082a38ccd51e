<form class="wrapper" #validateForm="ngForm">
  <table class="mgtList">
    <ng-container *ngIf="orgParentBool">
      <tr>
        <td><span>类型：</span></td>
        <td style="text-align:left">
          <nz-radio-group nzSize="small" [(ngModel)]="organize.orgTypeCode" name="nz-radio">
            <label nz-radio nzValue="01">单位</label>
            <label nz-radio nzValue="02">部门</label>
          </nz-radio-group>
        </td>
      </tr>
    </ng-container>


    <tr>
      <ng-container *ngIf="organize.orgTypeCode == '01';else fail">
        <td><span>单位名称：</span></td>
        <td>
          <input placeholder="请输入单位名称" [slValidate]="{required:true,label:'单位名称'}" type="text" name="orgName" nz-input
            [(ngModel)]="organize.orgShort" />
        </td>
      </ng-container>
      <ng-template #fail>
        <td><span>部门名称：</span></td>
        <td>
          <input placeholder="请输入部门名称" [(ngModel)]="organize.orgShort" [slValidate]="{required:true,label:'部门名称'}"
            type="text" name="orgName" nz-input />
        </td>
      </ng-template>

    </tr>
    <ng-container *ngIf="orgParentBool">
      <tr>
        <td><span>上级单位：</span></td>
        <td>
          <nz-tree-select nzPlaceHolder="请选择上级单位" [nzNodes]="organize.orgTypeCode == '01'? nodesUnit : nodesDept"
            nzDefaultExpandAll [(ngModel)]="organize.parentId" name="organize.parentId"
            [slValidate]="{required:true,label:'上级单位'}" style="width: 341px;text-align: left;" nzVirtualHeight="150px">
          </nz-tree-select>
        </td>
      </tr>
    </ng-container>
    <tr>
      <td>负责人：</td>
      <td>
        <!--[slValidate]="{required:true,label:'负责人'}"-->
        <input [(ngModel)]="organize.leader" placeholder="请输入负责人" type="text" name="leader" nz-input />
      </td>
    </tr>
    <tr>
      <td>联系方式：</td>
      <td>
        <!--[slValidate]="{required:true,label:'联系方式',type:'phone',errorTip:'联系方式输入错误'}" maxlength="11" minlength="11"-->
        <input [(ngModel)]="organize.officePhone" placeholder="请输入联系方式" type="text" name="contact" nz-input />
      </td>
    </tr>
    <tr>
      <td>备注：</td>
      <td>
        <textarea [(ngModel)]="organize.remark" type="text" nz-input placeholder="请输入备注" nz-input
          style="height: 72px;padding-bottom: 50px;" name="remark"></textarea>
    </tr>
  </table>
</form>