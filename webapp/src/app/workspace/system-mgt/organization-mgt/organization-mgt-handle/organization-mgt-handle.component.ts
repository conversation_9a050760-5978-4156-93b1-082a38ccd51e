import { filter } from 'rxjs/operators';
import { element } from 'protractor';
import { OrganizationService } from './../organization.service';
import {
  Component,
  OnInit,
  ViewChild,
  AfterViewInit,
  Host,
  Input,
} from '@angular/core';
import { SlModalComponent } from 'src/app/shared/modules/sl-modal/sl-modal/sl-modal.component';
import { NgForm } from '@angular/forms';
import { TblOrganize } from 'src/app/workspace/workspace-shared/models/tbl_organize';
import * as _ from 'lodash';
@Component({
  selector: 'app-organization-mgt-handle',
  templateUrl: './organization-mgt-handle.component.html',
  styleUrls: ['./organization-mgt-handle.component.less'],
})
export class OrganizationMgtHandleComponent implements OnInit, AfterViewInit {
  @ViewChild(NgForm) validateForm!: NgForm;
  @Input() organize: TblOrganize = new TblOrganize();
  @Input() isEdit: boolean = false; // 判断编辑或新增 true 编辑 false 新增 区分 有无上级之
  // 展示的上级单位数据
  leader: TblOrganize = new TblOrganize();
  nodesDept: Array<any> = []; // 上级部门数据列表
  // 上级单位数据列表
  nodesUnit: Array<any> = [];
  oldNodesOrg: Array<any> = [];
  newOrgTypeCode: string = '01';
  // 筛选的只剩单位的列表
  result: Array<any> = [];
  constructor(
    @Host() private modal: SlModalComponent,
    private service: OrganizationService
  ) { }
  get orgParentBool(): boolean {
    if (!this.isEdit) return true;
    return this.organize && this.organize.parentId !== undefined
  }
  ngAfterViewInit(): void {
    this.modal.form = this.validateForm;
  }
  ngOnInit(): void {
    this.getList();
    if (this.isEdit) return;
    this.organize.orgTypeCode = '01'; // 新增状态下 默认选中radio
  }
  private getList() {
    this.service.getOrgList().then((res: TblOrganize) => {
      this.leader = res;
      this.nodesDept = [this.leader];
      this.oldNodesOrg = [_.cloneDeep(res)];

      this.newRecursion(this.nodesDept, 'dept');

      if (!this.oldNodesOrg) return;
      this.nodesUnit = this.newRecursion(this.oldNodesOrg, 'unit');
      this.comIsleaf(this.nodesUnit)  // 重新对单位数据叶子进行判断
      this.comIsleaf(this.nodesDept)  // 重新对单位数据叶子进行判断
    });
  }
  // private recursion(data: Array<any>) {
  //   for (let i = 0; i < data.length; i++) {
  //     data[i].key = data[i].id;
  //     if (data[i].orgTypeCode == '02') {
  //       data.splice(i, 1);
  //       i--;
  //     }
  //     if (data[i]?.children.length == 0) {
  //       data[i].isLeaf = true;
  //     }
  //     if (data[i]?.children) {
  //       data[i].children = this.recursion(data[i].children!);
  //     }
  //   }
  //   return data;
  // }

  // private computedIsLeaf(list: Array<any>) {
  //   list.forEach((ele) => {
  //     if (!ele.children || !ele.children.length) {
  //       ele.isLeaf = true;
  //     } else {
  //       this.computedIsLeaf(ele.children);
  //     }
  //   });
  // }
  // 2.第二种方式
  // private filterType(item: TblOrganize) {
  //   const items = [item];
  //   let func = (item: TblOrganize) => {
  //     if (item.children && item.children.length) {
  //       item.children = item.children.filter(func);
  //       return true;
  //     } else if (item.orgTypeCode) {
  //       return item.orgTypeCode === '01';
  //     } else {
  //       return false;
  //     }
  //   };
  //   return items.filter(func);
  // }

  private newRecursion(s: Array<any>, type: 'unit' | 'dept') {
    for (let i = 0; i < s.length; i++) {
      // 单位 对单位筛选 02单位 去除
      if (type == 'unit') {
        s[i].key = s[i].id;
        if (s[i].orgTypeCode == '02') {
          s.splice(i, 1);
          i--;
        }
      }
      // debugger
      // 叶子节点判断
      if (s[i]?.children.length == 0) {
        s[i].isLeaf = true;
      }
      // 若是编辑状态下
      if (this.isEdit) {
        if (this.organize && s[i]?.title === this.organize.orgShort) {
          s.splice(i, 1)
          i--;
          continue;
        }
      }
      if (s[i]?.children) {
        s[i].children = this.newRecursion(s[i].children!, type);
      }
    }
    return s;
  }

  // 重新对数据是否叶子判断 用于单位数据
  private comIsleaf(u: Array<any>) {
    u.forEach(ele => {
      if (!ele.children || !ele.children.length) {
        ele.isLeaf = true;
      } else {
        this.comIsleaf(ele.children)
      }
    })
  }
}
