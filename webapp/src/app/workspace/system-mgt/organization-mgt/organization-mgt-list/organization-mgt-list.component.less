.o-list-page {
  padding-bottom: 56px;

  &>.title {
    width: 100%;
    height: 48px;
    border-bottom: 1px solid #eceef4;
    display: flex;
    align-items: center;
    font-size: 16px;
    color: #333333;
    font-weight: 500;

    i+span {
      margin-left: 6px;
    }
  }
}

.list-table-cotent {
  padding: 0 8px;
  margin-bottom: 15px;
}

.showBox {
  transition: transform 0.5s;
  -moz-transition: -moz-transform 0.5s;
  /* Firefox 4 */
  -webkit-transition: -webkit-transform 0.5s;
  /* Safari 和 Chrome */
  -o-transition: -o-transform 0.5s;
  transform: rotate(180deg);
}

.closeBox {
  transition: transform 0.5s;
}

.head-handle-area {
  height: 76px;
  display: flex;
  align-items: center;
  flex-direction: row;
  justify-content: flex-end;
  padding: 0 4px;

  button+button {
    margin-left: 10px;
  }
}

.btn-list {
  display: flex;
  justify-content: space-around;
}

app-org-tree-item {
  display: table-row;
}

.title-table {
  width: 1854px;
  background-color: #EEF4FE;
  line-height: 36px;
  font-size: 14px;
  color: #333333;
  font-weight: 400;
  text-align: center;
  border: 1px solid #D7DBE8;

  td {
    width: 370.59px;
    border-right: 1px solid #D7DBE8;
  }
}
