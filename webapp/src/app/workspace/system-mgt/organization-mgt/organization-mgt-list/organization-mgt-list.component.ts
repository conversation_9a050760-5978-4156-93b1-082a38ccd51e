import { Component, OnInit, ViewChild } from '@angular/core';
import { BtnEventOption, Page } from 'src/app/shared/models';
import { SlModalService } from 'src/app/shared/modules/sl-modal/sl-modal.service';
import { SpinService } from 'src/app/shared/services/spin.service';
import { OrganizationService } from '../organization.service';
import { TblOrganize } from 'src/app/workspace/workspace-shared/models/tbl_organize';

@Component({
  selector: 'app-organization-mgt-list',
  templateUrl: './organization-mgt-list.component.html',
  styleUrls: ['./organization-mgt-list.component.less'],
})
export class OrganizationMgtListComponent implements OnInit {
  constructor(
    private service: OrganizationService,
    private slModalService: SlModalService,
    private spinService: SpinService
  ) {}
  // children级
  @ViewChild('itemTree')
  page: Page<any> = new Page();
  organize: TblOrganize = new TblOrganize();
  handleOrganize!: TblOrganize;
  detailorganize!: TblOrganize;
  handleVisible: boolean = false;
  infoVisible: boolean = false;
  ifSpinning: boolean = true;
  organizeList: Array<TblOrganize> = [];
  isEdit: boolean = false;
  ngOnInit() {
    this.getOrgList();
  }
  getInfo(id: string, _organize: TblOrganize, cb?: () => any) {
    // this.spinService.showText();
    this.service
      .getInfo(id)
      .then((resOrganize: TblOrganize) => {
        // this.spinService.close();
        _organize = resOrganize;
        cb && cb();
      })
      .catch((err) => {
        this.spinService.close();
      });
  }

  getOrgList() {
    this.service
      .getOrgList()
      .then((res) => {
        this.organize = res;
        this.organizeList = [res];
        this.ifSpinning = false;
      })
      .catch((err) => {
        this.spinService.close();
      });
  }
  showHandle(): void {
    this.handleVisible = true;
    this.isEdit = false;
    this.handleOrganize = new TblOrganize();
  }

  save() {
    this.service
      .save(this.handleOrganize)
      .then((res) => {
        this.spinService.close();
        if (res) {
          this.handleVisible = false;
          this.getOrgList();
        }
      })
      .catch((err) => {
        this.spinService.close();
        this.slModalService.openPromptModal({
          type: 'error',
          content: err && '保存失败',
        });
      });
    // this.service.save(this.role).then(res => {
    //   this.spinService.close()
    //   if (res) {
    //     this.handleVisible = false
    //     const refreshCb = this.bListPageModel.refreshCb;
    //     if (refreshCb) {
    //       refreshCb(this.bListPageModel.search)
    //     }
    //   }
    // }).catch(err => {
    //   this.spinService.close()
    // })
  }
  onInfoItem(item: TblOrganize) {
    this.infoVisible = true;
    this.detailorganize = item;
  }
  onEditItem(item: TblOrganize) {
    this.service.getInfo(item.id!).then((resOrganize: TblOrganize) => {
      this.handleVisible = true;
      this.isEdit = true;
      this.handleOrganize = resOrganize;
    });
  }
  onDeleteItem(item: TblOrganize) {
    this.slModalService.openPromptModal({
      type: 'confirm',
      content: '确定删除吗？',
      okCb: () => {
        this.service
          .delete(item.id!)
          .then(() => {
            this.slModalService.openPromptModal({
              type: 'success',
              content: '删除成功！',
              okCb: () => {
                this.getOrgList();
              },
            });
          })
          .catch((err) => {
            this.slModalService.openPromptModal({
              type: 'error',
              content: err || '删除失败！',
            });
          });
      },
    });
  }
}
