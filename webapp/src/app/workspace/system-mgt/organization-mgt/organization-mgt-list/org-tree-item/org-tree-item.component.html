<tr class="trwrapper">
  <app-org-tree-td [org]="item" [level]="level" (onClickItem)="clickItem($event)" (onInfoItem)="toInfoItem($event)"
    (onEditItem)="toEditItem($event) " (onDeleteItem)="toDeleteItem($event)">
  </app-org-tree-td>
</tr>
<ng-container *ngIf="open">
  <ng-container *ngFor="let subItem of item.children; index as idx">
    <app-org-tree-item [item]="subItem" [level]="level + 1" (onClickItem)="clickItem($event)"
      (onInfoItem)="toInfoItem($event)" (onEditItem)="toEditItem($event)" (onDeleteItem)="toDeleteItem($event)">
    </app-org-tree-item>
  </ng-container>
</ng-container>
