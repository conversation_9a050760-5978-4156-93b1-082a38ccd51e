import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
  selector: 'app-org-tree-item',
  templateUrl: './org-tree-item.component.html',
  styleUrls: ['./org-tree-item.component.less'],
})
export class OrgTreeItemComponent implements OnInit {
  @Input() item: any = {};
  // 记住当前级别
  @Input() level: number = 1;
  // 选中的节点
  @Output() onClickItem: EventEmitter<any> = new EventEmitter();

  @Output() onInfoItem: EventEmitter<any> = new EventEmitter();
  @Output() onEditItem: EventEmitter<any> = new EventEmitter();
  @Output() onDeleteItem: EventEmitter<any> = new EventEmitter();

  open: boolean = true;
  constructor() {}
  ngOnInit(): void {}
  // 发给最外层
  clickItem(event: any) {
    this.open = event;
  }
  toInfoItem(event: MouseEvent) {
    this.onInfoItem.emit(event);
  }
  toEditItem(event: MouseEvent) {
    this.onEditItem.emit(event);
  }
  toDeleteItem(event: MouseEvent) {
    this.onDeleteItem.emit(event);
  }
}
