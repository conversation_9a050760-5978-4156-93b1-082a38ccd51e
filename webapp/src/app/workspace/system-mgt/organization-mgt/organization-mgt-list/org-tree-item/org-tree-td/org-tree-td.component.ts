import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { stopBubble, stopDefault } from 'src/app/shared/utils';
import { TblOrganize } from 'src/app/workspace/workspace-shared/models/tbl_organize';

@Component({
  selector: 'app-org-tree-td',
  templateUrl: './org-tree-td.component.html',
  styleUrls: ['./org-tree-td.component.less'],
})
export class OrgTreeTdComponent implements OnInit {
  @Input() org: TblOrganize = new TblOrganize();
  @Input() level: number = 0;
  open: boolean = true;
  // 子节点是否展开
  @Output() onClickItem: EventEmitter<any> = new EventEmitter();

  @Output() onInfoItem: EventEmitter<any> = new EventEmitter();
  @Output() onEditItem: EventEmitter<any> = new EventEmitter();
  @Output() onDeleteItem: EventEmitter<any> = new EventEmitter();

  constructor() {}
  ngOnInit() {}
  get isFolder() {
    return this.org.children && this.org.children.length;
  }

  toggle() {
    if (this.isFolder) {
      this.open = !this.open;
    }
    this.onClickItem.emit(this.open);
  }

  toInfo(event: MouseEvent) {
    const selNode = this.getSelectedNode(event);
    this.onInfoItem.emit(selNode);
  }

  toEdit(event: MouseEvent) {
    const selNode = this.getSelectedNode(event);
    this.onEditItem.emit(selNode);
  }

  toDelete(event: MouseEvent) {
    const selNode = this.getSelectedNode(event);
    this.onDeleteItem.emit(selNode);
  }

  private getSelectedNode(event: MouseEvent) {
    let selNode: any;
    if (event instanceof MouseEvent) {
      stopBubble(event);
      stopDefault(event);
      selNode = this.org;
    } else {
      selNode = event;
    }
    return selNode;
  }
}
