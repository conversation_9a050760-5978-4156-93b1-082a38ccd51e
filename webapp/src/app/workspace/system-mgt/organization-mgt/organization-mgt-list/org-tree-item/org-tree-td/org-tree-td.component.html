<td style="text-align: left;">
  <div style="width: 357.60px; " [style.paddingLeft.px]="(level-1)*20 +96">
    <ng-container *ngIf=" isFolder;else space16">
      <a href="javascript:void(0)" (click)="toggle()">
        <!-- <i class="sl-caret-up-16"></i> -->
        <i class="sl-caret-up-16" [class]="open? 'showBox': 'closeBox'"></i>
      </a>

    </ng-container>
    {{org.orgShort}}
  </div>
</td>
<td>
  <div class="content">
    {{org.leader}}
  </div>
</td>
<td>
  <div class="content">
    {{org.officePhone}}
  </div>
</td>
<td>
  <div class="content" title="{{org.remark}}">
    <div class="remark">{{org.remark}}</div>
  </div>
</td>
<td>
  <div class="content contenBtn">
    <div class="btnst">
      <a href="javascript:void(0)" style="color: #2BB7A6;" (click)="toInfo($event)" *slHasAnyAuthority="'3.1.4'">查看</a>
      <a href="javascript:void(0)" style="color: #2D74EF;" (click)="toEdit($event)" *slHasAnyAuthority="'3.1.2'">编辑</a>
      <a href="javascript:void(0)" style="color: #ED1313;" (click)="toDelete($event)" *slHasAnyAuthority="'3.1.3'"
        [hidden]="!org.parentId">删除</a>
      <!-- 占位 不用占位样式调整  边距不太居中-->
      <a style="color: transparent;cursor: default;" *slHasAnyAuthority="'3.1.3'"
        [hidden]="org.parentId">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</a>
    </div>
  </div>
</td>

<ng-template #space16>
  <div style="width: 16px;display: inline-block;"></div>
</ng-template>
