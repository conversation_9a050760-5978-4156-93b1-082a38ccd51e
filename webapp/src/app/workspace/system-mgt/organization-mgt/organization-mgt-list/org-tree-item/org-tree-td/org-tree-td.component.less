.content {
  width: 357.60px;
}

td {
  box-sizing: border-box;
  border-right: 1px solid #d7dbe8;
  text-align: center;
  line-height: 36px;


}

.remark {
  width: 320px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin: 0 auto;
}

.contenBtn {
  display: flex;
  align-items: center;
  justify-content: center;

  .btnst {
    display: flex;
    align-items: center;

    a {
      padding: 0 20px;
    }
  }
}

.showBox {
  transition: transform 0.5s;
  -moz-transition: -moz-transform 0.5s;
  /* Firefox 4 */
  -webkit-transition: -webkit-transform 0.5s;
  /* Safari 和 Chrome */
  -o-transition: -o-transform 0.5s;
  transform: rotate(180deg);
}

.closeBox {
  transition: transform 0.5s;
}
