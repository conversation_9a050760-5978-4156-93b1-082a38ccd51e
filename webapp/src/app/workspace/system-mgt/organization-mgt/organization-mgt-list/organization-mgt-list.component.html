<div class="o-list-page">
  <div class="title">
    <i class='sl-location-16'></i>
    <span>单位管理</span>
  </div>
  <div class="list-table-cotent">
    <nz-spin nzTip="数据加载中,请稍后..." [nzSpinning]="ifSpinning">
    </nz-spin>
    <div class="head-handle-area">
      <button sl-button slType="primary" (click)="showHandle()" *slHasAnyAuthority="'3.1.1'">
        <i class='sl-plus-16'></i>
        新增
      </button>
    </div>
    <table class="sl-table-list" cellspacing="0" cellpadding="0">
      <div class="title-table">
        <tr>
          <td>
            单位名称
          </td>
          <td>
            负责人
          </td>
          <td>
            联系方式
          </td>
          <td>
            备注
          </td>
          <td style="border: none;">
            操作
          </td>
        </tr>
      </div>
      <ng-container *ngFor="let org of organizeList;index as idx">
        <tbody class="t-content">
          <app-org-tree-item [item]="org" (onInfoItem)="onInfoItem($event)" (onEditItem)="onEditItem($event)"
            (onDeleteItem)="onDeleteItem($event)" [level]="idx"></app-org-tree-item>
        </tbody>
      </ng-container>
    </table>


    <sl-modal *ngIf="handleVisible" [slTitle]="isEdit?'单位（编辑）': '单位（新增）'" [(slVisible)]="handleVisible" [slWidth]="455"
      (onOk)="save()">
      <app-organization-mgt-handle [organize]="handleOrganize" [isEdit]="isEdit"></app-organization-mgt-handle>
    </sl-modal>


    <sl-modal *ngIf="infoVisible" [slFooter]="false" [slTitle]="'单位（详细）'" [(slVisible)]="infoVisible" [slWidth]="455">
      <app-organization-mgt-detail [organize]="detailorganize"></app-organization-mgt-detail>
    </sl-modal>
