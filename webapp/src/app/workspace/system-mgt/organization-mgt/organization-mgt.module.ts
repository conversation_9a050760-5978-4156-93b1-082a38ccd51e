import { SlTableModule } from './../../../shared/modules/sl-table/sl-table.module';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { SlButtonModule } from './../../../shared/modules/sl-button/sl-button.module';
import { OrganizationMgtHandleComponent } from './organization-mgt-handle/organization-mgt-handle.component';
import { OrganizationMgtDetailComponent } from './organization-mgt-detail/organization-mgt-detail.component';
import { OrganizationMgtListComponent } from './organization-mgt-list/organization-mgt-list.component';
// 树形结构
import { OrgTreeItemComponent } from './organization-mgt-list/org-tree-item/org-tree-item.component';
import { OrgTreeTdComponent } from './organization-mgt-list/org-tree-item/org-tree-td/org-tree-td.component';
import { NzTreeSelectModule } from 'ng-zorro-antd/tree-select';

import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzTreeModule } from 'ng-zorro-antd/tree';

import { OrganizationMgtRoutingModule } from './organization-mgt-routing.module';
import { FormsModule } from '@angular/forms';
import { SharedModule } from 'src/app/shared/shared.module';
import { NzTableModule } from 'ng-zorro-antd/table';

// layout

@NgModule({
  declarations: [
    OrganizationMgtDetailComponent,
    OrganizationMgtHandleComponent,
    OrganizationMgtListComponent,
    OrgTreeItemComponent,
    OrgTreeTdComponent,
  ],
  imports: [
    CommonModule,
    OrganizationMgtRoutingModule,
    NzInputModule,
    SlButtonModule,
    NzRadioModule,
    FormsModule,
    NzSelectModule,
    SharedModule,
    NzSpinModule,
    SlTableModule,
    NzTreeModule,
    NzTableModule,
    NzTreeSelectModule,
  ],
})
export class OrganizationMgtModule {}
