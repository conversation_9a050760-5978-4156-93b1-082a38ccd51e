.login-container {
  width: 100%;
  height: 100%;
  position: fixed;
  background: url("/assets/img/login/login-bg-image.png") no-repeat;
  background-attachment: fixed;
  background-size: cover;
  -webkit-background-size: cover;
  -o-background-size: cover;
  top: 0;
  left: 0;
  background-position-y: bottom;
  .login-wrapper {
    position: absolute;
    right: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-width: 50%;
  }
  .title {
    width: 498px;
    height: 40px;
    margin-top: -30px;
    margin-bottom: 40px;
    img {
      width: 100%;
      max-height: 100%;
    }
  }

  .login-content {
    width: 380px;
    border-radius: 8px;
    background: #fff;
    .login-content__tabs {
      height: 56px;
      display: flex;
      flex-direction: row;
      justify-content: space-around;
      align-items: center;
      width: 100%;
      border-bottom: 1px solid #f4f8fe;
      background-repeat: no-repeat;
      // &.phone-tab {
      //   background-image: url("/assets/img/login/username-selected.png");
      // }
      // &.username-tab {
      //   background-image: url("/assets/img/login/phone-selected.png");
      // }
      .tab {
        width: 50%;
        height: 100%;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        a {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: 18px;
          font-size: 18px;
          color: #122657;
          font-family: SourceHanSansCN-Medium;
          font-weight: 500;
        }
        &.active > a {
          color: #2d74ef;
        }
      }
    }
  }
}

@import "./login.component.media";
