import { Component, OnIni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, AfterViewInit } from '@angular/core';
import { DynamicComponent, LoginUser } from 'src/app/shared/models';
import { LoginFormUsernameComponent } from './login-form-username/login-form-username.component';
import { LoginFormPhoneComponent } from './login-form-phone/login-form-phone.component';

interface LoginTab {
  title: string,
  dynamic: DynamicComponent,
  className: string
}

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.less'],
})
export class LoginComponent implements OnInit, OnDestroy, AfterViewInit {
  currentIndex: number
  loginUser: LoginUser = new LoginUser()
  tabList: Array<LoginTab> = [
    {
      title: '手机号登录',
      className: 'phone-tab',
      dynamic: {
        name: 'LoginFormPhone', component: LoginFormPhoneComponent,
        data: {
          params: {
            loginUser: this.loginUser
          }
        }
      },
    },
    {
      title: '账号登录',
      className: 'username-tab',
      dynamic: {
        name: 'LoginFormUsername', component: LoginFormUsernameComponent, data: {
          params: {
            loginUser: this.loginUser
          }
        }
      }
    }
  ]
  // 已经加载的动态组件
  dynamicList: Array<DynamicComponent>

  get currentTab() {
    return this.tabList[this.currentIndex]
  }

  constructor() {
    this.currentIndex = 0
    this.dynamicList = []
  }

  ngOnDestroy(): void {
  }

  ngAfterViewInit(): void {
  }

  ngOnInit(): void {
    const dynamic = this.tabList[this.currentIndex].dynamic
    this.dynamicList.push(dynamic)
  }
  /**
    * 切换标签页并加载动态组件
    * 
    * @param index 
    */
  switchTab(index: number) {
    if (this.currentIndex != index) {
      const dynamic = this.tabList[index].dynamic
      const isExist = this.dynamicList.some(ele => dynamic.name == ele.name)
      // 如果当前组件已经加载，做一个显示处理
      if (!isExist) {
        this.dynamicList.push(dynamic)
      }
      // 更新index
      this.currentIndex = index
    }
  }
}