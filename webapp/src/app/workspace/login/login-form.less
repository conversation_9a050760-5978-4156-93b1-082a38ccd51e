.login-content__form {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  padding: 24px 0 60px 0;
  .input-group {
    position: relative;
    width: 300px;
    height: 48px;
    display: flex;
    align-items: center;
    margin-top: 30px;
    .prefix {
      height: 48px;
      width: 48px;
      background: #2d74ef;
      border-radius: 3px;
      position: absolute;
      left: 0;
      top: 0;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    input {
      width: 100%;
      height: 40px;
      background: #ffffff;
      border: 1px solid rgba(213, 222, 234, 1);
      border-radius: 3px;
      font-size: 16px;
      color: #333333;
      letter-spacing: 0;
      font-weight: 400;
      padding-left: 58px;
      &,
      &:focus {
        outline: none;
        border: 1px solid rgba(14, 58, 221, 0.33);
      }
    }
    .suffix {
      position: absolute;
      right: 8px;
      cursor: pointer;
    }
  }
  .input-group__tip {
    width: 300px;
    display: flex;
    align-items: center;
    padding-left: 50px;
    color: #e83838;
    visibility: hidden;
    i {
      margin-right: 6px;
    }
    &.visible {
      visibility: visible;
    }
  }
  .login-submit {
    width: 300px;
    margin-top: 40px;
    & > button {
      height: 40px;
      width: 100%;
      border-radius: 4px;
      font-size: 16px;
      color: #ffffff;
      font-weight: 500;
      background: #2d74ef;
      &:disabled {
        opacity: 0.5;
      }
    }
  }
}
