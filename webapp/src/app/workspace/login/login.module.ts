import { FormsModule } from '@angular/forms';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { LoginRoutingModule } from './login-routing.module';
import { LoginComponent } from './login.component';
import { LoginFormPhoneComponent } from './login-form-phone/login-form-phone.component';
import { LoginFormUsernameComponent } from './login-form-username/login-form-username.component';
import { LoginVerifyCodeComponent } from './login-verify-code/login-verify-code.component';
import { SharedModule } from 'src/app/shared/shared.module';
@NgModule({
  declarations: [LoginComponent, LoginFormPhoneComponent, LoginFormUsernameComponent, LoginVerifyCodeComponent],
  imports: [CommonModule, LoginRoutingModule, FormsModule, SharedModule],
})

export class LoginModule { }
