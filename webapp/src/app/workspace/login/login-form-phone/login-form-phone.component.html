<form action="" #loginForm="ngForm">
    <div class="login-content__form">
        <div class="input-group">
            <div class="prefix">
                <i class="sl-phone-24"></i>
            </div>
            <input type="text" name="username" #usernameInput="ngModel" [(ngModel)]="loginUser.username" name="username"
                [(ngModel)]="loginUser.username" [slValidate]="phoneValidate" placeholder="请输入手机号" minlength="11"
                maxlength="11" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')" (keydown)="tipMsg=''" />
        </div>
        <div class="input-group__tip" [class.visible]="usernameInput.touched && usernameInput.invalid">
            <i class="sl-warn-red-14"></i>
            <span>手机号输入错误，请重新输入</span>
        </div>
        <div class="input-group">
            <div class="prefix">
                <i class="sl-message-24"></i>
            </div>
            <input #codeElement #codeInput="ngModel" maxlength="4" minlength="4"
                onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')" type="text" name="code" [(ngModel)]="loginUser.code"
                autocomplete="off" placeholder="请输入验证码" [slValidate]="codeValidate" />
            <a class="verify-text" href="javascript:void(0)" (click)="getPhoneCode(usernameInput)">
                {{verifyCodeText}}
            </a>
        </div>
        <div class="input-group__tip" [class.visible]="!!tipMsg">
            <i class="sl-warn-red-14"></i>
            <span>{{tipMsg}}</span>
        </div>
        <div class="login-submit">
            <button class="sl-default-btn" [disabled]="usernameInput.invalid||codeInput.invalid" (click)="login()"
                type="submit">{{loginText}}</button>
        </div>
    </div>
</form>