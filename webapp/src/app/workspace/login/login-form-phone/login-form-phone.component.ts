import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { NgModel } from '@angular/forms';
import { DynamicComponentData, LoginUser } from 'src/app/shared/models';
import { SlValidateOption } from 'src/app/shared/modules/sl-validate';
import { VALIDATE_TIPS, SEND_CODE_TIMER } from '../login';
import { LoginService } from '../login.service';

@Component({
  selector: 'app-login-form-phone',
  templateUrl: './login-form-phone.component.html',
  styleUrls: ['../login-form.less', './login-form-phone.component.less']
})
export class LoginFormPhoneComponent implements OnInit {
  @Input() data: DynamicComponentData = new DynamicComponentData()
  loginUser: LoginUser = new LoginUser()
  phoneValidate: SlValidateOption = { required: true, type: 'phone', errorTip: '手机号输入错误，请重新输入' }
  codeValidate: SlValidateOption = { required: true, label: '手机验证码', maxLen: 4, minLen: 4 }
  // 剩余多少秒可以发送验证码
  timeRemaining: number = 0
  // 提示语
  tipMsg: string = ''
  // 登录状态
  // verifyCodeText
  verifyCodeText: string = '获取验证码'
  //loginText
  loginText: string = '登录'

  @ViewChild('codeElement', { static: false })
  codeElement!: ElementRef<HTMLInputElement>;

  constructor(private loginService: LoginService) { 
    const msg = (event: MessageEvent<any>) => {
      const data = event.data
      if (data && event.data.type !== 'webpackOk' && data.username) {
        // console.log('login component Received message:', event.data);
        const { username, password, code } = data
        this.loginUser.username = username
        if (password) {
          this.loginUser.password = password
        }
        if (code) {
          this.loginUser.code = code
        }
        // remove 
        window.removeEventListener('message', msg)
      }
    }
    window.addEventListener('message', msg)
  }

  ngOnInit(): void {
    if (this.data && this.data.params) {
      const { loginUser } = this.data.params
      if (loginUser) this.loginUser = loginUser
    }
  }
  login() {
    this.loginText = '登录中...'
    this.loginService.login(this.loginUser.username!, this.loginUser.code!, '0')
      .then(res => {
        this.loginText = '登录'
      }).catch(err => {
        this.loginText = '登录'
        this.tipMsg = err
      })
  }
  getPhoneCode(usernameInput: NgModel) {
    if (usernameInput.invalid || this.timeRemaining > 0) return;
    this.timeRemaining = SEND_CODE_TIMER;
    this.verifyCodeText = `验证码获取中`
    this.loginService
      .getPhoneVerificationCode(this.loginUser.username!)
      .then(() => {
        // 验证码获取成功，验证码输入框获取焦点
        if (this.codeElement) this.codeElement.nativeElement.focus()
        this.tipMsg = VALIDATE_TIPS.CODE_HAS_SEND
        let interval = setInterval(() => {
          this.timeRemaining--;
          this.verifyCodeText = `${this.timeRemaining}s后可获取`
          // 60s 计时完成后，清除计时器
          if (this.timeRemaining < 1) {
            clearInterval(interval);
            this.verifyCodeText = '获取验证码'
          }
        }, 1000);
      })
      .catch((err) => {
        this.loginUser.code = '';
        this.tipMsg = err || VALIDATE_TIPS.CODE_SEND_ERROR;
        this.timeRemaining = 0; // 不用计时
        this.verifyCodeText = '获取验证码'
      });
  }

}
