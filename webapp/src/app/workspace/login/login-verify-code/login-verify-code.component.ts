import { Component, ElementRef, Input, OnInit } from '@angular/core';
import { createCode, randomColor, randomNum } from 'src/app/shared/utils';

@Component({
  selector: 'app-login-verify-code',
  templateUrl: './login-verify-code.component.html',
  styleUrls: ['./login-verify-code.component.less']
})
export class LoginVerifyCodeComponent implements OnInit {
  @Input() canvasWidth: number = 95
  @Input() canvasHeight: number = 38
  @Input() codeSize: number = 4;

  verifyCode: string = '';

  // cavas
  private el: HTMLCanvasElement


  constructor(private elementRef: ElementRef) {
    this.el = this.elementRef.nativeElement
  }

  ngOnInit(): void {
    this.drawVerifyCode()
  }

  /**
   *
   * 绘制验证码
   * @memberof LoginVerifyCodeComponent
   */
  drawVerifyCode() {
    const ctx = this.el.getContext('2d')!;
    ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);
    this.verifyCode = createCode(this.codeSize);
    ctx.textBaseline = 'bottom';
    // 绘制背景
    ctx.fillStyle = randomColor(200, 240);
    ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
    // 绘制文字
    for (let i = 0; i < this.codeSize; i++) {
      this.drawText(ctx, this.verifyCode[i], i);
    }
    this.drawLine(ctx);
    this.drawPoint(ctx);
  }

  /**
   *
   * 绘制文本
   * @private
   * @param {CanvasRenderingContext2D} ctx
   * @param {string} txt
   * @param {number} i
   * @memberof LoginVerifyCodeComponent
   */
  private drawText(ctx: CanvasRenderingContext2D, txt: string, i: number) {
    ctx.fillStyle = randomColor(0, 255);
    ctx.font = `${randomNum(28, 40)}px SimHei`;
    let x = (i + 1) * (this.canvasWidth / (this.codeSize + 2));
    let y = randomNum(40, this.canvasHeight - 5);
    let deg = randomNum(-30, 30);
    // 修改坐标原点和旋转角度
    ctx.translate(x, y);
    ctx.rotate((deg * Math.PI) / 270);
    ctx.fillText(txt, 0, 0);
    // 恢复坐标原点和旋转角度
    ctx.rotate((-deg * Math.PI) / 270);
    ctx.translate(-x, -y);
  }

  /**
   *
   * 绘制干扰点
   * @private
   * @param {CanvasRenderingContext2D} ctx
   * @memberof LoginVerifyCodeComponent
   */
  private drawPoint(ctx: CanvasRenderingContext2D) {
    for (let i = 0; i < this.canvasWidth / 2; i++) {
      ctx.fillStyle = randomColor(0, 255);
      ctx.beginPath();
      ctx.arc(
        randomNum(0, this.canvasWidth),
        randomNum(0, this.canvasHeight),
        1,
        0,
        2 * Math.PI
      );
      ctx.fill();
    }
  }


  /**
   *
   * 绘制干扰线
   * @private
   * @param {CanvasRenderingContext2D} ctx
   * @memberof LoginVerifyCodeComponent
   */
  private drawLine(ctx: CanvasRenderingContext2D) {
    for (let i = 0; i < 2; i++) {
      ctx.strokeStyle = randomColor(40, 180);
      ctx.beginPath();
      ctx.moveTo(
        randomNum(0, this.canvasWidth),
        randomNum(0, this.canvasHeight)
      );
      ctx.lineTo(
        randomNum(0, this.canvasWidth),
        randomNum(0, this.canvasHeight)
      );
      ctx.stroke();
    }
  }

}
