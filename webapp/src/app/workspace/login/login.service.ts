import { LoginUser } from './../../shared/models/login-user.model';
import { Injectable } from '@angular/core';
import { AuthServerProvider } from 'src/app/shared/services/auth-session.service';
import { BaseHttpService } from 'src/app/shared/services/base-http.service';
import { Principal } from 'src/app/shared/services/principal.service';
import { Router } from '@angular/router';

@Injectable({ providedIn: 'root' })
export class LoginService {
  constructor(
    private baseService: BaseHttpService,
    private authService: AuthServerProvider,
    private principal: Principal,
    private router: Router,
  ) { }

  /**
   *
   * 用户登录逻辑 
   * @param {string} username 用户名
   * @param {string} code_pwd 验证码 或者 密码
   * @param {string} type 0 手机+验证码  1 用户名+密码
   * @return {*} 
   * @memberof LoginService
   */
  login(username: string, code_pwd: string, type: string) {
    return new Promise((resolve, reject) => {
      this.checkLogin(username, code_pwd, type).then(res => {
        const loginUser: LoginUser = {
          username,
          password: code_pwd,
        }
        this.authService.authorization(loginUser).then(res => {
          if (res) {
            if (res.rlt === 0) {
              resolve(res)
              this.principal.authenticate(res)
              this.router.navigateByUrl('/admin')
            } else {
              reject(res.info || '未知错误！')
            }
          } else {
            reject('系统异常，请稍后再试！')
          }
        }).catch(err => {
          reject(err || '系统异常，请稍后再试！')
        })
      }).catch(err => {
        reject(err || '系统异常，请稍后再试！')
      })
    })
  }

  /**
   *
   * 通过手机号获取验证码
   * @param {string} phoneNumber
   * @return {*}
   * @memberof LoginService
   */
  getPhoneVerificationCode(phoneNumber: string) {
    return this.baseService.get(`api/open/getCode/${phoneNumber}`);
  }

  /**
   * 验证登录
   * @param username
   * @param code
   * @param type  0 手机号+验证码 1 用户名+密码
   * @returns
   */
  checkLogin(username: string, code: string, type: string) {
    return this.baseService.get(
      `api/open/checkLogin/${username}/${code}/${type}`
    );
  }

  logout() {
    return this.authService.logout();
  }
}
