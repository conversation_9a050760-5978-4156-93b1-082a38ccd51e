<div class="login-container">
    <div class="login-wrapper">
        <div class="title">
            <!-- <img src="/assets/img/login/sys-title.png" /> -->
        </div>
        <div class="login-content">
            <div class="login-content__tabs {{currentTab.className}}">
                <div class="tab" *ngFor="let tab of tabList;index as i" [class.active]="currentIndex == i">
                    <a href="javascript:void(0)" (click)="switchTab(i)">{{tab.title}}</a>
                </div>
            </div>
            <ng-container *ngFor="let item of dynamicList">
                <div [hidden]="currentTab.dynamic.name!=item.name">
                    <app-dynamic-compopnent [dynamicCom]="item"></app-dynamic-compopnent>
                </div>
            </ng-container>
        </div>
    </div>
</div>