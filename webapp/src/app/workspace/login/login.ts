export enum VALIDATE_TIPS {
    NULL_PHONE_NUM = '手机号码不能为空',
    ERROR_PHONE_NUM = '请输入正确的手机号码',
    CODE_HAS_SEND = '验证码已发送，请注意查收',
    CODE_SEND_ERROR = '验证码发送失败，请稍后再试',
    VERIFY_CODE_ERROR = '验证码输入错误',
}

export enum LOGIN_STATUS {
    DEFAULT, // 默认状态
    SEND_CODE, // 发送验证码中
    SEND_CODE_SUCCESS,// 发送验证码成功
    SEND_CODE_ERROR,// 发送验证码失败
    LOGINING,// 登录中
    LOGIN_ERROR, // 登录失败
    LOGIN_SUCCESS// 登录成功
}
// 间隔60s 可发送一次验证码
export const SEND_CODE_TIMER = 60