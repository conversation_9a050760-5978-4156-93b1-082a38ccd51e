<form #loginForm="ngForm">
    <div class="login-content__form">
        <!-- <div class="input-group">
            <div class="prefix"><i class="sl-phone-24"></i></div>
            <input placeholder="请输入用户名" type="text" #usernameInput="ngModel" [slValidate]="userNameValidate"
                name="username" [(ngModel)]="loginUser.username" />
        </div>
        <div class="input-group__tip" [class.visible]="usernameInput.touched && usernameInput.invalid">
            <i class="sl-warn-red-14"></i>
            <span>请输入用户名</span>
        </div> -->


        <div class="input-group">
            <div class="prefix">
                <i class="sl-phone-24"></i>
            </div>
            <input type="text" name="username" #usernameInput="ngModel" [(ngModel)]="loginUser.username" name="username"
                [(ngModel)]="loginUser.username" [slValidate]="phoneValidate" placeholder="请输入手机号" minlength="11"
                maxlength="11" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')" (keydown)="tipMsg=''" />
        </div>
        <div class="input-group__tip" [class.visible]="usernameInput.touched && usernameInput.invalid">
            <i class="sl-warn-red-14"></i>
            <span>手机号输入错误，请重新输入</span>
        </div>
        <div class="input-group">
            <div class="prefix"> <i class="sl-message-24"></i></div>
            <input placeholder="请输入密码" type="password" name="password" [slValidate]="passwordValidate" #passwordElement
                #passwordInput="ngModel" [(ngModel)]="loginUser.password" />
            <a href="javascript:void(0)" class="suffix" (click)="togglePwd(passwordElement)">
                <i [ngClass]="passwordElement.type=='text'?'sl-eye-opened-16':'sl-eye-closed-16'"></i>
            </a>
        </div>
        <div class="input-group__tip" [class.visible]="!!tipMsg">
            <i class="sl-warn-red-14"></i>
            <span>{{tipMsg}}</span>
        </div>
        <div class="login-submit">
            <button (click)="login()" [disabled]="usernameInput.invalid||passwordInput.invalid" class="sl-default-btn"
                type="submit">{{loginText}}</button>
        </div>
    </div>
</form>