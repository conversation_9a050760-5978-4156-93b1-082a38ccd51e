import { Component, Input, OnInit } from '@angular/core';
import { LoginUser, DynamicComponentData } from 'src/app/shared/models';
import { SlValidateOption } from 'src/app/shared/modules/sl-validate';
import { LoginService } from '../login.service';

@Component({
  selector: 'app-login-form-username',
  templateUrl: './login-form-username.component.html',
  styleUrls: ['../login-form.less', './login-form-username.component.less']
})
export class LoginFormUsernameComponent implements OnInit {

  @Input() data: DynamicComponentData = new DynamicComponentData()

  loginUser: LoginUser = new LoginUser()
  userNameValidate: SlValidateOption = { required: true, errorTip: '请输入手机号' }
  phoneValidate: SlValidateOption = { required: true, type: 'phone', errorTip: '手机号输入错误，请重新输入' }
  passwordValidate: SlValidateOption = { required: true, label: '密码' }
  tipMsg: string = ''
  //loginText
  loginText: string = '登录'
  constructor(private loginService: LoginService) { }

  ngOnInit(): void {
    if (this.data && this.data.params) {
      const { loginUser } = this.data.params
      if (loginUser) this.loginUser = loginUser
    }
  }
  login() {
    this.loginText = '登录中...'
    this.loginService.login(this.loginUser.username!, this.loginUser.password!, '1')
      .then(res => {
        this.loginText = '登录'
      }).catch(err => {
        this.loginText = '登录'
        this.tipMsg = err
      })
  }
  /**
   *
   * 显示、隐藏密码
   * @param {HTMLInputElement} control
   * @memberof LoginFormUsernameComponent
   */
  togglePwd(control: HTMLInputElement) {
    control.type = control.type == 'text' ? 'password' : 'text';
  }
}
