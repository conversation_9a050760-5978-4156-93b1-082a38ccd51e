import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MapToolLineLayerComponent } from './map-tool-line-layer.component';
import { MapToolCircleLayerComponent } from './map-tool-circle-layer.component';
import { MapToolRectLayerComponent } from './map-tool-rect-layer.component';
import { MapToolPolygonLayerComponent } from './map-tool-polygon-layer.component';
import { MapToolPointComponent } from './map-tool-point.component';
import { MapRangingDistanceLayerComponent } from './map-ranging-distance-layer.component';
import { MapRangingLastPopupComponent } from './map-ranging-last-popup/map-ranging-last-popup.component';



@NgModule({
  declarations: [
    MapToolLineLayerComponent,
    MapToolCircleLayerComponent,
    MapToolRectLayerComponent,
    MapToolPolygonLayerComponent,
    MapToolPointComponent,
    MapRangingDistanceLayerComponent,
    MapRangingLastPopupComponent,
  ],
  imports: [
    CommonModule
  ]
})
export class MapDrawToolModule { }
