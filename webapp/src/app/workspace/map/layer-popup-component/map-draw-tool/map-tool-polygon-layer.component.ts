import { MapLayerComponentService } from './../../service/map-layer-component.service';
import { MapStateService } from './../../service/map-state.service';
import { MapToolBase } from './map-tool-base';
import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import * as L from 'leaflet'
import * as _ from 'lodash';
import { MapDrawToolService } from './map-draw-tool.service';
import { DynamicComponentData } from 'src/app/shared/models';
@Component({
  selector: 'app-map-tool-polygon',
  template: ``
})
export class MapToolPolygonLayerComponent extends MapToolBase implements OnInit, OnDestroy {
  @Input() data: DynamicComponentData = {}
  constructor(
    protected mapState: MapStateService,
    protected mapLayerSerivce: MapLayerComponentService,
    protected mapDrawToolService: MapDrawToolService
  ) {
    super(mapState, mapLayerSerivce, mapDrawToolService)
  }
  ngOnDestroy(): void {
    this.layerGroup.remove()
    this.tempLayerGroup.remove()
    this.removeMouseListener('mousedown')
    this.removeMouseListener('mousemove')
    this.removeMouseListener('dblclick')
  }

  ngOnInit(): void {
    this.initData(this.data)
    if (this.latlngs && this.latlngs.length) {
      this.initPolygon()
    } else {
      this.draw()
    }
  }

  private initPolygon() {
    const polygon = this.genPolygon(this.latlngs).addTo(this.tempLayerGroup)
    if (this.fitBounds) this.map.fitBounds(polygon.getBounds())
    this.latlngs.forEach(ele => {
      this.genCircleMarker(ele, { fillOpacity: 1 }).addTo(this.tempLayerGroup)
    })
  }

  private draw() {
    this.addMouseListener('mousedown', this.drawSolidLine)
    this.addMouseListener('mousemove', this.drawDashLine)
    this.addMouseListener('dblclick', this.drawComplete)
  }

  /**
   *
   * 绘制实线：鼠标点击
   * @private
   * @param {L.LeafletMouseEvent} e
   * @memberof MapToolPolygonLayerComponent
   */
  private drawSolidLine = (e: L.LeafletMouseEvent) => {
    if (this.isFinished) return
    const latlng = e.latlng
    if (this.isNotExist(latlng)) {
      this.tempLatlngs.push(latlng)
      this.genCircleMarker(latlng, { fillOpacity: 1 }).addTo(this.layerGroup)
      this.mapDrawToolService.setState({ latlng });
    }
  }


  /**
   *
   * 绘制虚线：鼠标移动
   * @private
   * @param {L.LeafletMouseEvent} e
   * @memberof MapToolPolygonLayerComponent
   */
  private drawDashLine = (e: L.LeafletMouseEvent) => {
    // 移除临时绘制的图层
    this.tempLayerGroup.clearLayers()
    if (this.isFinished) return
    const latlng = e.latlng
    if (this.isNotExist(latlng)) {
      this.genPolygon([...this.tempLatlngs, latlng]).addTo(this.tempLayerGroup)
      // this.polygon.setLatLngs([...this.tempLatlngs, latlng])
    }
    // 绘制小圆点
    const circle = this.genCircleMarker(latlng)
    this.tempLayerGroup.addLayer(circle)
  }

  /**
   *
   * 绘制完成：鼠标双击触发
   * @private
   * @memberof MapToolPolygonLayerComponent
   */
  private drawComplete = (e: L.LeafletMouseEvent) => {
    this.isFinished = true
    this.removeMouseListener('mousemove')
    this.removeMouseListener('mousedown')
    this.removeMouseListener('dblclick')
    this.tempLayerGroup.clearLayers()
    this.genPolygon(this.tempLatlngs, { dashArray: undefined }).addTo(this.layerGroup)
    this.mapDrawToolService.setState({
      complete: true,
      name: 'MapToolPolygonLayerComponent',
    });
  }

  /**
   *
   * 成成多边形
   * @private
   * @param {Array<L.LatLng>} latlngs
   * @return {*}  {L.Polygon}
   * @memberof MapToolPolygonLayerComponent
   */
  private genPolygon(latlngs: Array<L.LatLng>, options?: L.PolylineOptions): L.Polygon {
    let _opt: L.PolylineOptions = {
      color: this.color,
      fillOpacity: 0.2,
      // fillColor: '#B9C6ED',
      weight: 2,
      dashArray: '5'
    }
    if (options) _opt = _.assign(_opt, options)
    return L.polygon(latlngs, _opt);
  }
}
