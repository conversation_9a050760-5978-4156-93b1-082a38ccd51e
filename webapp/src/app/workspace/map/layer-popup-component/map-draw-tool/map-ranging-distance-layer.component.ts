import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import * as L from 'leaflet';
import * as _ from 'lodash';
import { MapLayerComponentService } from '../../service/map-layer-component.service';
import { MapStateService } from '../../service/map-state.service';
import { MapDrawToolService } from './map-draw-tool.service';
import { MapToolBase } from './map-tool-base';

@Component({
  selector: 'app-map-ranging-distance-layer',
  template: `<app-map-ranging-last-popup (onClose)="closeLayer()" [distance]="totalDistance/100"  #lastPopupRef></app-map-ranging-last-popup>`,
  styles: []
})
export class MapRangingDistanceLayerComponent extends MapToolBase implements OnInit {
  solidLine!: L.Polyline; //  实线
  dashLine!: L.Polyline; // 虚线
  lastMarker!: <PERSON><PERSON>arker; // 最后一个marker
  totalDistance: number = 0;
  lastPopup!: L.Popup;// 最后一个popup
  @ViewChild('lastPopupRef', { static: true, read: ElementRef })
  lastPopupRef!: ElementRef;
  constructor(
    protected mapState: MapStateService,
    protected layerComponentService: MapLayerComponentService,
    protected mapDrawToolService: MapDrawToolService,
  ) {
    super(mapState, layerComponentService, mapDrawToolService)
  }
  ngOnDestroy(): void {
    this.removeMouseListener('mousedown')
    this.removeMouseListener('mousemove')
    this.layerGroup.remove()
    this.tempLayerGroup.remove()
    this.removeBaseClass()
  }

  ngOnInit(): void {
    this.color = '#D23C3C'
    this.solidLine = this.genPolyline([]).addTo(this.layerGroup)
    this.dashLine = this.genPolyline([], { dashArray: '5' }).addTo(this.tempLayerGroup)
    this.draw()
  }

  closeLayer() {
    this.mapDrawToolService.setState({
      destroy: true,
      name: 'MapRangingDistanceLayerComponent',
    });
  }

  private draw() {
    this.addBaseClass()
    this.addMouseListener('mousedown', this.drawSolidLine)
    this.addMouseListener('mousemove', this.drawDashLine)
    this.addMouseListener('dblclick', this.drawComplete)
  }

  /**
   *
   * 绘制实线：鼠标点击
   * @private
   * @param {L.LeafletMouseEvent} e
   * @memberof MapToolLineLayerComponent
   */
  private drawSolidLine = (e: L.LeafletMouseEvent) => {
    if (this.isFinished) return
    const latlng = e.latlng
    if (this.isNotExist(latlng)) {
      this.tempLatlngs.push(latlng)
      this.lastMarker = this.genCircleMarker(latlng, { radius: 5, fillColor: this.color, color: '#fff' }).addTo(this.layerGroup)
      if (this.len < 2) {
        const popup = this.genPopup(latlng, '起点')
        this.layerGroup.addLayer(popup)
      } else {
        this.solidLine.setLatLngs(this.tempLatlngs)
        const penultLatlng = this.tempLatlngs[this.len - 2]
        const distance = this.getDistance(penultLatlng, e.latlng, 2)
        if (distance > 0.01) {
          this.totalDistance += distance * 100
          this.lastPopup = this.genPopup(latlng, distance + '海里').addTo(this.layerGroup)
        }
      }
    }
  }


  /**
   * 
   * 绘制虚线：鼠标移动 
   * @private
   * @param {L.LeafletMouseEvent} e
   * @memberof MapToolLineLayerComponent
   */
  private drawDashLine = (e: L.LeafletMouseEvent) => {
    // 移除临时绘制的图层
    this.tempLayerGroup.clearLayers()
    if (this.isFinished) return
    const latlng = e.latlng
    // 绘制小圆点
    const tempCircle = this.genCircleMarker(latlng, { radius: 5, fillColor: this.color, color: '#fff' })
    this.tempLayerGroup.addLayer(tempCircle)
    if (this.len < 1) return
    const penultLatlng = this.tempLatlngs[this.len - 1]
    const distance = this.getDistance(penultLatlng, e.latlng, 2)
    if (distance > 0.01) {
      this.dashLine.setLatLngs([penultLatlng, latlng])
      const tempPopup = this.genPopup(e.latlng, distance + '海里')
      this.tempLayerGroup.addLayer(this.dashLine)
      this.tempLayerGroup.addLayer(tempPopup)
    }
  }

  /**
   *
   * 绘制完成：鼠标双击触发
   * @private
   * @memberof MapToolLineLayerComponent
   */
  private drawComplete = (e: L.LeafletMouseEvent) => {
    this.isFinished = true
    // 移除临时图层的绘制
    this.tempLayerGroup.clearLayers()
    // 取消mouse事件监听
    this.removeMouseListener('mousemove')
    this.removeMouseListener('mousedown')
    this.removeMouseListener('dblclick')
    if (this.lastMarker) {
      this.layerGroup.removeLayer(this.lastMarker)
    }
    // 绘制最后的实心圆
    this.lastMarker = this.genCircleMarker(e.latlng, { radius: 3, fillColor: this.color, color: this.color }).addTo(this.layerGroup)
    if (this.lastPopup) {
      this.layerGroup.removeLayer(this.lastPopup)
    }
    this.lastPopup = this.genPopup(e.latlng, this.lastPopupRef.nativeElement)
      .addTo(this.layerGroup)
    this.mapDrawToolService.setState({
      latlng: e.latlng,
      complete: true,
      name: 'MapRangingDistanceLayerComponent',
    });
  }

  private genPopup(latlng: L.LatLng, cotent: string | HTMLElement, opt?: L.PopupOptions): L.Popup {
    let _defaultOpt: L.PopupOptions = {
      closeButton: false,
      autoClose: false,
      autoPan: false,
      closeOnEscapeKey: false,
      className: 'map-rangin-distance-popup',
    };
    if (opt) _defaultOpt = Object.assign({}, _defaultOpt, opt);
    return L.popup(_defaultOpt).setLatLng(latlng).setContent(cotent);
  }
}
