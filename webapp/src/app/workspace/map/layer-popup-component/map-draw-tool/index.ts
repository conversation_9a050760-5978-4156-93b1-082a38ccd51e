import { MapRangingDistanceLayerComponent } from './map-ranging-distance-layer.component';
import { MapToolPolygonLayerComponent } from './map-tool-polygon-layer.component';
import { MapToolLineLayerComponent } from './map-tool-line-layer.component';
import { DynamicComponent } from 'src/app/shared/models'
import { MapToolCircleLayerComponent } from './map-tool-circle-layer.component'
import { MapToolRectLayerComponent } from './map-tool-rect-layer.component';
import { MapToolPointComponent } from './map-tool-point.component';

export const DYNAMIC_MAP_DRAW_TOOL_COMPONENTS: Array<DynamicComponent> = [
    { name: 'MapToolCircleLayerComponent', component: MapToolCircleLayerComponent }, // 圆
    { name: 'MapToolLineLayerComponent', component: MapToolLineLayerComponent }, // 线
    { name: 'MapToolPolygonLayerComponent', component: MapToolPolygonLayerComponent }, // 多边形
    { name: 'MapToolRectLayerComponent', component: MapToolRectLayerComponent }, // 矩形
    { name: 'MapToolPointComponent', component: MapToolPointComponent }, // 点
    { name: 'MapRangingDistanceLayerComponent', component: MapRangingDistanceLayerComponent }, // 测距
]