import { Component, OnInit, On<PERSON><PERSON>roy, Input } from '@angular/core';
import { MapLayerComponentService } from '../../service/map-layer-component.service';
import { MapStateService } from '../../service/map-state.service';
import { MapToolBase } from './map-tool-base';
import * as L from 'leaflet'
import * as _ from 'lodash';
import { MapDrawToolService } from './map-draw-tool.service';
import { DynamicComponentData } from 'src/app/shared/models';
@Component({
  selector: 'app-map-tool-circle',
  template: ``
})
export class MapToolCircleLayerComponent extends MapToolBase implements OnInit, OnDestroy {
  @Input() data: DynamicComponentData = {}
  constructor(
    protected mapState: MapStateService,
    protected layerComponentService: MapLayerComponentService,
    protected mapDrawToolService: MapDrawToolService,
  ) {
    super(mapState, layerComponentService, mapDrawToolService)
  }
  ngOnDestroy(): void {
    this.removeMouseListener('mousedown')
    this.removeMouseListener('mousemove')
    this.layerGroup.remove()
    this.tempLayerGroup.remove()
  }

  ngOnInit(): void {
    this.initData(this.data)
    if (this.latlngs && this.latlngs.length && this.radius) {
      this.initCircle()
    } else {
      this.draw()
    }
  }

  private initCircle() {
    const circle = this.genCircle(this.latlngs[0], { radius: this.radius }).addTo(this.tempLayerGroup)
    if (this.fitBounds) this.map.fitBounds(circle.getBounds());
    this.latlngs.forEach(ele => {
      this.genCircleMarker(ele, { fillOpacity: 1 }).addTo(this.tempLayerGroup)
    })
  }

  /**
   *
   * 绘制 circle
   * @private
   * @memberof MapToolCircleLayerComponent
   */
  private draw() {
    this.addMouseListener('mousedown', this.drawSolidLine)
    this.addMouseListener('mousemove', this.drawDashLine)
  }

  /**
   * 
   * 绘制实线圆：鼠标点击
   * @private
   * @param {L.LeafletMouseEvent} e
   * @memberof MapToolCircleLayerComponent
   */
  private drawSolidLine = (e: L.LeafletMouseEvent) => {
    if (this.isFinished || this.len > 1) return
    const latlng = e.latlng
    if (this.isNotExist(latlng)) {
      this.tempLatlngs.push(latlng);
      const circleMarker = this.genCircleMarker(latlng, { fillOpacity: 1 })
      if (this.len == 1) {
        this.layerGroup.addLayer(circleMarker)
        this.mapDrawToolService.setState({
          latlng,
        });
      }
      // 鼠标标记两个点代表绘制完成
      if (this.len == 2) {
        const radius = this.tempLatlngs[0].distanceTo(latlng)
        this.genCircle(this.tempLatlngs[0], { radius }).addTo(this.layerGroup)
        this.drawComplete(e, radius)
      }
    }
  }

  /**
   *
   * 画虚拟圆：鼠标移动
   * @private
   * @param {L.LeafletMouseEvent} e
   * @memberof MapToolCircleLayerComponent
   */
  private drawDashLine = (e: L.LeafletMouseEvent) => {
    this.tempLayerGroup.clearLayers()
    if (this.isFinished) return
    if (this.len == 1) {
      const radius = this.tempLatlngs[0].distanceTo(e.latlng)
      this.genPolyline([this.tempLatlngs[0], e.latlng], { dashArray: '5' }).addTo(this.tempLayerGroup)
      this.genCircle(this.tempLatlngs[0], { radius, dashArray: '5' }).addTo(this.tempLayerGroup)
    }
    this.genCircleMarker(e.latlng).addTo(this.tempLayerGroup)
  }
  /**
     * 绘制圆
     * @param latlng
     * @returns
     */
  private genCircle(latlng: L.LatLng, options?: Partial<L.CircleMarkerOptions>): L.Circle {
    let _opt: L.CircleMarkerOptions = {
      color: this.color,
      fillOpacity: 0.1,
      // fillColor: '#99ffff',
      weight: 2,
      radius: 3
    };
    if (options) _opt = _.assign(_opt, options);
    return L.circle(latlng, _opt);
  }

  /**
   * 绘制完成
   */
  private drawComplete(e: L.LeafletMouseEvent, radius: number) {
    this.isFinished = true
    this.removeMouseListener('mousedown')
    this.removeMouseListener('mousemove')
    this.tempLayerGroup.clearLayers()
    this.mapDrawToolService.setState({
      complete: true,
      radius,
      name: 'MapToolCircleLayerComponent',
    });
  }
}
