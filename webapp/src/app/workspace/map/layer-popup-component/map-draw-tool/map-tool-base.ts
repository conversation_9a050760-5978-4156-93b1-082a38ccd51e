import { Inject } from '@angular/core'
import * as L from 'leaflet'
import { DynamicComponentData } from 'src/app/shared/models'
import { MapLayerComponentService } from '../../service/map-layer-component.service'
import { MapStateService } from '../../service/map-state.service'
import { MapDrawToolService } from './map-draw-tool.service'
type eventType = 'dblclick' | 'mousedown' | 'mousemove'
export class MapToolBase {
  map: L.Map  // map 
  tempLatlngs: Array<L.LatLng> = [];// 坐标数
  layerGroup: L.FeatureGroup// 图层
  tempLayerGroup: L.FeatureGroup // 临时图层
  layerList: Array<any> // 最终绘制的图层数组
  tempLayerList: Array<any> // 临时图层数组
  isFinished: boolean = false; // 是否绘制完成
  fitBounds: boolean = false;// 是否缩放到合适的级别
  color: string = '#BC9DEE' // 颜色
  iconUrl: string = '' // 点 icon 
  iconSize!: [number, number] // 图标尺寸
  latlngs: Array<L.LatLng> = [] // 初始的坐标
  radius: number = 0 // 半径
  MAP_TOOL_BASE_ACTIVE = 'map-tool-base-active'; // 基础样式

  constructor(
    protected mapState: MapStateService,
    protected layerComponentService: MapLayerComponentService,
    protected mapDrawToolService: MapDrawToolService,
  ) {
    this.map = this.mapState.getMapInstance()
    this.layerGroup = L.featureGroup().addTo(this.map)
    this.tempLayerGroup = L.featureGroup().addTo(this.map)
    this.tempLatlngs = []
    this.layerList = []
    this.tempLayerList = []

  }

  initData(data: DynamicComponentData) {
    if (!data) return
    const { params } = data
    if (params) {
      const { color, iconUrl, latlngs, radius, fitBounds, iconSize } = params
      if (color) this.color = color
      if (iconUrl) this.iconUrl = iconUrl
      if (radius) this.radius = radius
      if (fitBounds) this.fitBounds = fitBounds
      if (iconSize) this.iconSize = iconSize
      if (latlngs && latlngs.length) {
        this.latlngs = latlngs
      }
    }
  }

  get len() {
    return (this.tempLatlngs && this.tempLatlngs.length) || 0;
  }
  addBaseClass() {
    this.mapState.addMapContainerClass(this.MAP_TOOL_BASE_ACTIVE);
  }

  removeBaseClass() {
    this.mapState.removeMapContainerClass(this.MAP_TOOL_BASE_ACTIVE);
  }
  addMouseListener(type: eventType, eventFn: L.LeafletMouseEventHandlerFn) {
    this.map.on(type, eventFn)
  }
  removeMouseListener(type: eventType) {
    this.map.off(type)
    setTimeout(() => {
      this.mapState.onMousemove();
    }, 100);
  }
  /**
     * 两点之间画线
     * @param latlngs
     * @returns
     */
  genPolyline(
    latlngs: L.LatLngExpression[] | L.LatLngExpression[][],
    options?: L.PolylineOptions
  ) {
    let _opt = { weight: 2, color: this.color };
    if (options) {
      _opt = Object.assign({}, _opt, options);
    }
    return L.polyline(latlngs, _opt);
  }
  /**
   * 小圆点
   * @param latlngs
   * @returns
   */
  genCircleMarker(latlng: L.LatLng, options?: Partial<L.CircleMarkerOptions>) {
    let _opt: L.CircleMarkerOptions = {
      color: this.color,
      radius: 3,
      className: 'map-tool-circle',
      fillOpacity: 1
    };
    if (options) _opt = Object.assign({}, _opt, options);
    return L.circleMarker(latlng, _opt);
  }

  /**
   *
   * 获取两个坐标点之前的距离,保留5位小数
   * @param {L.LatLng} latlng1
   * @param {L.LatLng} latlng2
   * @return {*}  {string}
   * @memberof MapToolBase
   */
  getDistance(latlng1: L.LatLng, latlng2: L.LatLng, decimal = 5): number {
    return parseFloat(Number(latlng1.distanceTo(latlng2) / 1852).toFixed(decimal));
  }

  isNotExist(latlng: L.LatLng) {
    return !this.isExist(latlng);
  }

  /**
   * 坐标点是否存在数组中
   * @param latlng
   */
  isExist(latlng: L.LatLng) {
    return this.tempLatlngs.some(
      (ele) => ele.lat === latlng.lat && ele.lng === latlng.lng
    );
  }
}
