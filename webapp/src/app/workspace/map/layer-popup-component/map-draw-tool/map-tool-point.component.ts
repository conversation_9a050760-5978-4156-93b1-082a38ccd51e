import { Component, Input, OnInit, OnD<PERSON>roy } from '@angular/core';
import { DynamicComponentData } from 'src/app/shared/models';
import { MapLayerComponentService } from '../../service/map-layer-component.service';
import { MapStateService } from '../../service/map-state.service';
import { MapDrawToolService } from './map-draw-tool.service';
import { MapToolBase } from './map-tool-base';
import * as L from 'leaflet'
@Component({
  selector: 'app-map-tool-point',
  template: ``
})
export class MapToolPointComponent extends MapToolBase implements OnInit, OnDestroy {
  @Input() data: DynamicComponentData = {}
  constructor(
    protected mapState: MapStateService,
    protected mapLayerService: MapLayerComponentService,
    protected mapDrawToolService: MapDrawToolService) {
    super(mapState, mapLayerService, mapDrawToolService)

  }
  ngOnDestroy(): void {
    this.removeMouseListener('mousedown')
    this.removeMouseListener('mousemove')
    this.layerGroup.remove()
    this.tempLayerGroup.remove()
  }

  ngOnInit(): void {
    this.initData(this.data)
    if (this.latlngs && this.latlngs.length && this.iconUrl) {
      this.initPoint()
    } else {
      this.draw()
    }
  }
  private initPoint() {
    const icon = this.genIconMarker(this.latlngs[0]).addTo(this.tempLayerGroup)
    if (this.fitBounds) this.map.setView(icon.getLatLng());
  }

  private draw() {
    this.addMouseListener('mousedown', this.drawComplete)
    this.addMouseListener('mousemove', this.drawPoint)
  }

  private drawPoint = (e: L.LeafletMouseEvent) => {
    this.tempLayerGroup.clearLayers()
    const latlng = e.latlng;
    this.genIconMarker(latlng).addTo(this.tempLayerGroup)
  }

  private drawComplete = (e: L.LeafletMouseEvent) => {
    this.removeMouseListener('mousedown')
    this.removeMouseListener('mousemove')
    const latlng = e.latlng;
    this.mapDrawToolService.setState({
      latlng,
      complete: true,
      name: 'MapToolPointComponent'
    })
  }

  /**
   * 绘制图标icon
   * @param latlng
   */
  private genIconMarker(latlng: L.LatLng) {
    const option: L.IconOptions = { iconUrl: this.iconUrl }
    if (this.iconSize) {
      option.iconSize = this.iconSize
    }
    const icon = L.icon(option);
    return L.marker(latlng, {
      icon,
    });
  }

}
