import { MapLayerComponentService } from './../../service/map-layer-component.service';
import { MapToolBase } from './map-tool-base';
import { MapStateService } from './../../service/map-state.service';
import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import * as L from 'leaflet'
import { MapDrawToolService } from './map-draw-tool.service';
import { DynamicComponentData } from 'src/app/shared/models';
@Component({
  selector: 'app-map-tool-line',
  template: ``
})
export class MapToolLineLayerComponent extends MapToolBase implements OnInit, OnDestroy {

  @Input() data: DynamicComponentData = {}

  solidLine!: L.Polyline; //  实线
  dashLine!: L.Polyline; // 虚线

  constructor(
    protected mapState: MapStateService,
    protected mapLayerService: MapLayerComponentService,
    protected mapDrawToolService: MapDrawToolService) {
    super(mapState, mapLayerService, mapDrawToolService)

  }
  ngOnDestroy(): void {
    this.tempLayerGroup.remove()
    this.layerGroup.remove()
    this.removeMouseListener('mousedown')
    this.removeMouseListener('mousemove')
    this.removeMouseListener('dblclick')
  }

  ngOnInit(): void {
    this.initData(this.data)
    this.solidLine = this.genPolyline([]).addTo(this.layerGroup)
    this.dashLine = this.genPolyline([], { dashArray: '5' }).addTo(this.tempLayerGroup)
    if (this.latlngs && this.latlngs.length) {
      this.initLine()
    } else {
      this.draw()
    }
  }

  private initLine() {
    this.solidLine.setLatLngs(this.latlngs)
    if (this.fitBounds) this.map.fitBounds(this.solidLine.getBounds());
    this.latlngs.forEach(ele => {
      this.genCircleMarker(ele, { fillOpacity: 1 }).addTo(this.tempLayerGroup)
    })
  }

  private draw() {
    this.addMouseListener('mousedown', this.drawSolidLine)
    this.addMouseListener('mousemove', this.drawDashLine)
    this.addMouseListener('dblclick', this.drawComplete)
  }

  /**
   *
   * 绘制实线：鼠标点击
   * @private
   * @param {L.LeafletMouseEvent} e
   * @memberof MapToolLineLayerComponent
   */
  private drawSolidLine = (e: L.LeafletMouseEvent) => {
    if (this.isFinished) return
    const latlng = e.latlng
    if (this.isNotExist(latlng)) {
      this.tempLatlngs.push(latlng)
      this.genCircleMarker(latlng, { fillOpacity: 1 }).addTo(this.layerGroup)
      if (this.len > 1) {
        this.solidLine.setLatLngs(this.tempLatlngs)
      }
      this.mapDrawToolService.setState({ latlng });
    }
  }

  /**
   * 
   * 绘制虚线：鼠标移动 
   * @private
   * @param {L.LeafletMouseEvent} e
   * @memberof MapToolLineLayerComponent
   */
  private drawDashLine = (e: L.LeafletMouseEvent) => {
    // 移除临时绘制的图层
    this.tempLayerGroup.clearLayers()
    if (this.isFinished) return
    const latlng = e.latlng
    // 绘制小圆点
    const circle = this.genCircleMarker(latlng)
    this.tempLayerGroup.addLayer(circle)
    if (this.len < 1) return
    const penultLatlng = this.tempLatlngs[this.len - 1]
    const distance = this.getDistance(penultLatlng, e.latlng)
    if (distance > 0) {
      this.dashLine.setLatLngs([penultLatlng, latlng])
      const circle = this.genCircleMarker(e.latlng)
      this.tempLayerGroup.addLayer(circle)
      this.tempLayerGroup.addLayer(this.dashLine)
    }
  }

  /**
   *
   * 绘制完成：鼠标双击触发
   * @private
   * @memberof MapToolLineLayerComponent
   */
  private drawComplete = (e: L.LeafletMouseEvent) => {
    this.isFinished = true
    // 移除临时图层的绘制
    this.tempLayerGroup.clearLayers()
    // 取消mouse事件监听
    this.removeMouseListener('mousedown')
    this.removeMouseListener('mousemove')
    this.removeMouseListener('dblclick')
    this.mapDrawToolService.setState({
      complete: true,
      name: 'MapToolLineLayerComponent',
    });
  }
}
