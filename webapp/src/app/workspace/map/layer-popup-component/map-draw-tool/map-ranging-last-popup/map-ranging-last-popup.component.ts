import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
  selector: 'app-map-ranging-last-popup',
  templateUrl: './map-ranging-last-popup.component.html',
  styleUrls: ['./map-ranging-last-popup.component.less']
})
export class MapRangingLastPopupComponent implements OnInit {
  @Input() distance: number = 0
  @Output() onClose: EventEmitter<any> = new EventEmitter()
  constructor() { }

  ngOnInit(): void {
  }

  close() {
    this.onClose.emit()
  }

}
