import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { MapToolState } from './map-tool';
@Injectable({
  providedIn: 'root'
})
export class MapDrawToolService {

  private currentPointChange!: Subject<MapToolState>;
  change$: Observable<MapToolState>;
  constructor() {
    this.currentPointChange = new Subject();
    this.change$ = this.currentPointChange.asObservable();
  }
  setState(data: MapToolState) {
    this.currentPointChange.next(data);
  }
}
