import { MapLayerComponentService } from './../../service/map-layer-component.service';
import { MapStateService } from './../../service/map-state.service';
import { MapToolBase } from './map-tool-base';
import { Component, OnInit, OnDestroy, Input } from '@angular/core';
import * as L from 'leaflet';
import * as _ from 'lodash';
import { MapDrawToolService } from './map-draw-tool.service';
import { DynamicComponentData } from 'src/app/shared/models';

@Component({
  selector: 'app-map-tool-rect',
  template: ``
})
export class MapToolRectLayerComponent extends MapToolBase implements OnInit, OnDestroy {
  @Input() data: DynamicComponentData = {}
  constructor(
    protected mapState: MapStateService,
    protected mapLayerService: MapLayerComponentService,
    protected mapDrawToolService: MapDrawToolService) {
    super(mapState, mapLayerService, mapDrawToolService)
  }
  ngOnDestroy(): void {
    this.removeMouseListener('mousedown')
    this.removeMouseListener('mousemove')
    this.layerGroup.remove()
    this.tempLayerGroup.remove()
  }

  ngOnInit(): void {
    this.initData(this.data)
    if (this.latlngs && this.latlngs.length) {
      this.initRect()
    } else {
      this.draw()
    }
  }

  private initRect() {
    const rect = this.genRect(this.latlngs).addTo(this.tempLayerGroup)
    if (this.fitBounds) this.map.fitBounds(rect.getBounds())
    this.latlngs.forEach(ele => {
      this.genCircleMarker(ele, { fillOpacity: 1 }).addTo(this.tempLayerGroup)
    })
  }

  private draw() {
    this.addMouseListener('mousedown', this.drawSolidLine)
    this.addMouseListener('mousemove', this.drawDashLine)
  }

  /**
   * 
   * 绘制实线：鼠标点击
   * @private
   * @param {L.LeafletMouseEvent} e
   * @memberof MapToolRectLayerComponent
   */
  private drawSolidLine = (e: L.LeafletMouseEvent) => {
    if (this.isFinished) return
    if (this.len > 1) return
    const latlng = e.latlng
    if (this.isNotExist(latlng)) {
      this.tempLatlngs.push(latlng)
      this.genCircleMarker(latlng, { fillOpacity: 1 }).addTo(this.layerGroup)
      if (this.len == 2) {
        this.genRect(this.tempLatlngs, { dashArray: undefined }).addTo(this.layerGroup)
        this.drawComplete(e)
      } else {
        this.mapDrawToolService.setState({ latlng: e.latlng })
      }
    }
  }

  /**
   * 
   * 绘制虚线：鼠标移动
   * @private
   * @param {L.LeafletMouseEvent} e
   * @memberof MapToolRectLayerComponent
   */
  private drawDashLine = (e: L.LeafletMouseEvent) => {
    this.tempLayerGroup.clearLayers()
    if (this.isFinished) return
    const latlng = e.latlng
    this.genCircleMarker(latlng).addTo(this.tempLayerGroup)
    this.genRect([...this.tempLatlngs, latlng]).addTo(this.tempLayerGroup)
  }

  /**
   *
   * 绘制完成：鼠标第二点击触发
   * @private
   * @memberof MapToolRectLayerComponent
   */
  private drawComplete(e: L.LeafletMouseEvent) {
    this.isFinished = true
    this.removeMouseListener('mousemove')
    this.removeMouseListener('mousedown')
    this.tempLayerGroup.clearLayers()
    this.mapDrawToolService.setState({ latlng: e.latlng, complete: true, name: 'MapToolRectLayerComponent' })
  }

  /**
   *
   * 绘制矩形
   * @private
   * @param {Array<L.LatLng>} latlngs
   * @param {L.PolylineOptions} [options]
   * @return {*} 
   * @memberof MapToolRectLayerComponent
   */
  private genRect(latlngs: Array<L.LatLng>, options?: L.PolylineOptions) {
    let _opt: L.PolylineOptions = {
      color: this.color,
      weight: 2,
      fillOpacity: 0.1,
      // fillColor: '#B9C6ED',
      dashArray: '5'
    };
    if (options) _opt = _.assign(_opt, options);
    return L.rectangle(L.latLngBounds(latlngs), _opt);
  }

}
