
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MapDrawToolModule } from './map-draw-tool/map-draw-tool.module';
import { WindpowerAccidentModule } from './windpower-accident/windpower-accident.module';
import { WindpowerProtectModule } from './windpower-protect/windpower-protect.module';
import { MapAuxiliaryModule } from './map-auxiliary/map-auxiliary.module';
import { WindpowerShipModule } from './windpower-ship/windpower-ship.module';
import { CaseTestModule } from './case-test/case-test.module';
import { MapShipTrajectoryModule } from './map-ship-trajectory/map-ship-trajectory.module';
import { MapPolttingModule } from './map-poltting/map-poltting.module';
import { StationModule } from './station/station.module';
import { StationTestProjectModule } from './station-test-project/station-test-project.module';
import { BroadcastComponent } from './broadcast/broadcast';
import { FormsModule } from '@angular/forms';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzEmptyModule } from 'ng-zorro-antd/empty';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzRadioModule } from 'ng-zorro-antd/radio';

@NgModule({
  declarations: [BroadcastComponent],
  imports: [
    CommonModule,
    FormsModule, NzInputModule,
    NzEmptyModule, NzRadioModule,
    NzSelectModule,
    // 绘图工具
    MapDrawToolModule,
    // 事故
    WindpowerAccidentModule,
    // // 风电场保护区
    WindpowerProtectModule,
    // // 标绘模块
    MapPolttingModule,
    // // 其他辅助模块
    MapAuxiliaryModule,
    // // 船舶模块
    WindpowerShipModule,
    // // 测试
    CaseTestModule,
    // // 船舶轨迹模块
    MapShipTrajectoryModule,
    // 基站
    StationModule,
    // 项目测试列表
    StationTestProjectModule
  ],
})
export class LayerPopupComponentModule { }
