import { isNotEmpty } from 'src/app/shared/utils/convert';
import { Pipe, PipeTransform } from '@angular/core';
import * as moment from 'moment';
@Pipe({
  name: 'caseTestDate'
})
export class CaseTestDatePipe implements PipeTransform {

  transform(value: string | number, list: Array<any>, index: number): string {
    if (isNotEmpty(value)) {
      const subList = list.slice(0, index + 1).map(ele => parseFloat(ele.time));
      const totalTime = subList.reduce((x, y) => x + y);
      return moment().add(totalTime, 'minute').format('YYYY-MM-DD HH:mm');
    }
    return ''
  }

}
