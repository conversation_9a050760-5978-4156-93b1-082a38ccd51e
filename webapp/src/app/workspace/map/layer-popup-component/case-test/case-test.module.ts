import { NzEmptyModule } from 'ng-zorro-antd/empty';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzInputModule } from 'ng-zorro-antd/input';
import { FormsModule } from '@angular/forms';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { CaseTestHandleComponent } from './case-test-handle/case-test-handle.component';
import { CaseTestComponent } from './case-test.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { CaseTestDatePipe } from './case-test-date.pipe';

@NgModule({
  declarations: [CaseTestHandleComponent, CaseTestComponent, CaseTestDatePipe],
  imports: [
    CommonModule,
    FormsModule,
    NzInputModule,
    SharedModule,
    NzSelectModule,
    NzSpinModule,
    NzEmptyModule,
  ]
})
export class CaseTestModule { }
