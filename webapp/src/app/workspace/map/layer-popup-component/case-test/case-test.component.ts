import { Subscription } from 'rxjs';
import { CaseTestService } from './case-test.service';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { MapLayerComponentService } from '../../service/map-layer-component.service';
import { BaseSearch } from 'src/app/shared/models';
import { SlModalService } from 'src/app/shared/modules/sl-modal/sl-modal.service';

@Component({
  selector: 'app-case-test',
  templateUrl: './case-test.component.html',
  styleUrls: ['./case-test.component.less']
})
export class CaseTestComponent implements OnInit, OnDestroy {
  list: Array<any> = []
  sub: Subscription;
  isSpining: boolean = false;
  constructor(
    private mapLayerService: MapLayerComponentService,
    private slModalService: SlModalService,
    private service: CaseTestService) {
    this.sub = this.service.change.subscribe(() => {
      this.getList();
    })
  }
  ngOnDestroy(): void {
    this.sub.unsubscribe();
  }

  ngOnInit(): void {
    this.getList();
  }



  getList() {
    this.isSpining = true;
    const search = new BaseSearch(false);
    this.service.getList(search).then((res: any) => {
      this.list = res || [];
      this.isSpining = false;
    }).catch(() => {
      this.isSpining = false;
    })
  }

  del(item: any) {
    this.slModalService.openPromptModal({
      type: 'confirm',
      content: '确定删除吗？',
      okCb: () => {
        this.service.del(item.id).then(() => {
          this.slModalService.openPromptModal({ type: 'success', content: '删除成功！' })
          this.getList()
        }).catch(err => {
          this.slModalService.openPromptModal({ type: 'error', content: '删除失败！' })
        })
      }
    })
  }
  satart(item: any) {
    this.addTest(item.id, 'mock');
  }
  edit(item: any) {
    this.addTest(item.id, 'edit');
  }
  addTest(id?: string, type?: string) {
    let title = '用例新增'
    if (type) {
      title = type == 'mock' ? '开始模拟' : '用例编辑'
    }
    this.mapLayerService.addComponent({
      type: 'popup',
      name: 'CaseTestHandleComponent',
      title,
      data: {
        size: {
          width: 850
        },
        position: {
          top: 80,
          left: 560
        },
        params: {
          id,
          type
        }
      }
    })
  }
}
