import { NgForm } from '@angular/forms';
import { Subscription } from 'rxjs';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { DictionaryService } from 'src/app/shared/services/dictionary.service';
import { MapLayerComponentService } from '../../../service/map-layer-component.service';
import { DynamicComponent } from 'src/app/shared/models';
import { CaseTestService } from '../case-test.service';
import { SlModalService } from 'src/app/shared/modules/sl-modal/sl-modal.service';
import { SlValidateService } from 'src/app/shared/modules/sl-validate/sl-validate.service';
import * as L from 'leaflet';
import { MapDrawToolService } from '../../map-draw-tool/map-draw-tool.service';

@Component({
  selector: 'app-case-test-handle',
  templateUrl: './case-test-handle.component.html',
  styleUrls: ['./case-test-handle.component.less']
})
export class CaseTestHandleComponent implements OnInit, OnDestroy {
  list: Array<any> = [];
  name: string = '';
  content: string = '';
  isEdit: boolean = false;
  isMock: boolean = false;
  // 组合类型(状态)
  shipStatusList!: any[];
  speed: number = -1;
  status: string | undefined;
  currentData: { id?: string, name?: string, content?: string } = {}
  isSpining: boolean = false;
  private coordinateSubscription!: Subscription;

  private currentMapTool: DynamicComponent | undefined;

  constructor(
    private mapLayerService: MapLayerComponentService,
    private dictService: DictionaryService,
    private mapToolService: MapDrawToolService,
    private service: CaseTestService,
    private slModalService: SlModalService,
    private slValidateService: SlValidateService
  ) { }
  ngOnDestroy(): void {
    this.unSubscription();
    this.removeMapTool();
  }

  private removeMapTool() {
    if (this.currentMapTool) {
      this.mapLayerService.removeComponentByUUID(this.currentMapTool.uuid!);
      this.currentMapTool = undefined;
    }
  }

  private unSubscription() {
    if (this.coordinateSubscription) {
      this.coordinateSubscription.unsubscribe();
    }
  }


  ngOnInit(): void {
    this.getShipStatusList();
    const { params } = this['data'];
    if (params) {
      this.getInfo(params.id, params.type)
    }

  }

  private getInfo(id: string, type: string) {
    if (id) {
      if (type) {
        type == 'mock' ? this.isMock = true : this.isEdit = true;
      }
      this.list = [];
      this.isSpining = true;
      this.service.getInfo(id).then((res: any) => {
        // this.isSpining = false;
        this.currentData = res;
        const { name, content } = res;
        this.name = name;
        const list: Array<string> = content.split(';')
        list.forEach(ele => {
          const [lng, lat, speed, code, time] = ele.split(',');
          const item = { lng, lat, speed, code, time };
          this.list.push(item);
        })
        if (this.list.length) this.initPoint(this.list);
      }).catch(() => {
        this.isSpining = false;
      })
    }
  }

  private getShipStatusList() {
    this.dictService
      .getDicList('PL_HHZT')
      .then((res: Array<any>) => {
        this.shipStatusList = res;
      });
  }

  private initPoint(list: Array<any>) {
    const latlngs: Array<L.LatLng> = list.map(ele => L.latLng(ele.lat, ele.lng))
    this.currentMapTool = this.mapLayerService.addComponent({
      type: 'layer',
      name: 'MapToolLineLayerComponent',
      data: {
        params: { latlngs, fitBounds: true }
      }
    })
  }

  addPoint() {
    this.removeMapTool();
    this.unSubscription();
    this.currentMapTool = this.mapLayerService.addComponent({
      type: 'layer',
      name: 'MapToolLineLayerComponent',
    })
    this.coordinateSubscription = this.mapToolService.change$
      .subscribe((res) => {
        if (res) {
          if (res.complete) {
            this.unSubscription();
          } else {
            const latlng = res.latlng;
            const time = this.list.length == 0 ? 0 : 1;
            const item: any = { lat: latlng.lat, lng: latlng.lng, time }
            if (this.speed > -1) {
              item.speed = this.speed
            }
            if (this.status) {
              item.code = this.status;
            }
            this.list.push(item)
          }
        }
      });
  }
  start() {
    this.slModalService.openPromptModal({
      type: 'confirm',
      content: '确定开始模拟吗？',
      okCb: () => {
        if (!this.currentData.id) return;
        this.service.start(this.currentData.id!).then(() => {
          this.slModalService.openPromptModal({
            type: 'success',
            content: '模拟成功！',
          })
        }).catch(err => {
          this.slModalService.openPromptModal({ type: 'error', content: '模拟失败！' })
        })
      }
    })
  }
  close() {
    this.mapLayerService.removeComponent({ name: 'CaseTestHandleComponent', destroy: true })
  }
  save(validateForm: NgForm) {
    const isValidate = this.slValidateService.validateFormWithAlert(validateForm);
    if (!isValidate) return;
    // 拼接字符串
    this.content = '';
    this.list.forEach(ele => {
      //经度、纬度、航速、状态、时间
      this.content += ele.lng + ',' + ele.lat + ',' + ele.speed + ',' + ele.code + ',' + ele.time + ';'
    })
    if (this.content.length) {
      this.content = this.content.substring(0, this.content.length - 1)
      this.slModalService.openPromptModal({
        type: 'confirm',
        content: '确定保存吗？',
        okCb: () => {
          const data: any = { name: this.name, content: this.content };
          if (this.isEdit) data.id = this.currentData.id
          this.service.save(data).then(res => {
            this.service.notifyRefresh();
            this.slModalService.openPromptModal({
              type: 'success',
              content: '保存成功！',
              okCb: () => {
                this.close()
              }
            })
          }).catch(err => {
            this.slModalService.openPromptModal({ type: 'error', content: '保存失败！' + err })
          })
        }
      })
    }
  }
}
