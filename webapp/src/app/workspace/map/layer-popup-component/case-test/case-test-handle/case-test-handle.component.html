<div class="case-test-handle-container">
    <form #validateForm="ngForm">
        <div class="header">
            <a *ngIf="!isEdit&&!isMock" href="javascript:void(0)" (debounceClick)="addPoint()">海图取点</a>
            <div class="item">
                <label>名称：</label>
                <input [style.width.px]="isMock?320:280" [slValidate]="{required:true,label:'用例名称'}" nz-input
                    type="text" name="name" [(ngModel)]="name" placeholder="请输入用例名称">
            </div>
            <ng-container *ngIf="!isEdit&&!isMock">
                <div class="item">
                    <label>默认航速：</label>
                    <input style="width: 70px;" nz-input type="number" name="speedNum" [(ngModel)]="speed"
                        placeholder="速度（方便填写）">
                    <span>节</span>
                </div>
                <div class="item">
                    <label>默认状态：</label>
                    <nz-select style="width: 140px;" name="status" nzPlaceHolder="请选择状态" nzAllowClear
                        [(ngModel)]="status">
                        <nz-option *ngFor="let item of shipStatusList" [nzValue]="item.itemCode"
                            [nzLabel]="item.itemName">
                        </nz-option>
                    </nz-select>
                </div>
            </ng-container>

        </div>
        <div class="body">
            <table class="inner-table">
                <colgroup>
                    <col [attr.width]="50" />
                    <col [attr.width]="240" />
                    <col [attr.width]="90" />
                    <col [attr.width]="160" />
                    <col [attr.width]="260" />
                    <!--大于5条出现滚动条，width 和滚动条宽度一致-->
                    <col *ngIf="list.length > 5" [attr.width]="5" />
                </colgroup>
                <tr class="t-head">
                    <td class="serial">序号</td>
                    <td class="coordinate">经纬度</td>
                    <td>航速（节）</td>
                    <td>状态</td>
                    <td>数据获取时间</td>
                </tr>
            </table>
            <div class="table-content-container">
                <div class="table-content-container__inner">
                    <table class="inner-table">
                        <colgroup>
                            <col [attr.width]="50" />
                            <col [attr.width]="240" />
                            <col [attr.width]="90" />
                            <col [attr.width]="160" />
                            <col [attr.width]="260" />
                        </colgroup>
                        <tr class="t-content" *ngFor="let item of list;index as j">
                            <td class="serial">{{j+1}}</td>
                            <td class="coordinate">
                                <input [slValidate]="{required:true,label:'第'+(j+1)+'行纬度'}" class="d" name="lat{{j}}"
                                    required type="number" nz-input [ngModel]="item?.lat" />
                                <sup>°</sup>
                                <span class="unit">N</span>
                                <input [slValidate]="{required:true,label:'第'+(j+1)+'行经度'}" class="d" name="lng{{j}}"
                                    required type="number" nz-input [ngModel]="item?.lng" />
                                <sup>°</sup>
                                <span class="unit">E</span>
                            </td>
                            <td class="text-center">
                                <input [slValidate]="{required:true,label:'第'+(j+1)+'行航速'}" type="number"
                                    name="speed{{j}}" nz-input class="speed" [(ngModel)]="item.speed">
                            </td>
                            <td>
                                <nz-select class="setting-select" required
                                    [slValidate]="{required:true,label:'第'+(j+1)+'行状态'}" name="code{{j}}"
                                    nzPlaceHolder="请选择状态" nzAllowClear [(ngModel)]="item.code">
                                    <nz-option *ngFor="let item of shipStatusList" [nzValue]="item.itemCode"
                                        [nzLabel]="item.itemName">
                                    </nz-option>
                                </nz-select>
                            </td>
                            <td>
                                <span>间隔</span>
                                <input nz-input type="number" [slValidate]="{required:true,label:'第'+(j+1)+'行时间'}"
                                    required name="time{{j}}" class="minute" [(ngModel)]="item.time">
                                <span>分钟</span>
                                <span style="margin-left:6px">{{item.time | caseTestDate:list:j}}</span>
                            </td>
                        </tr>
                        <tr *ngIf="!list||list.length==0">
                            <td colspan="5">
                                <nz-spin style="text-align: center;padding: 15px 0;" *ngIf="isSpining;else emptyBlock"
                                    nzTip="数据查询中,请稍后..." nzSimple>
                                </nz-spin>
                                <ng-template #emptyBlock>
                                    <nz-empty></nz-empty>
                                </ng-template>
                            </td>

                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </form>
    <div class="footer">
        <button class="sl-default-btn" (click)="close()">取消</button>
        <button *ngIf="isMock;else addBlock" class="sl-primary-btn" (click)="start()">开始模拟</button>
        <ng-template #addBlock>
            <button class="sl-primary-btn" (click)="save(validateForm)">保存</button>
        </ng-template>
    </div>
</div>