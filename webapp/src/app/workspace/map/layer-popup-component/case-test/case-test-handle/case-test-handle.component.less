@import "/src/styles/mixins";
.case-test-handle-container {
  .header {
    padding: 8px 10px;
    display: flex;
    align-items: center;
    .item {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-left: 20px;
    }
  }
  .body,
  .footer {
    padding: 0 10px 10px 10px;
  }
  .body {
    table.inner-table {
      width: 100%;
      tr {
        &.t-head {
          td {
            background-color: #e7f1ff;
            text-align: center;
          }
        }
        td {
          &.serial {
            padding: 8px 0;
            width: 40px;
            text-align: center;
          }
          &.text-center {
            text-align: center;
          }
          .unit {
            color: #666;
          }
          sup {
            margin-left: 10px;
            margin-right: 10px;
          }
          .setting-select {
            width: 140px;
          }
          .speed {
            width: 70px;
          }
          input[type="number"] {
            &::-webkit-outer-spin-button,
            &::-webkit-inner-spin-button {
              -webkit-appearance: none;
            }
            -moz-appearance: textfield;
            &.d {
              width: 100px;
            }
            &.minute {
              width: 60px;
            }
            & + sup {
              margin: 0;
            }
          }
        }
      }
    }
    .table-content-container {
      overflow: hidden;
      height: 190px; // 5条数据的高度
      .scrollbars(5px,#C8C9CC);
      .table-content-container__inner {
        overflow-y: auto;
        height: 100%;
      }
    }
  }
  .footer {
    display: flex;
    justify-content: center;
    margin-top: 15px;
    button + button {
      margin-left: 15px;
    }
  }
}
