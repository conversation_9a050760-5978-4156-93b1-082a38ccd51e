<div class="case-test-container">
    <div class="header">
        <button class="sl-primary-btn" (click)="addTest()">新增</button>
        <a class="sl-primary-btn" href="/api/Content/download">测试下载</a>
    </div>
    <div class="body">
        <!-- <table class="sl-table-list">
            <thead class="t-head">
                <tr>
                    <td class="serial">序号</td>
                    <td style="width:300px;">用例</td>
                    <td style="width: 130px;">操作</td>
                </tr>
            </thead>
            <tbody class="t-content">
                <tr *ngFor="let item of list;index as i">
                    <td class="serial">{{i+1}}</td>
                    <td>{{item.name}}</td>
                    <td class="btns" style="width: 130px;">
                        <a href="javascript:void(0)" class="btn-orange-text" (click)="del(item)">
                            删除
                        </a>
                        <a href="javascript:void(0)" class="btn-blue-text" (click)="edit(item)">
                            编辑
                        </a>
                        <a href="javascript:void(0)" class="btn-cyan-text" (click)="satart(item)">
                            模拟
                        </a>
                    </td>
                </tr>
                <tr *ngIf="!list||list.length==0">
                    <td colspan="3">
                        <nz-spin *ngIf="isSpining;else emptyBlock" nzTip="数据查询中,请稍后..." nzSimple>
                        </nz-spin>
                        <ng-template #emptyBlock>
                            <nz-empty></nz-empty>
                        </ng-template>
                    </td>
                </tr>
            </tbody>
        </table> -->


        <table class="sl-table-list inner-table">
            <colgroup>
                <col [attr.width]="50" />
                <col [attr.width]="290" />
                <col [attr.width]="130" />
                <!--大于5条出现滚动条，width 和滚动条宽度一致-->
                <col *ngIf="list.length > 5" [attr.width]="5" />
            </colgroup>
            <thead class="t-head">
                <tr>
                    <td class="serial">序号</td>
                    <td>用例</td>
                    <td>操作</td>
                    <!-- <td class="gutter" *ngIf="list.length > 5"></td> -->
                </tr>
            </thead>
        </table>
        <div class="table-content-container">
            <div class="table-content-container__inner">
                <table class="sl-table-list inner-table">
                    <colgroup>
                        <col [attr.width]="50" />
                        <col [attr.width]="290" />
                        <col [attr.width]="130" />
                    </colgroup>
                    <tbody class="t-content">
                        <tr *ngFor="let item of list;index as i">
                            <td class="serial">{{i+1}}</td>
                            <td>{{item.name}}</td>
                            <td class="btns" style="width: 130px;">
                                <a href="javascript:void(0)" class="btn-orange-text" (click)="del(item)">
                                    删除
                                </a>
                                <a href="javascript:void(0)" class="btn-blue-text" (click)="edit(item)">
                                    编辑
                                </a>
                                <a href="javascript:void(0)" class="btn-cyan-text" (click)="satart(item)">
                                    模拟
                                </a>
                            </td>
                        </tr>
                        <tr *ngIf="!list||list.length==0">
                            <td colspan="3">
                                <nz-spin style="text-align: center;padding: 15px 0;" *ngIf="isSpining;else emptyBlock"
                                    nzTip="数据查询中,请稍后..." nzSimple>
                                </nz-spin>
                                <ng-template #emptyBlock>
                                    <nz-empty></nz-empty>
                                </ng-template>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>