import { CaseTestDatePipe } from './case-test-date.pipe';
import { CaseTestHandleComponent } from './case-test-handle/case-test-handle.component';
import { CaseTestComponent } from './case-test.component';

//angular 组件注册
export const CASE_TEST_COMPONENTS = [
    CaseTestComponent,
    CaseTestHandleComponent,
    CaseTestDatePipe
];

// 动态组件配置
export const DYNAMIC_CASE_TEST_COMPONENTS = [
    {
        name: 'CaseTestComponent',
        component: CaseTestComponent,
    },
    {
        name: 'CaseTestHandleComponent',
        component: CaseTestHandleComponent
    }
];
