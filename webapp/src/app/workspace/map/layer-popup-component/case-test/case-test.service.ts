import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import { BaseSearch } from 'src/app/shared/models';
import { BaseHttpService } from 'src/app/shared/services/base-http.service';

@Injectable({
  providedIn: 'root'
})
export class CaseTestService {
  private baseUrl = '/api/Mockship';

  private saveOkSub: Subject<any> = new Subject();

  constructor(private baseService: BaseHttpService) { }

  get change() {
    return this.saveOkSub.asObservable();
  }

  notifyRefresh() {
    this.saveOkSub.next(new Date().getTime())
  }

  getList(search: BaseSearch) {
    return this.baseService.post(this.baseUrl + '/getList', search);
  }

  getInfo(id: string) {
    return this.baseService.get(this.baseUrl + '/getInfo/' + id);
  }

  save(data: { name: string, content: string }) {
    return this.baseService.post(this.baseUrl + '/save', data);
  }

  del(id: string) {
    return this.baseService.get(this.baseUrl + '/delete' + id);
  }

  start(id: string) {
    return this.baseService.get(`${this.baseUrl}/start/${id}`)
  }

}
