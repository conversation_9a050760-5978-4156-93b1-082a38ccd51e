<div class="protect-handle-container">
    <!--公用表单 start-->
    <form #validateForm="ngForm">
        <table class="handle-table">
            <tr>
                <td class="label required">
                    <span>保护区名称：</span>
                </td>
                <td class="control">
                    <input name="areaName" [(ngModel)]="proarea.areaName" [slValidate]="{required:true,label:'保护区名称'}"
                        style="width: 232px;" type="text" nz-input placeholder="请输入保护区名称" />
                </td>
                <td class="label required">
                    <span>海底电缆保护范围：</span>
                </td>
                <td class="control">
                    <input name="cableDistance" [(ngModel)]="proarea.cableDistance"
                        [slValidate]="{required:true,label:'海底电缆保护范围'}" type="number" nz-input placeholder="请输入保护范围" />
                    <span class="unit">&nbsp;米</span>
                </td>
                <td class="label required">
                    <span>风机保护范围：</span>
                </td>
                <td class="control">
                    <input name="fanDistance" [(ngModel)]="proarea.fanDistance"
                        [slValidate]="{required:true,label:'风机保护范围'}" type="number" nz-input placeholder="请输入保护范围" />
                    <span class="unit">&nbsp;米</span>
                </td>
                <td>
                    <a href="/api/Content/download" style="margin-left:10px">下载模板</a>
                </td>
                <td class="handle" style="width: 240px;">
                    <nz-upload [nzAccept]="acceptStr" [nzLimit]="1" [nzFileListRender]="fileListTpl"
                        class="protect-data-upload" nzAction="/api/Content/upload" (nzChange)="uploadTplFile($event)">
                        <button sl-button slType="success">导入</button>
                    </nz-upload>
                </td>
                <td>
                    <ng-template #fileListTpl let-list>
                        <ng-container *ngFor="let item of list;index as i ">
                            <span *ngIf="i==list.length-1">
                                {{item.name}}
                            </span>
                        </ng-container>
                    </ng-template>
                </td>
            </tr>
        </table>
    </form>
    <!--公用表单 end-->
    <!--tabset start-->
    <div class="tabset-container">
        <ul>
            <li *ngFor="let item of tabList;index as i;" [class.active]="currentIndex==i">
                <a href="javascript:void(0)" (click)="switchTab(i)">
                    <i [ngClass]="currentIndex==i?item.icon+'-active-24':item.icon+'-24'"></i>
                    <span>{{item.title}}</span>
                </a>
            </li>
        </ul>
        <div class="title">
            <span>{{tabList[currentIndex].title}}</span>
            <!-- <button sl-button slType="success">导入</button> -->
        </div>
    </div>
    <!--tabset end-->
    <!--数据集主体table start-->
    <div class="list-table-container">
        <ng-container *ngFor="let item of dynamicList">
            <div [hidden]="currentDynamic.name!=item.name">
                <app-dynamic-compopnent [dynamicCom]="item"></app-dynamic-compopnent>
            </div>
        </ng-container>
    </div>
    <!--数据集主体table end-->
</div>