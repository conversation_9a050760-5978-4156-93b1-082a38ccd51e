import { proareaType } from './../windpower-protect.model';
import { Component, Input } from '@angular/core';
import { NgForm, } from '@angular/forms';
import * as _ from 'lodash';
import { DynamicComponentData } from 'src/app/shared/models/dynamic-component.model';
import { uuid } from 'src/app/shared/utils';
import { UUIDModel } from 'src/app/workspace/workspace-shared/models/uuid_model';
import { environment } from 'src/environments/environment';
import { WindpowerProtectService } from "../windpower-protect.service";
@Component({
    selector: 'app-protect-handle-table',
    template: ``
})
export class ProtectHandleTableCommon<T extends UUIDModel> {
    @Input() data: DynamicComponentData = {}
    list: Array<T> = []
    // 缓存数据主要是为了在加载航标列表时可以通过滚动条滚动加载
    cachedList: Array<T> = []
    isEdit: boolean = false
    // 是否是航标列表页面
    isNavType: boolean = false
    type?: proareaType
    spining: boolean = false
    startSlicePos: number = 0 // 截取的起点位置
    endSlicePos: number = 6 // 切割的起点位置
    perSliceCount: number = 6 // 每次切割的长度
    loadComplete: boolean = false // 数据切割完成

    // 列表最大显示行数:5（超过5条数据出现滚动条）
    get maxLen() {
        return this.list.length > 5
    }
    constructor(
        protected service: WindpowerProtectService,
    ) {

    }

    init() {
        const { params } = this.data
        if (params && params.list) {
            this.list = params.list
            this.isEdit = true
        } else {
            if (environment.mockData) {
                this.mockData()
            }
        }
    }

    mockData() { }

    /**
    * 添加item
    */
    addItem() {
        const item = <T>{ uuid: uuid() }
        this.list.push(item)
        if (this.isNavType) {
            // 如果达到底部 
            if (this.isEdit) {
                if (this.loadComplete) {
                    this.cachedList.push(item)
                }
            } else {
                this.cachedList.push(item)
            }
        }
    }

    /**
     * 删除
     * @param item 
     * @param index 
     */
    delItem(item: any, index: number) {
        this.list.splice(index, 1)
        if (this.isNavType && this.isEdit) {
            this.cachedList.splice(index, 1)
            this.startSlicePos = this.cachedList.length
            this.endSlicePos = this.startSlicePos + this.perSliceCount
        }
    }

    /**
     * 复制
     * @param item 
     * @param index 
     */
    copyItem(item: any, index: number) {
        const mark = _.cloneDeep(item)
        mark.uuid = uuid()
        this.list.splice(index + 1, 0, mark)
        if (this.isNavType && this.isEdit) {
            this.cachedList.splice(index + 1, 0, mark)
            this.startSlicePos = this.cachedList.length
            this.endSlicePos = this.startSlicePos + this.perSliceCount
        }
    }

    /**
     * trackBy
     * @param index 
     * @param item 
     * @returns 
     */
    trackByItems(index: number, item: any): string {
        return item.uuid!;
    }

    /**
     *
     *  下一步（保存）操作(需要校验)
     * @param {NgForm} validateForm
     * @param {number} index
     * @memberof ProtectHandleTableCommon
     */
    nextStep(validateForm: NgForm) {
        // 先校验列表数据是否填写完整
        const isValidate = this.service.validateFormWithAlert(validateForm);
        if (isValidate) {
            this.service.tabState = { form: validateForm, list: this.list }
        }
    }
    // 取消
    cancel() {
        this.service.closePopup('ProtectHandleComponent')
        this.service.removeComponent({ id: '', type: 'edit', componentType: 'proarea' })
    }

    // 删除
    del() {
    }
}