@import "/src/styles/mixins";
.coordinate-container-outer {
  overflow: hidden;
  .scrollbars(5px, #C8C9CC, rgba(0, 0, 0, 0));
  .coordinate-container-inner {
    overflow-y: auto;
    height: 100%;
  }
}
.table-container__footer {
  height: 66px;
  padding: 0 48px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  button + button {
    margin-left: 20px;
  }
}
.table-container_handle {
  display: flex;
  height: 40px;
  align-items: center;
  text-align: center;
  justify-content: center;
  border: 1px solid rgba(215, 219, 232, 1);
  &.no-data {
    border-top: none;
  }
  & > a {
    display: inline-flex;
    align-items: center;
    i {
      margin-right: 6px;
    }
  }
}

table {
  tr {
    border-top: 1px solid rgba(215, 219, 232, 1);
    &:last-child {
      border-bottom: 1px solid rgba(215, 219, 232, 1);
    }
    &.title > td {
      background: #eef4fe;
      &.required {
        position: relative;
        span::before {
          content: "*";
          color: red;
          width: 10px;
          height: 10px;
          display: inline-block;
        }
      }
    }
    &.content > td {
      // input {
      //   background: #fafafa;
      // }
      input + sup {
        margin-left: 2px;
        margin-right: 2px;
      }
    }
    &.content:last-child {
      border-bottom: none;
    }
    &.content.first {
      border-top: none;
    }
    td {
      text-align: center;
      height: 40px;
      padding: 0 4px;
      border-left: 1px solid rgba(215, 219, 232, 1);
      &:last-child {
        border-right: 1px solid rgba(215, 219, 232, 1);
      }
    }
  }
}
