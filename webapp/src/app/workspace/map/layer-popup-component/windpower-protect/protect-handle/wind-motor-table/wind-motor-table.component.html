<div class="table-container" [style.paddingRight.px]="maxLen?5:0">
    <div class="table-container__title" [style.paddingRight.px]="maxLen?5:0">
        <table>
            <ng-container [ngTemplateOutlet]="tableCol"></ng-container>
            <tr class="title">
                <td class="serial">序号</td>
                <td class="required"><span>风机名称</span></td>
                <td class="required"><span>设计位置</span></td>
                <td><span>备注</span></td>
                <td class="handle"><span>操作</span></td>
            </tr>
        </table>
    </div>
    <form #validateForm="ngForm">
        <div class="coordinate-container-outer" [style.height]="maxLen?'200px':'auto'">
            <div class="coordinate-container-inner">
                <table>
                    <ng-container [ngTemplateOutlet]="tableCol"></ng-container>
                    <tr class="content" *ngFor="let item of list;index as i;trackBy:trackByItems" [class.first]="i==0">
                        <td class="serial">{{i+1}}</td>
                        <td>
                            <input type="text" name="fanName-{{item.uuid}}" [(ngModel)]="item.name" nz-input
                                [slValidate]="{required:true,label:'第'+(i+1)+'行风机名称'}" />
                        </td>
                        <td>
                            <input type="number" name="latD-{{item.uuid}}"
                                [slValidate]="{required:true,label:'第'+(i+1)+'行纬度（度）',type:'la_degree'}"
                                [(ngModel)]="item.latD" nz-input style="width: 50px;" />
                            <sup>°</sup>
                            <input type="number" name="latM-{{item.uuid}}"
                                [slValidate]="{required:true,label:'第'+(i+1)+'行纬度（分）',type:'la_minute'}"
                                [(ngModel)]="item.latM" nz-input style="width: 50px;" />
                            <sup>′</sup>
                            <input type="number" name="latS-{{item.uuid}}"
                                [slValidate]="{required:true,label:'第'+(i+1)+'行纬度（秒）',type:'la_second'}"
                                [(ngModel)]="item.latS" nz-input style="width: 74px;" />
                            <sup>″</sup>
                            <span class="unit" style="margin-left: 4px;margin-right: 6px;">N</span>
                            <input type="number" name="lngD-{{item.uuid}}"
                                [slValidate]="{required:true,label:'第'+(i+1)+'行经度（度）',type:'lo_degree'}"
                                [(ngModel)]="item.lngD" nz-input style="width: 50px;" />
                            <sup>°</sup>
                            <input type="number" name="lngM-{{item.uuid}}"
                                [slValidate]="{required:true,label:'第'+(i+1)+'行经度（分）',type:'lo_minute'}"
                                [(ngModel)]="item.lngM" nz-input style="width: 50px;" />
                            <sup>′</sup>
                            <input type="number" name="lngS-{{item.uuid}}"
                                [slValidate]="{required:true,label:'第'+(i+1)+'行经度（秒）',type:'lo_second'}"
                                [(ngModel)]="item.lngS" nz-input style="width: 74px;" />
                            <sup>″</sup>
                            <span class="unit" style="margin-left: 4px;">E</span>
                        </td>
                        <td>
                            <input type="text" [(ngModel)]="item.remark" nz-input name="remark-{{item.uuid}}" />
                        </td>
                        <td class="handle">
                            <a style="color:#2D74EF;margin-right: 16px;" href="javascript:void(0)"
                                (click)="copyItem(item,i)">复制</a>
                            <a style="color:#ED1313;" href="javascript:void(0)" (click)="delItem(item,i)">删除</a>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        <div class="table-container_handle" [class.no-data]="list.length==0" [style.marginRight.px]="maxLen?5:0">
            <a href="javascript:void(0)" (click)="addItem()"><i class="sl-plus-circle-20"></i>添加</a>
        </div>
        <!--footer start-->
        <div class="table-container__footer">

            <button sl-button type="button" (click)="cancel()">取消</button>
            <!-- <button sl-button type="button" *ngIf="isEdit" slType="danger" (click)="del()">删除</button> -->
            <button sl-button type="button" slType="primary" (click)="nextStep(validateForm)">保存</button>
        </div>
        <!--footer end-->
    </form>
</div>
<ng-template #tableCol>
    <colgroup>
        <col style="width: 55px;" />
        <col style="width: 300px;" />
        <col style="width: 500px;" />
        <col style="width: 450px;" />
        <col style="width: 200px;" />
    </colgroup>
</ng-template>