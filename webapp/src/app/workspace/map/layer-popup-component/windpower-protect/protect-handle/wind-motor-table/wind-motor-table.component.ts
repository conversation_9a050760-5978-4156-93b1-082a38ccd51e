import { ProtectHandleTableCommon } from './../protect-handle-table-common';
import { Component, OnInit, OnDestroy, ViewChild, AfterViewInit } from '@angular/core';
import * as _ from 'lodash';
import { WindpowerProtectService } from '../../windpower-protect.service';
import { NgForm } from '@angular/forms';
import { TblFan } from 'src/app/workspace/workspace-shared/models/tbl_fan';
import { uuid } from 'src/app/shared/utils';
@Component({
  selector: 'app-wind-motor-table',
  templateUrl: './wind-motor-table.component.html',
  styleUrls: ['../protect-handle-table-common.less', './wind-motor-table.component.less']
})
export class WindMotorTableComponent extends ProtectHandleTableCommon<TblFan> implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild(NgForm) validateForm!: NgForm
  constructor(
    protected service: WindpowerProtectService,
  ) {
    super(service,)
  }
  ngAfterViewInit(): void {
    this.service.setTabForm(this.validateForm, 3, this.list)
  }
  ngOnInit(): void {
    this.type = 'fan'
    this.init()

  }

  ngOnDestroy(): void {
  }

  mockData(): void {
    // 模拟数据
    this.list = [
      { name: '风机1', latD: 22, latM: 47, latS: 2.0585, lngD: 116, lngM: 12, lngS: 14.6527, remark: '' },
      { name: '后湖风电场1号风机', latD: 22, latM: 46, latS: 19.6375, lngD: 116, lngM: 12, lngS: 8.1409, remark: '' },
      { name: '后湖风电场2号风机', latD: 22, latM: 45, latS: 57.4539, lngD: 116, lngM: 12, lngS: 31.3689, remark: '' },
      { name: '后湖风电场3号风机', latD: 22, latM: 45, latS: 35.5631, lngD: 116, lngM: 12, lngS: 54.8388, remark: '' },
      { name: '后湖风电场4号风机', latD: 22, latM: 44, latS: 41.2565, lngD: 116, lngM: 12, lngS: 11.2359, remark: '' },
      { name: '后湖风电场5号风机', latD: 22, latM: 44, latS: 17.5615, lngD: 116, lngM: 10, lngS: 54.4501, remark: '' },
      { name: '后湖风电场6号风机', latD: 22, latM: 43, latS: 54.0015, lngD: 116, lngM: 9, lngS: 40.545, remark: '' },
      { name: '后湖风电场7号风机', latD: 22, latM: 43, latS: 34.3574, lngD: 116, lngM: 8, lngS: 37.3102, remark: '' },
      { name: '后湖风电场8号风机', latD: 22, latM: 43, latS: 14.7845, lngD: 116, lngM: 7, lngS: 35.2287, remark: '' },
      { name: '后湖风电场9号风机', latD: 22, latM: 42, latS: 54.1759, lngD: 116, lngM: 6, lngS: 30.3981, remark: '' },
      { name: '后湖风电场10号风机', latD: 22, latM: 42, latS: 31.6947, lngD: 116, lngM: 5, lngS: 17.5971, remark: '' },
      { name: '后湖风电场11号风机', latD: 22, latM: 42, latS: 6.2057, lngD: 116, lngM: 3, lngS: 58.0154, remark: '' },
      { name: '后湖风电场12号风机', latD: 22, latM: 41, latS: 47.2869, lngD: 116, lngM: 2, lngS: 57.3606, remark: '' },
      { name: '后湖风电场13号风机', latD: 22, latM: 42, latS: 20.6553, lngD: 116, lngM: 2, lngS: 43.9213, remark: '' },
      { name: '后湖风电场14号风机', latD: 22, latM: 42, latS: 53.3428, lngD: 116, lngM: 2, lngS: 30.8351, remark: '' },
      { name: '后湖风电场15号风机', latD: 22, latM: 43, latS: 28.462, lngD: 116, lngM: 2, lngS: 16.7841, remark: '' },
      { name: '后湖风电场16号风机', latD: 22, latM: 43, latS: 49.6466, lngD: 116, lngM: 3, lngS: 19.8402, remark: '' },
      { name: '后湖风电场17号风机', latD: 22, latM: 44, latS: 14.5091, lngD: 116, lngM: 3, lngS: 59.8986, remark: '' },
      { name: '后湖风电场18号风机', latD: 22, latM: 44, latS: 4.5844, lngD: 116, lngM: 4, lngS: 35.9122, remark: '' },
      { name: '后湖风电场19号风机', latD: 22, latM: 43, latS: 44.9245, lngD: 116, lngM: 5, lngS: 51.0685, remark: '' },
      { name: '后湖风电场20号风机', latD: 22, latM: 43, latS: 55.1564, lngD: 116, lngM: 7, lngS: 11.4588, remark: '' },
      { name: '后湖风电场21号风机', latD: 22, latM: 44, latS: 22.5016, lngD: 116, lngM: 8, lngS: 7.6777, remark: '' },
      { name: '后湖风电场22号风机', latD: 22, latM: 44, latS: 50.7986, lngD: 116, lngM: 9, lngS: 5.3266, remark: '' },
      { name: '后湖风电场23号风机', latD: 22, latM: 45, latS: 21.5267, lngD: 116, lngM: 10, lngS: 9.0664, remark: '' },
      { name: '后湖风电场24号风机', latD: 22, latM: 45, latS: 54.9032, lngD: 116, lngM: 11, lngS: 17.1095, remark: '' },
      { name: '后湖风电场25号风机', latD: 22, latM: 43, latS: 53.54, lngD: 116, lngM: 2, lngS: 30.18, remark: '' },
      { name: '后湖风电场26号风机', latD: 22, latM: 44, latS: 4.944, lngD: 116, lngM: 3, lngS: 27.3, remark: '' },
      { name: '后湖风电场27号风机', latD: 22, latM: 43, latS: 20.48, lngD: 116, lngM: 2, lngS: 57.37, remark: '' },
    ]
    this.list.forEach(ele => ele.uuid = uuid())
  }
}
