import { uuid } from './../../../../../../shared/utils/uuid';
import { NgForm } from '@angular/forms';
import { Component, OnInit, OnDestroy, ViewChild, ContentChild, AfterViewInit } from '@angular/core';
import { WindpowerProtectService } from '../../windpower-protect.service';
import { ProtectHandleTableCommon } from '../protect-handle-table-common';
import { TblCrawl } from 'src/app/workspace/workspace-shared/models/tbl_crawl';
@Component({
  selector: 'app-windpower-area-table',
  templateUrl: './windpower-area-table.component.html',
  styleUrls: ['../protect-handle-table-common.less', './windpower-area-table.component.less']
})
export class WindpowerAreaTableComponent extends ProtectHandleTableCommon<TblCrawl> implements OnInit, AfterViewInit, On<PERSON><PERSON>roy {
  @ViewChild(NgForm) validateForm!: NgForm

  constructor(protected service: WindpowerProtectService) {
    super(service)
  }

  ngAfterViewInit(): void {
    this.service.setTabForm(this.validateForm, 0, this.list)
  }

  ngOnInit(): void {
    this.type = 'crawl'
    this.init()
  }

  ngOnDestroy(): void {
  }

  mockData(): void {
    // 模拟数据
    this.list = [
      { name: '东区', markNum: 'A', latD: 22, latM: 47, latS: 2.0585, lngD: 116, lngM: 12, lngS: 14.6527 },
      { name: '东区', markNum: 'B', latD: 22, latM: 44, latS: 53.0885, lngD: 116, lngM: 14, lngS: 46.4771 },
      { name: '东区', markNum: 'C', latD: 22, latM: 41, latS: 3.3717, lngD: 116, lngM: 2, lngS: 34.8331 },
      { name: '东区', markNum: 'D', latD: 22, latM: 44, latS: 5.709, lngD: 116, lngM: 1, lngS: 21.885 },
      { name: '东区', markNum: 'E', latD: 22, latM: 44, latS: 54.5653, lngD: 116, lngM: 3, lngS: 45.4787 },
      { name: '东区', markNum: 'F', latD: 22, latM: 44, latS: 12.0806, lngD: 116, lngM: 6, lngS: 24.9746 },
      { name: '西区', markNum: 'a', latD: 22, latM: 42, latS: 48.6781, lngD: 115, lngM: 57, lngS: 37.0778 },
      { name: '西区', markNum: 'b', latD: 22, latM: 40, latS: 20.0658, lngD: 116, lngM: 0, lngS: 19.753 },
      { name: '西区', markNum: 'c', latD: 22, latM: 38, latS: 31.7977, lngD: 115, lngM: 54, lngS: 35.5574 },
      { name: '西区', markNum: 'd', latD: 22, latM: 40, latS: 53.488, lngD: 115, lngM: 51, lngS: 57.9258 },
    ]
    this.list.forEach(ele => ele.uuid = uuid())
  }
}
