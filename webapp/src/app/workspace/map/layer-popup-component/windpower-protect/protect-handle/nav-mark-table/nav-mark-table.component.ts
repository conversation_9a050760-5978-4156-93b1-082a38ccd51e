import { NAV_MOCK_DATA } from './mock.data';
import { Dictdetail } from './../../../../../../shared/models/dict-detail.model';
import { AfterViewInit, Component, OnDestroy, OnInit, TemplateRef, ViewChild, ViewContainerRef } from '@angular/core';
import { NgForm } from '@angular/forms';
import * as _ from 'lodash';
import * as $ from 'jquery'
import { DictionaryService } from 'src/app/shared/services/dictionary.service';
import { isEmpty, uuid } from 'src/app/shared/utils';
import { TblNavigation } from 'src/app/workspace/workspace-shared/models/tbl_navigation';
import { WindpowerProtectService } from '../../windpower-protect.service';
import { ProtectHandleTableCommon } from '../protect-handle-table-common';
import { SlModalService } from 'src/app/shared/modules/sl-modal/sl-modal.service';

@Component({
  selector: 'app-nav-mark-table',
  templateUrl: './nav-mark-table.component.html',
  styleUrls: ['../protect-handle-table-common.less', './nav-mark-table.compoent.less']
})
export class NavMarkTableComponent extends ProtectHandleTableCommon<TblNavigation> implements OnInit, AfterViewInit, OnDestroy {
  colorCodeList: Array<Dictdetail> = []
  lightRhythmCodeList: Array<Dictdetail> = []
  navigationTypeList: Array<Dictdetail> = []
  @ViewChild(NgForm) validateForm!: NgForm



  constructor(
    protected service: WindpowerProtectService,
    private slModalService: SlModalService,
    private dictService: DictionaryService) {
    super(service)
  }

  ngAfterViewInit(): void {
    this.service.setTabForm(this.validateForm, 2, this.list)
  }
  ngOnInit(): void {
    this.isNavType = true
    this.type = 'navigation'
    this.init()
    if (this.isEdit) {
      if (this.list.length > this.endSlicePos) {
        this.cachedList = this.list.slice(this.startSlicePos, this.endSlicePos)
        this.startSlicePos = this.endSlicePos
        this.endSlicePos += this.perSliceCount
      }
    }

    this.dictService.getDicList(['PL_AIDSTYPE', 'PL_LIGHTCOLOR', 'PL_RECCODE']).then(([list1, list2, list3]) => {
      this.navigationTypeList = <Array<Dictdetail>>list1
      this.colorCodeList = <Array<Dictdetail>>list2
      this.lightRhythmCodeList = <Array<Dictdetail>>list3
    })
  }

  ngOnDestroy(): void {

  }

  mockData(): void {
    // 模拟数据
    this.list = NAV_MOCK_DATA
    this.list.forEach(ele => ele.uuid = uuid())
  }
  navNextStep(validateForm: NgForm) {
    const errors = this.getVitualMarkErrors()
    if (errors.length) {
      this.slModalService.openPromptModal({
        type: 'error',
        content: errors,
      });
      return
    }
    this.nextStep(validateForm)
  }
  private getVitualMarkErrors() {
    const vitualMarkList: { nav: TblNavigation, index: number }[] = []
    this.list.forEach((ele, index) => {
      if (ele.navigationType == '0105') {
        vitualMarkList.push({ nav: ele, index })
      }
    })
    // const vitualMarkList = this.list.filter(ele => ele.navigationType == '0105')
    const errors: string[] = []
    vitualMarkList.forEach(ele => {
      if (!ele.nav.mmsi) {
        errors.push(`第${ele.index}行AIS航标MMSI不能为空！`)
      }
      if (!ele.nav.aisNameEn) {
        errors.push(`第${ele.index}行AIS航标英文名称不能为空！`)
      }
    })
    return errors
  }
  onScroll($event: Event): void {
    if (!this.isEdit) return
    const target: Element = $event.target as Element;
    const height = $(target).height() || 0
    const scrollHeight = target.scrollHeight
    const scrollTop = $(target).scrollTop() || 0
    if (!this.loadComplete && scrollTop + height >= scrollHeight - 40) {// 到底部了 -40 提前加载
      if (this.startSlicePos > this.list.length - 1) {
        this.loadComplete = true
        return
      }
      this.spining = true
      const items = this.list.slice(this.startSlicePos, this.endSlicePos)
      setTimeout(() => {
        this.cachedList.push(...items)
        this.spining = false
      }, 500);
      this.startSlicePos = this.endSlicePos
      this.endSlicePos += this.perSliceCount
    }
  }
}
