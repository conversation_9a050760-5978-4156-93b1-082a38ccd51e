import { Component, OnInit, <PERSON><PERSON><PERSON><PERSON>, ViewChild, AfterViewInit } from '@angular/core';
import { NgForm } from '@angular/forms';
import { uuid } from 'src/app/shared/utils';
import { TblCable } from 'src/app/workspace/workspace-shared/models/tbl_cable';
import { WindpowerProtectService } from '../../windpower-protect.service';
import { ProtectHandleTableCommon } from '../protect-handle-table-common';
@Component({
  selector: 'app-sea-mark-table',
  templateUrl: './sea-mark-table.component.html',
  styleUrls: ['../protect-handle-table-common.less', './sea-mark-table.component.less']
})
export class SeaMarkTableComponent extends ProtectHandleTableCommon<TblCable> implements OnInit, AfterViewInit, OnDestroy {

  @ViewChild(NgForm) validateForm!: NgForm
  constructor(
    protected service: WindpowerProtectService,
  ) {
    super(service)
  }
  ngAfterViewInit(): void {
    this.service.setTabForm(this.validateForm, 1, this.list)
  }

  ngOnInit(): void {
    this.type = 'cable'
    this.init()
  }

  ngOnDestroy(): void {
  }

  mockData(): void {
    // 模拟数据
    this.list = [
      { name: '海底电缆-1#', markNum: 'A', latD: 22, latM: 41, latS: 29.1269, lngD: 116, lngM: 1, lngS: 56.0548 },
      { name: '海底电缆-1#', markNum: 'B', latD: 22, latM: 40, latS: 45.7143, lngD: 116, lngM: 0, lngS: 20.9275 },
      { name: '海底电缆-2#', markNum: 'a', latD: 22, latM: 42, latS: 13.7213, lngD: 115, lngM: 55, lngS: 17.1728 },
      { name: '海底电缆-2#', markNum: 'b', latD: 22, latM: 43, latS: 42.7318, lngD: 115, lngM: 53, lngS: 52.1509 },
      { name: '海底电缆-2#', markNum: 'c', latD: 22, latM: 45, latS: 14.8115, lngD: 115, lngM: 52, lngS: 50.6249 },
      { name: '海底电缆-2#', markNum: 'd', latD: 22, latM: 46, latS: 9.715, lngD: 115, lngM: 51, lngS: 31.0825 },
      { name: '海底电缆-2#', markNum: 'e', latD: 22, latM: 46, latS: 50.16, lngD: 115, lngM: 51, lngS: 12.91 },
      { name: '海底电缆-2#', markNum: 'f', latD: 22, latM: 46, latS: 50.58, lngD: 115, lngM: 51, lngS: 13.67 },
    ]
    this.list.forEach(ele => ele.uuid = uuid())
  }

}
