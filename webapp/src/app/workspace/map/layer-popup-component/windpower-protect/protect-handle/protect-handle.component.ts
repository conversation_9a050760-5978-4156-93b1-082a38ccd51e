import { HANDLE_TAB_LIST } from './../protect-data';
import { MapLayerComponentService } from './../../../service/map-layer-component.service';
import { TblProarea } from './../../../../workspace-shared/models/tbl_proarea';
import { Component, OnInit, OnDestroy, Input, ViewChild } from '@angular/core';
import { DynamicComponent, DynamicComponentData } from 'src/app/shared/models';
import { NgForm } from '@angular/forms';
import { WindpowerProtectService } from '../windpower-protect.service';
import { Subscription } from 'rxjs';
import { SlModalService } from 'src/app/shared/modules/sl-modal/sl-modal.service';
import { UUIDModel } from 'src/app/workspace/workspace-shared/models/uuid_model';
import { DMSToLatlng, latlngToDMS, latlngWithFormatToDMS } from '../../../utils/map-tools';
import { uuid } from 'src/app/shared/utils';
import * as _ from 'lodash';
import { ProtectHandleTabItem } from '../windpower-protect.model';
import { NzUploadChangeParam } from 'ng-zorro-antd/upload';
import { NzMessageService } from 'ng-zorro-antd/message';
@Component({
  selector: 'app-protect-handle',
  templateUrl: './protect-handle.component.html',
  styleUrls: ['./protect-handle.component.less']
})
export class ProtectHandleComponent implements OnInit, OnDestroy {
  isEdit: boolean = false
  /**导入模板的文件名 */
  fileName: string = '';
  // 上传指定为excel 格式
  acceptStr = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel';

  @Input() data: DynamicComponentData = new DynamicComponentData()

  @ViewChild(NgForm) validateForm!: NgForm

  // 当前tab 角标
  currentIndex: number
  // 保护区
  proarea: TblProarea = new TblProarea()
  /**
   *
   * tab data
   * @memberof ProtectHandleComponent
   */
  tabList: Array<ProtectHandleTabItem> = []
  // 已经加载的动态组件
  dynamicList: Array<DynamicComponent>

  // 当前显示的动态组件
  get currentDynamic() {
    return this.tabList[this.currentIndex].dynamic
  }
  private tabChangeSub$: Subscription

  private tabFormStateSub$: Subscription

  constructor(
    private service: WindpowerProtectService,
    private mapLayerService: MapLayerComponentService,
    private slModalService: SlModalService,
    private msg: NzMessageService
  ) {
    this.tabList = _.cloneDeep(HANDLE_TAB_LIST)
    this.currentIndex = 0
    this.dynamicList = []

    // 下一步或者保存
    this.tabChangeSub$ = this.service.nextTabChange.subscribe((res) => {
      this.nextOrSave(res)
    })
    // 初始化绑定绑定组件的 form  与 list 
    this.tabFormStateSub$ = this.service.tabFormStateChange.subscribe((res: { form: NgForm, index: number, list: Array<UUIDModel> }) => {
      this.tabList[res.index].form = res.form
      this.tabList[res.index].list = res.list
    })
  }

  ngOnInit(): void {
    const { params } = this.data
    if (params && params.infoData) { // 编辑
      const { infoData, tabIndex } = params
      this.isEdit = true
      this.currentIndex = tabIndex
      this.proarea = infoData
      this.tabList.forEach(ele => {
        ele.list = this.proarea[ele.listPro]
        if (ele.list && ele.list.length) {
          ele.list.forEach(ele => {
            ele.uuid = uuid()
            const lat = latlngToDMS(ele.latitude!)
            const lng = latlngToDMS(ele.longitude!)
            ele.latD = lat.d
            ele.latM = lat.m
            ele.latS = lat.s
            ele.lngD = lng.d
            ele.lngM = lng.m
            ele.lngS = lng.s
          })
        }
        ele.dynamic.data = { params: { list: ele.list, type: ele.type } }
      })
    }
    const dynamic = this.tabList[this.currentIndex].dynamic
    this.dynamicList.push(dynamic)
  }

  ngOnDestroy(): void {
    this.tabChangeSub$.unsubscribe()
    this.tabFormStateSub$.unsubscribe()
  }

  /**
   * 上传模板文件
   */
  uploadTplFile(info: NzUploadChangeParam) {
    if (info.file.status === 'done') {
      this.fileName = info.file.name;
      const res = info.file.response;
      if (res && res.rlt == 0) {
        this.msg.success(`${info.file.name} 上传成功！`);
        this.proarea = <TblProarea>res.datas;
        this.tabList.forEach(ele => {
          ele.list = this.proarea[ele.listPro]
          if (ele.list && ele.list.length) {
            ele.list.forEach(ele => {
              ele.uuid = uuid()
              //latitude: "22°27′33.3497″N"
              //longitude: "115°2′1.3989″E"
              const { d: latD, m: latM, s: latS } = latlngWithFormatToDMS(ele.latitude! + '')
              const { d: lngD, m: lngM, s: lngS } = latlngWithFormatToDMS(ele.longitude! + '')
              ele.latD = latD
              ele.latM = latM
              ele.latS = latS
              ele.lngD = lngD
              ele.lngM = lngM
              ele.lngS = lngS
            })
          }
          ele.dynamic.data = { params: { list: ele.list } }
        })
        this.dynamicList = []
        setTimeout(() => {
          const dynamic = this.tabList[this.currentIndex].dynamic
          this.dynamicList.push(dynamic)
        });
      } else {
        this.msg.error(`${res.info || '未知错误'}`);
      }

    } else if (info.file.status === 'error') {
      this.msg.error(`${info.file.name} 上传失败！`);
    }
  }
  /**
   * 切换标签页并加载动态组件
   * 切换标签页校验：当前tab 页面表单校验不通过不允许跳转
   * @param index 
   */
  switchTab(index: number) {
    if (this.currentIndex != index) {
      // 当前tab页面list长度是否通过校验
      const tab = this.tabList[this.currentIndex]
      if (!this.isListLenValid(tab)) return
      // 当前表单是否已经通过校验
      const form = tab.form
      if (!form) return
      if (!this.isFormValid(form)) return
      const dynamic = this.tabList[index].dynamic
      const isExist = this.dynamicList.some(ele => dynamic.name == ele.name)
      // 如果当前组件没有加载
      if (!isExist) {
        const list = this.tabList[index].list
        if (list && list.length) {
          dynamic.data = { params: { list, type: this.tabList[index].type } }
        }
        this.dynamicList.push(dynamic)
      }
      // }
      // 更新index
      this.currentIndex = index
    }
  }


  /**
   *
   * 表单校验
   * @private
   * @param {NgForm} form
   * @memberof ProtectHandleComponent
   */
  private isFormValid(form: NgForm): boolean {
    // 校验公共form 表单
    const commonIsValidate = this.service.validateFormWithAlert(this.validateForm)
    if (!commonIsValidate) return false
    // 校验单个表单
    return this.service.validateFormWithAlert(form)
  }

  /**
   *
   * 校验tab 页面中 list 数组的长度
   * @private
   * @param {ProtectHandleTabItem} tab
   * @memberof ProtectHandleComponent
   */
  private isListLenValid(tab: ProtectHandleTabItem) {
    let result = true
    const { minListLen } = tab
    if (minListLen && minListLen > 0) {
      const len = tab.list?.length
      result = len != undefined && len >= minListLen
    }
    if (!result) {
      this.slModalService.openPromptModal({
        type: 'error',
        content: `${tab.title}至少需要添加${tab.minListLen}条数据`
      })
    }
    return result
  }

  /**
   * 保存或者下一步
   */
  private nextOrSave(data: { form: NgForm }) {
    if (this.currentIndex < 3) { // 下一步
      this.switchTab(this.currentIndex + 1)
    } else { // 保存
      this.tabList.forEach(ele => {
        // const idPro = ele.blockIdPro //区域主键 前端生成随机数赋值给此字段（name 相同设置为同一uuid）
        const listPro = ele.listPro
        const list = ele.list
        this.proarea[listPro] = []
        if (list && list.length) {
          const nameArr: Array<UUIDModel> = [] // 根据name 分组
          for (let i = 0; i < list.length; i++) {
            const item = list[i]
            item.latitude = DMSToLatlng(item.latD!, item.latM!, item.latS!, 8)
            item.longitude = DMSToLatlng(item.lngD!, item.lngM!, item.lngS!, 8)
            item.serialNumber = i + 1
            if (listPro == 'crawlList' || listPro == 'cableList') { // 电子围栏 与 海底电缆 根据是否为相同名称进行分组
              const existItem = nameArr.find(e => e.name == item.name)
              if (!existItem) {
                nameArr.push(item)
                item.blockId = item.uuid
              } else {
                item.blockId = existItem.uuid
              }
            } else { // 航标 风机 
              item.blockId = item.uuid
            }
            this.proarea[ele.listPro].push(item)
          }
        }
      })
      console.log('save', this.tabList);
      console.log('save', this.proarea);
      this.slModalService.openPromptModal({
        type: 'confirm',
        content: '确定保存吗？',
        okCb: () => {
          this.service.save(this.proarea).then(res => {
            this.removeHandleComponent()
            if (this.isEdit) {
              this.service.removeComponent({ id: '', type: 'edit', componentType: 'proarea' })
            }
            this.slModalService.openPromptModal({
              type: 'success',
              content: '保存成功',
              okCb: () => {
                this.refreshListComponent()
                this.refreshLayerComponent()
              }
            })
          }).catch(err => {
            this.slModalService.openPromptModal({
              type: 'error',
              content: err && '保存失败'
            })
          })
        }
      })
    }
  }

  private removeHandleComponent() {
    this.mapLayerService.removeComponent(
      {
        destroy: true,
        name: 'ProtectHandleComponent'
      }
    )
  }

  private refreshListComponent() {
    this.mapLayerService.refreshComponent(
      {
        name: 'ProtectListComponent'
      }
    )
  }
  private refreshLayerComponent() {
    this.mapLayerService.refreshComponent(
      {
        name: 'WindpowerProtectLayerComponent'
      }
    )
  }
}
