<div class="table-container">
    <div class="table-container__title" [style.paddingRight.px]="maxLen?5:0">
        <table>
            <ng-container [ngTemplateOutlet]="tableCol"></ng-container>
            <tr class="title">
                <td class="serial">序号</td>
                <td class="required"><span>航标名称</span></td>
                <td><span>英文名称</span></td>
                <td class="required"><span>航标类别</span></td>
                <td class="required"><span>设计位置</span></td>
                <td><span>MMSI</span></td>
                <td><span>光色</span></td>
                <td><span>闪光节奏</span></td>
                <td><span>周期(s)</span></td>
                <td><span>灯高(m)</span></td>
                <td><span>射程(NM)</span></td>
                <td><span>构造</span></td>
                <td><span>备注</span></td>
                <td class="handle"><span>操作</span></td>
            </tr>
        </table>
    </div>
    <form #validateForm="ngForm">
        <nz-spin [nzSpinning]="spining" nzTip="数据加载中...">
            <div class="coordinate-container-outer" [style.height]="maxLen?'319px':'auto'">
                <div class="coordinate-container-inner" (scroll)="onScroll($event)">
                    <table>
                        <ng-container [ngTemplateOutlet]="tableCol"></ng-container>
                        <tr class="content" *ngFor="let item of isEdit? cachedList:list;index as i;trackBy:trackByItems"
                            [class.first]="i==0">
                            <td class="serial">{{i+1}}</td>
                            <td>
                                <textarea style="width: 146px;resize: none;" rows="3" nz-input [(ngModel)]="item.name"
                                    nz-input name="markName-{{item.uuid}}"
                                    [slValidate]="{required:true,label:'第'+(i+1)+'行航标名称'}"></textarea>
                            </td>
                            <td>
                                <textarea style="width: 108px;resize: none;" rows="3" nz-input
                                    [(ngModel)]="item.aisNameEn" nz-input name="aisNameEn-{{item.uuid}}"></textarea>
                            </td>
                            <td>
                                <nz-select [(ngModel)]="item.navigationType" style="width: 88px"
                                    name="navigationType-{{item.uuid}}"
                                    [slValidate]="{required:true,label:'第'+(i+1)+'行航标类别'}">
                                    <nz-option *ngFor="let item of navigationTypeList" [nzValue]="item.itemCode"
                                        [nzLabel]="item.itemName!"></nz-option>
                                </nz-select>
                            </td>
                            <td>
                                <div class="latlng-container" style="margin-top:5px;">
                                    <input type="number"
                                        [slValidate]="{required:true,label:'第'+(i+1)+'行纬度（度）',type:'la_degree'}"
                                        [(ngModel)]="item.latD" name="latD-{{item.uuid}}" nz-input
                                        style="width: 46px;" />
                                    <sup>°</sup>
                                    <input type="number"
                                        [slValidate]="{required:true,label:'第'+(i+1)+'行纬度（分）',type:'la_minute'}"
                                        [(ngModel)]="item.latM" name="latM-{{item.uuid}}" nz-input
                                        style="width: 46px;" />
                                    <sup>′</sup>
                                    <input type="number"
                                        [slValidate]="{required:true,label:'第'+(i+1)+'行纬度（秒）',type:'la_second'}"
                                        [(ngModel)]="item.latS" name="latS-{{item.uuid}}" nz-input
                                        style="width: 74px;" />
                                    <sup>″</sup>
                                    <span class="unit">N</span>
                                </div>
                                <div class="latlng-container">
                                    <input type="number"
                                        [slValidate]="{required:true,label:'第'+(i+1)+'行经度（度）',type:'lo_degree'}"
                                        [(ngModel)]="item.lngD" name="lngD-{{item.uuid}}" nz-input
                                        style="width: 46px;" />
                                    <sup>°</sup>
                                    <input type="number"
                                        [slValidate]="{required:true,label:'第'+(i+1)+'行经度（分）',type:'lo_minute'}"
                                        [(ngModel)]="item.lngM" name="lngM-{{item.uuid}}" nz-input
                                        style="width: 46px;" />
                                    <sup>′</sup>
                                    <input type="number"
                                        [slValidate]="{required:true,label:'第'+(i+1)+'行经度（秒）',type:'lo_second'}"
                                        [(ngModel)]="item.lngS" name="lngS-{{item.uuid}}" nz-input
                                        style="width: 74px;" />
                                    <sup>″</sup>
                                    <span class="unit">E</span>
                                </div>
                            </td>
                            <td>
                                <input type="number" [slValidate]="{type:'mmsi',label:'第'+(i+1)+'行MMSI'}"
                                    [(ngModel)]="item.mmsi" nz-input name="mmsi-{{item.uuid}}" />
                            </td>
                            <td>
                                <nz-select [(ngModel)]="item.colorCode" style="width: 60px"
                                    name="colorCode-{{item.uuid}}">
                                    <nz-option *ngFor="let item of colorCodeList" [nzValue]="item.itemCode"
                                        [nzLabel]="item.itemName!"></nz-option>
                                </nz-select>
                            </td>
                            <td>
                                <nz-select [(ngModel)]="item.lightRhythmCode" style="width: 110px"
                                    name="lightRhythmCode-{{item.uuid}}">
                                    <nz-option *ngFor="let item of lightRhythmCodeList" [nzValue]="item.itemCode"
                                        [nzLabel]="item.itemName!"></nz-option>
                                </nz-select>
                            </td>
                            <td>
                                <input type="text" [(ngModel)]="item.lightCycle" nz-input
                                    name="lightCycle-{{item.uuid}}" />
                            </td>
                            <td>
                                <input type="text" [(ngModel)]="item.lightHigh" nz-input
                                    name="lightHigh-{{item.uuid}}" />
                            </td>
                            <td>
                                <input type="text" [(ngModel)]="item.lightRange" nz-input
                                    name="lightRange-{{item.uuid}}" />
                            </td>
                            <td>
                                <textarea style="width: 186px;resize: none;" rows="3" nz-input
                                    [(ngModel)]="item.lightStructure" nz-input
                                    name="lightStructure-{{item.uuid}}"></textarea>
                            </td>
                            <td>
                                <textarea style="width: 108px;resize: none;" rows="3" nz-input [(ngModel)]="item.remark"
                                    nz-input name="remark-{{item.uuid}}"></textarea>
                            </td>
                            <td class="handle">
                                <a style="color:#2D74EF;margin-right: 16px;" href="javascript:void(0)"
                                    (click)="copyItem(item,i)">复制</a>
                                <a style="color:#ED1313;" href="javascript:void(0)" (click)="delItem(item,i)">删除</a>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </nz-spin>
        <div class="table-container_handle" [class.no-data]="list.length==0" [style.marginRight.px]="maxLen?5:0">
            <a href="javascript:void(0)" (click)="addItem()"><i class="sl-plus-circle-20"></i>添加</a>
        </div>
        <!--footer start-->
        <div class="table-container__footer">
            <button sl-button type="button" (click)="cancel()">取消</button>
            <button sl-button type="button" slType="primary" (click)="navNextStep(validateForm)">下一步</button>
        </div>
        <!--footer end-->
    </form>
</div>

<ng-template #tableCol>
    <colgroup>
        <col style="width: 55px;" />
        <col style="width: 130px;" />
        <col style="width: 130px;" />
        <col style="width: 100px;" />
        <col style="width: 210px;" />
        <col style="width: 100px;" />
        <col style="width: 80px;" />
        <col style="width: 120px;" />
        <col style="width: 70px;" />
        <col style="width: 70px;" />
        <col style="width: 70px;" />
        <col style="width: 160px;" />
        <col style="width: 90px;" />
        <col style="width: 90px;" />
    </colgroup>
</ng-template>