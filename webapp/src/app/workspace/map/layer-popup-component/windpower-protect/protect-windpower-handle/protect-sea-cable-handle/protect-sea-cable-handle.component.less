@import "/src/styles/mixins";
.windpower-area-container {
  width: 545px;
  padding: 10px 8px;
  background: transparent;
  .table-container {
    width: 100%;
    padding: 2px 10px 11px 10px;
    padding-right: 5px;
    .table-container__title {
      .handle-area {
        display: flex;
        height: 30px;
        padding: 0 12px;
        align-items: center;
        justify-content: flex-end;
        border-top: 1px solid rgba(215, 219, 232, 1);
        border-right: 1px solid rgba(215, 219, 232, 1);
        border-left: 1px solid rgba(215, 219, 232, 1);
      }
    }
    .table-container__body {
      overflow: hidden;
      .scrollbars(5px, #C8C9CC, rgba(0, 0, 0, 0));
      .table-container__body__inner {
        height: 100%;
        overflow-y: auto;
      }
    }
    table {
      tr {
        td {
          border: 1px solid rgba(215, 219, 232, 1);
          overflow: hidden;
          word-break: keep-all;
          white-space: nowrap;
          text-overflow: ellipsis;
          input {
            height: 26px;
            background: #f5f6f8;
            border-radius: 1px;
            border: 1px solid rgba(214, 214, 214, 1);
            padding: 4px 5px;
          }
          &.required > span {
            position: relative;
            &::before {
              content: "*";
              color: red;
              width: 8px;
              height: 8px;
              position: absolute;
              left: -9px;
            }
          }
        }
        &.tr-title {
          td {
            background: #e7f1ff;
            border-bottom: none;
            height: 30px;
          }
        }
        &.tr-content {
          td {
            height: 36px;
          }
        }
      }
    }
  }
}
