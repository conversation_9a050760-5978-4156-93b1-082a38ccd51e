<div class="windpower-area-container">
    <form #validateForm="ngForm">
        <table class="sl-table-handle">
            <tr class="t-item">
                <td class="label" style="text-align: left;padding:4px 0 4px 20px;width: 100px;">
                    <span>电缆名称：</span>
                </td>
                <td>
                    <input nz-input type="text" [slValidate]="{required:true,label:'电缆名称'}" style="width: 340px;"
                        placeholder="请输入电缆名称" name="cableName" [(ngModel)]="name" />
                </td>
            </tr>
            <tr class="t-item">
                <td class="label" colspan="2" style="text-align: left;padding:4px 0 4px 20px;">
                    <span>海底电缆坐标点</span>
                </td>
            </tr>
        </table>
        <div class="table-container">
            <div class="table-container__title" [style.paddingRight.px]="maxLen?5:0">
                <div class="handle-area">
                    <a href="javascript:void(0)" (click)="add()">
                        <i class="sl-plus-circle-16"></i>
                    </a>
                </div>
                <table>
                    <ng-container [ngTemplateOutlet]="tableCol"></ng-container>
                    <tr class="tr-title">
                        <td class="text-center">序号</td>
                        <td class="text-center required"><span>点</span></td>
                        <td class="text-center required"><span>坐标</span></td>
                        <td class="text-center">操作</td>
                    </tr>
                </table>
            </div>
            <div class="table-container__body" #tableContainer [style.height]="maxLen?'181px':'auto'">
                <div class="table-container__body__inner">
                    <table>
                        <ng-container [ngTemplateOutlet]="tableCol"></ng-container>
                        <tr class="tr-content" *ngFor="let item of list;index as i">
                            <td class="text-center">{{i+1}}</td>
                            <td class="text-center">
                                <input nz-input style="width: 40px;" type="text" name="{{item.uuid}}-markNum"
                                    [(ngModel)]="item.markNum" />
                            </td>
                            <td class=" text-center">
                                <input type="number" name="{{item.uuid}}-latD" [(ngModel)]="item.latD" nz-input
                                    style="width:34px;"
                                    [slValidate]="{required:true,label:'第'+(i+1)+'行纬度（度）',type:'la_degree'}" />
                                <sup>&nbsp;°&nbsp;</sup>
                                <input type="number" name="{{item.uuid}}-latM"
                                    [slValidate]="{required:true,label:'第'+(i+1)+'行纬度（分）',type:'la_minute'}"
                                    [(ngModel)]="item.latM" nz-input style="width:34px;" />
                                <sup>&nbsp;′&nbsp;</sup>
                                <input type="number" name="{{item.uuid}}-latS"
                                    [slValidate]="{required:true,label:'第'+(i+1)+'行纬度（秒）',type:'la_second'}"
                                    [(ngModel)]="item.latS" nz-input style="width:60px;" />
                                <sup>&nbsp;″&nbsp;</sup>
                                <span class="unit" style="margin-left: 4px;margin-right: 6px;">N</span>
                                <input type="number" name="{{item.uuid}}-lngD"
                                    [slValidate]="{required:true,label:'第'+(i+1)+'行经度（度）',type:'lo_degree'}"
                                    [(ngModel)]="item.lngD" nz-input style="width:35px;" />
                                <sup>&nbsp;°&nbsp;</sup>
                                <input type="number" name="{{item.uuid}}-lngM"
                                    [slValidate]="{required:true,label:'第'+(i+1)+'行经度（分）',type:'lo_minute'}"
                                    [(ngModel)]="item.lngM" nz-input style="width:35px;" />
                                <sup>&nbsp;′&nbsp;</sup>
                                <input type="number" name="{{item.uuid}}-lngS"
                                    [slValidate]="{required:true,label:'第'+(i+1)+'行经度（秒）',type:'lo_second'}"
                                    [(ngModel)]="item.lngS" nz-input style="width:60px;" />
                                <sup>&nbsp;″&nbsp;</sup>
                                <span class="unit" style="margin-left: 4px;">E</span>
                            </td>
                            <td class="text-center">
                                <a href="javascript:void(0)" (click)="del(item,i)">
                                    <i class="sl-close-red-16"></i>
                                </a>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </form>
    <div class="handle-area">
        <button sl-button (click)="cancel('ProtectSeaCableHandleComponent')">取消</button>
        <button sl-button slType="primary" (click)="save(validateForm)">保存</button>
    </div>
</div>
<ng-template #tableCol>
    <colgroup>
        <col style="width: 40px;" />
        <col style="width: 50px;" />
        <col style="width: 366px;" />
        <col style="width: 45px;" />
    </colgroup>
</ng-template>