import { proareaType } from './../../windpower-protect.model';
import { Component, Input, OnInit } from '@angular/core';
import { SlModalService } from 'src/app/shared/modules/sl-modal/sl-modal.service';
import { SlValidateService } from 'src/app/shared/modules/sl-validate/sl-validate.service';
import { MapLayerComponentService } from 'src/app/workspace/map/service/map-layer-component.service';
import { TblCrawl } from 'src/app/workspace/workspace-shared/models/tbl_crawl';
import { WindpowerProtectService } from '../../windpower-protect.service';
import { ProtectWindpowerHandleCommon } from '../protect-windpower-handle-common';

@Component({
  selector: 'app-protect-windpower-area-handle',
  templateUrl: './protect-windpower-area-handle.component.html',
  styleUrls: ['../protect-windower-handle-common.less', './protect-windpower-area-handle.component.less']
})
export class ProtectWindpowerAreaHandleComponent extends ProtectWindpowerHandleCommon<TblCrawl> implements OnInit {
  componentName: string = 'ProtectWindpowerAreaHandleComponent'
  minLen: number = 3
  searchLevel: string = '2-1'
  type: proareaType = 'crawl'
  constructor(
    protected slModalService: SlModalService,
    protected slValidateService: SlValidateService,
    protected mapLayerService: MapLayerComponentService,
    protected service: WindpowerProtectService
  ) {
    super(slModalService, slValidateService, mapLayerService, service)
  }

  ngOnInit(): void {
    this.initArr()
  }

}
