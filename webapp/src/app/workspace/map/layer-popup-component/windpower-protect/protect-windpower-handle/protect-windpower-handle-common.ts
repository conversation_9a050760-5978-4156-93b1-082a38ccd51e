import { proareaType } from './../windpower-protect.model';
import { latlngToDMS, DMSToLatlng } from './../../../utils/map-tools';
import { NgForm } from "@angular/forms";
import { DynamicComponentData } from "src/app/shared/models/dynamic-component.model";
import { UUIDModel } from "src/app/workspace/workspace-shared/models/uuid_model";
import { SlValidateService } from 'src/app/shared/modules/sl-validate/sl-validate.service';
import { uuid } from 'src/app/shared/utils';
import { SlModalService } from "./../../../../../shared/modules/sl-modal/sl-modal.service";
import { MapLayerComponentService } from '../../../service/map-layer-component.service';
import { WindpowerProtectService } from '../windpower-protect.service';

export abstract class ProtectWindpowerHandleCommon<T extends UUIDModel> {
    data: DynamicComponentData = {}
    name: string = ''// 电子围栏、海底电缆
    list: Array<T> = [] // 电子围栏、海底电缆
    obj: T = <T>{} // 电子围栏、海底电缆
    minLen: number = 0 // list 最小长度
    cachedBlockId: string = ''
    abstract type: proareaType

    abstract componentName: string
    abstract searchLevel: string

    get maxLen() {
        return this.list.length > 5
    }
    constructor(
        protected slModalService: SlModalService,
        protected slValidateService: SlValidateService,
        protected mapLayerService: MapLayerComponentService,
        protected service: WindpowerProtectService
    ) {
    }
    /**
     * 航标、风机
     */
    initObj() {
        const { params } = this.data
        if (params) {
            this.obj = params.infoData && params.infoData[0] || {}
            const lat = latlngToDMS(this.obj.latitude!, 4)
            const lng = latlngToDMS(this.obj.longitude!, 4)
            this.obj.latD = lat.d
            this.obj.latM = lat.m
            this.obj.latS = lat.s
            this.obj.lngD = lng.d
            this.obj.lngM = lng.m
            this.obj.lngS = lng.s
        }
    }

    /**
     * 电子围栏、海底电缆
     * @returns 
     */
    initArr() {
        const { params } = this.data
        if (params) {
            this.name = params.name
            if (!params.infoData || !params.infoData.length) return
            this.list = <Array<T>>params.infoData.map((ele: T) => {
                ele.uuid = uuid()
                const lat = latlngToDMS(ele.latitude!)
                const lng = latlngToDMS(ele.longitude!)
                ele.latD = lat.d
                ele.latM = lat.m
                ele.latS = lat.s
                ele.lngD = lng.d
                ele.lngM = lng.m
                ele.lngS = lng.s
                return ele
            })
            // 记录当前的blockId
            this.cachedBlockId = this.list[0].blockId!
        }
    }

    trackByItems(index: number, item: T): string {
        return item.uuid!;
    }
    add() {
        this.list.push(<T>{ uuid: uuid() })
    }
    del(item: any, index: number) {
        this.list.splice(index, 1)
    }
    cancel(compoentName: string) {
        this.slModalService.openPromptModal({
            type: 'confirm',
            content: '确认取消吗？',
            okCb: () => {
                this.mapLayerService.removeComponent({
                    destroy: true, name: compoentName
                })
                this.service.removeComponent({ id: '', type: 'edit', componentType: this.type })
            }
        })
    }

    save(validateForm: NgForm) {
        if (this.minLen > 0 && this.list.length < this.minLen) {
            this.slModalService.openPromptModal({
                type: 'error',
                content: '最少长度不少于' + this.minLen
            })
            return
        }
        const isValidate = this.slValidateService.validateFormWithAlert(validateForm)
        if (isValidate) {
            this.slModalService.openPromptModal({
                type: 'confirm',
                content: '确定保存吗？',
                okCb: () => {
                    let list: Array<UUIDModel> = []
                    // 海底电缆 与 电子围栏
                    if (this.searchLevel == '2-1' || this.searchLevel == '3-1') {
                        list = this.list.map((ele, index) => {
                            const item: T = <T>ele
                            item.name = this.name
                            item.serialNumber = index + 1
                            item.blockId = this.cachedBlockId
                            item.latitude = DMSToLatlng(item.latD!, item.latM!, item.latS!, 8)
                            item.longitude = DMSToLatlng(item.lngD!, item.lngM!, item.lngS!, 8)
                            return item
                        })
                    } else { // 风机 与 航标
                        const item: T = <T>this.obj
                        item.latitude = DMSToLatlng(this.obj.latD!, this.obj.latM!, this.obj.latS!, 8)
                        item.longitude = DMSToLatlng(this.obj.lngD!, this.obj.lngM!, this.obj.lngS!, 8)
                        list = [item]
                    }
                    const data = { searchLevel: this.searchLevel, list }
                    this.service.commit(data).then(res => {
                        this.slModalService.openPromptModal({
                            type: 'success',
                            content: '保存成功',
                            okCb: () => {
                                if (this.componentName) {
                                    this.service.removePopup(this.componentName)
                                }
                                this.service.removeComponent({ id: '', type: 'edit', componentType: this.type })
                                this.service.refreshListComponent()
                            }
                        })
                    })
                }
            })
        }
    }
}