<div class="wind-motor-container">
    <form #validateForm="ngForm">
        <table class="sl-table-handle">
            <tr class="t-item">
                <td class="label required" style="width: 90px;">
                    <span>风机名称：</span>
                </td>
                <td>
                    <input nz-input type="text" placeholder="请输入风机名称" name="fanName"
                        [slValidate]="{required:true,label:'风机名称'}" [(ngModel)]="obj.name" />
                </td>
            </tr>
            <tr class="t-item">
                <td class="label required">
                    <span>设计位置：</span>
                </td>
                <td>
                    <input type="number" class="latlng-d" name="latD"
                        [slValidate]="{required:true,label:'纬度（度）',type:'la_degree'}" [(ngModel)]="obj.latD" nz-input />
                    <sup>°</sup>
                    <input type="number" class="latlng-m" name="latM"
                        [slValidate]="{required:true,label:'纬度（分）',type:'la_minute'}" [(ngModel)]="obj.latM" nz-input />
                    <sup>′</sup>
                    <input type="number" class="latlng-s" name="latS"
                        [slValidate]="{required:true,label:'纬度（秒）',type:'la_second'}" [(ngModel)]="obj.latS" nz-input />
                    <sup>″</sup>
                    <span class="unit">N</span>
                    <input type="number" class="latlng-d" name="lngD"
                        [slValidate]="{required:true,label:'经度（度）',type:'lo_degree'}" [(ngModel)]="obj.lngD" nz-input />
                    <sup>°</sup>
                    <input type="number" class="latlng-m" name="lngM"
                        [slValidate]="{required:true,label:'经度（分）',type:'lo_minute'}" [(ngModel)]="obj.lngM" nz-input />
                    <sup>′</sup>
                    <input type="number" class="latlng-s" name="lngS"
                        [slValidate]="{required:true,label:'经度（秒）',type:'lo_second'}" [(ngModel)]="obj.lngS" nz-input />
                    <sup>″</sup>
                    <span class="unit" style="margin-right: 0;">E</span>
                </td>
            </tr>
            <tr class="t-item">
                <td class="label">
                    <span>备注：</span>
                </td>
                <td>
                    <textarea name="remark" nz-input rows="3" placeholder="请输入备注" [(ngModel)]="obj.remark"></textarea>
                </td>
            </tr>
        </table>
    </form>
    <div class="handle-area">
        <button sl-button (click)="cancel('ProtectWindMotorHandleComponent')">取消</button>
        <button sl-button slType="primary" (click)="save(validateForm)">保存</button>
    </div>
</div>