import { proareaType } from './../../windpower-protect.model';
import { Component, OnInit } from '@angular/core';
import { NgForm } from '@angular/forms';
import { SlModalService } from 'src/app/shared/modules/sl-modal/sl-modal.service';
import { SlValidateService } from 'src/app/shared/modules/sl-validate/sl-validate.service';
import { MapLayerComponentService } from 'src/app/workspace/map/service/map-layer-component.service';
import { TblFan } from 'src/app/workspace/workspace-shared/models/tbl_fan';
import { WindpowerProtectService } from '../../windpower-protect.service';
import { ProtectWindpowerHandleCommon } from '../protect-windpower-handle-common';

@Component({
  selector: 'app-protect-wind-motor-handle',
  templateUrl: './protect-wind-motor-handle.component.html',
  styleUrls: ['../protect-windower-handle-common.less', './protect-wind-motor-handle.component.less']
})
export class ProtectWindMotorHandleComponent extends ProtectWindpowerHandleCommon<TblFan> implements OnInit {
  componentName: string = 'ProtectWindMotorHandleComponent';
  searchLevel: string = '5-1'
  type: proareaType = 'fan'
  constructor(
    protected slModalService: SlModalService,
    protected slValidateService: SlValidateService,
    protected mapLayerService: MapLayerComponentService,
    protected service: WindpowerProtectService
  ) {
    super(slModalService, slValidateService, mapLayerService, service)
  }

  ngOnInit(): void {
    this.initObj()
  }
}
