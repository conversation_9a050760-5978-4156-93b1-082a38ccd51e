<div class="windpower-area-container">
    <form #validateForm="ngForm">
        <table class="sl-table-handle">
            <tr class="t-item">
                <td class="label required" style="width: 90px;">
                    <span>航标名称：</span>
                </td>
                <td>
                    <input nz-input type="text" [slValidate]="{required:true,label:'航标名称'}" placeholder="请输入航标名称"
                        name="navName" [(ngModel)]="obj.name" />
                </td>
            </tr>
            <tr class="t-item" *ngIf="isAisMark">
                <td class="label required">
                    <span>英文名称：</span>
                </td>
                <td>
                    <input nz-input type="text" [slValidate]="{required:true,label:'英文名称'}" placeholder="请输入英文名称"
                        name="aisNameEn" [(ngModel)]="obj.aisNameEn" />
                </td>
            </tr>
            <tr class="t-item">
                <td class="label required">
                    <span>航标类别：</span>
                </td>
                <td>
                    <nz-select style="width: 340px;" [nzAllowClear]="true" name="navigationType" nzPlaceHolder="请选择航标类别"
                        [(ngModel)]="obj.navigationType" [slValidate]="{required:true,label:'航标类别'}">
                        <nz-option *ngFor="let item of navigationTypeList" [nzValue]="item.itemCode"
                            [nzLabel]="item.itemName!"></nz-option>
                    </nz-select>
                </td>
            </tr>
            <tr class="t-item">
                <td class="label required">
                    <span>设计位置：</span>
                </td>
                <td>
                    <input type="number" name="latD" nz-input
                        [slValidate]="{required:true,label:'纬度（度）',type:'la_degree'}" [(ngModel)]="obj.latD"
                        class="latlng-d" />
                    <sup>°</sup>
                    <input type="number" name="latM" nz-input
                        [slValidate]="{required:true,label:'纬度（分）',type:'la_minute'}" [(ngModel)]="obj.latM"
                        class="latlng-m" />
                    <sup>′</sup>
                    <input type="number" name="latS" nz-input
                        [slValidate]="{required:true,label:'纬度（秒）',type:'la_second'}" [(ngModel)]="obj.latS"
                        class="latlng-s" />
                    <sup>″</sup>
                    <span class="unit">N</span>
                    <input type="number" name="lngD" nz-input
                        [slValidate]="{required:true,label:'经度（度）',type:'lo_degree'}" [(ngModel)]="obj.lngD"
                        class="latlng-d" />
                    <sup>°</sup>
                    <input type="number" name="lngM" nz-input
                        [slValidate]="{required:true,label:'纬度（分）',type:'lo_minute'}" [(ngModel)]="obj.lngM"
                        class="latlng-m" />
                    <sup>′</sup>
                    <input type="number" name="lngS" nz-input
                        [slValidate]="{required:true,label:'纬度（秒）',type:'lo_second'}" [(ngModel)]="obj.lngS"
                        class="latlng-s" />
                    <sup>″</sup>
                    <span class="unit" style="margin-right: 0;">E</span>
                </td>
            </tr>
            <ng-container *ngIf="isAisMark;else otherMarkBlock">
                <ng-template *ngTemplateOutlet="aisMarkBlock"></ng-template>
            </ng-container>
            <tr class="t-item">
                <td class="label">
                    <span>构造：</span>
                </td>
                <td>
                    <textarea name="lightStructure" nz-input rows="3" placeholder="请输入构造"
                        [(ngModel)]="obj.lightStructure"></textarea>
                </td>
            </tr>
            <tr class="t-item">
                <td class="label">
                    <span>备注：</span>
                </td>
                <td>
                    <textarea name="remark" nz-input rows="3" placeholder="请输入备注" [(ngModel)]="obj.remark"></textarea>
                </td>
            </tr>
        </table>
    </form>
    <div class="handle-area">
        <button sl-button (click)="cancel('ProtectNavMarkHandleComponent')">取消</button>
        <button sl-button slType="primary" (click)="save(validateForm)">保存</button>
    </div>
</div>

<ng-template #aisMarkBlock>
    <tr class="t-item">
        <td class="label">
            <span>MMSI：</span>
        </td>
        <td>
            <input style="width: 340px;" nz-input type="number" placeholder="请输入MMSI" name="mmsi"
                [(ngModel)]="obj.mmsi" />
        </td>
    </tr>
</ng-template>
<ng-template #otherMarkBlock>
    <tr class="t-item">
        <td class="label">
            <span>光色：</span>
        </td>
        <td>
            <nz-select style="width: 340px;" [nzAllowClear]="true" name="colorCode" nzPlaceHolder="请选择光色"
                [(ngModel)]="obj.colorCode">
                <nz-option *ngFor="let item of colorCodeList" [nzValue]="item.itemCode" [nzLabel]="item.itemName!">
                </nz-option>
            </nz-select>
        </td>
    </tr>
    <tr class="t-item">
        <td class="label">
            <span>闪光节奏：</span>
        </td>
        <td>
            <nz-select style="width: 340px;" [nzAllowClear]="true" name="lightRhythmCode" nzPlaceHolder="请选择闪光节奏"
                [(ngModel)]="obj.lightRhythmCode">
                <nz-option *ngFor="let item of lightRhythmCodeList" [nzValue]="item.itemCode"
                    [nzLabel]="item.itemName!">
                </nz-option>
            </nz-select>
        </td>
    </tr>
    <tr class="t-item">
        <td class="label">
            <span>周期：</span>
        </td>
        <td>
            <input style="width: 324px;" nz-input type="number" placeholder="请输入周期" name="lightCycle"
                [(ngModel)]="obj.lightCycle" />
            <span style="margin-left: 4px;" class="unti">s</span>
        </td>
    </tr>

    <tr class="t-item">
        <td class="label">
            <span>灯高：</span>
        </td>
        <td>
            <input style="width: 324px;" nz-input type="number" placeholder="请输入灯高" name="lightHigh"
                [(ngModel)]="obj.lightHigh" />
            <span style="margin-left: 4px;" class="unti">m</span>
        </td>
    </tr>
    <tr class="t-item">
        <td class="label">
            <span>射程：</span>
        </td>
        <td>
            <input style="width: 324px;" nz-input type="text" placeholder="请输入射程" name="lightRange"
                [(ngModel)]="obj.lightRange" />
            <span style="margin-left: 4px;" class="unti">NM</span>
        </td>
    </tr>
</ng-template>