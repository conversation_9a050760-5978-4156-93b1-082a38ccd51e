import { SlModalService } from './../../../../../../shared/modules/sl-modal/sl-modal.service';
import { Component, OnInit } from '@angular/core';
import { ProtectWindpowerHandleCommon } from '../protect-windpower-handle-common';
import { SlValidateService } from 'src/app/shared/modules/sl-validate/sl-validate.service';
import { TblCable } from 'src/app/workspace/workspace-shared/models/tbl_cable';
import { MapLayerComponentService } from 'src/app/workspace/map/service/map-layer-component.service';
import { WindpowerProtectService } from '../../windpower-protect.service';
import { proareaType } from '../../windpower-protect.model';

@Component({
  selector: 'app-protect-sea-cable-handle',
  templateUrl: './protect-sea-cable-handle.component.html',
  styleUrls: ['../protect-windower-handle-common.less', './protect-sea-cable-handle.component.less']
})
export class ProtectSeaCableHandleComponent extends ProtectWindpowerHandleCommon<TblCable> implements OnInit {
  componentName: string = 'ProtectSeaCableHandleComponent'
  minLen: number = 2
  searchLevel: string = '3-1'
  type: proareaType = 'cable'
  constructor(
    protected slModalService: SlModalService,
    protected slValidateService: SlValidateService,
    protected mapLayerService: MapLayerComponentService,
    protected service: WindpowerProtectService
  ) {
    super(slModalService, slValidateService, mapLayerService, service)
  }

  ngOnInit(): void {
    this.initArr()
  }
}
