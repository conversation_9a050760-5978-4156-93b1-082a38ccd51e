import { proareaType } from './../../windpower-protect.model';
import { Component, OnInit } from '@angular/core';
import { NgForm } from '@angular/forms';
import { TblNavigation } from 'src/app/workspace/workspace-shared/models/tbl_navigation';
import { SlModalService } from 'src/app/shared/modules/sl-modal/sl-modal.service';
import { SlValidateService } from 'src/app/shared/modules/sl-validate/sl-validate.service';
import { ProtectWindpowerHandleCommon } from '../protect-windpower-handle-common';
import { DictionaryService } from 'src/app/shared/services/dictionary.service';
import { Dictdetail } from 'src/app/shared/models';
import { MapLayerComponentService } from 'src/app/workspace/map/service/map-layer-component.service';
import { WindpowerProtectService } from '../../windpower-protect.service';

@Component({
  selector: 'app-protect-nav-mark-handle',
  templateUrl: './protect-nav-mark-handle.component.html',
  styleUrls: ['../protect-windower-handle-common.less', './protect-nav-mark-handle.component.less']
})
export class ProtectNavMarkHandleComponent extends ProtectWindpowerHandleCommon<TblNavigation> implements OnInit {
  componentName: string = 'ProtectNavMarkHandleComponent';
  colorCodeList: Array<Dictdetail> = []
  lightRhythmCodeList: Array<Dictdetail> = []
  navigationTypeList: Array<Dictdetail> = []
  searchLevel: string = '4-1'
  type: proareaType = 'navigation'
  constructor(
    protected slModalService: SlModalService,
    protected slValidateService: SlValidateService,
    private dictService: DictionaryService,
    protected mapLayerService: MapLayerComponentService,
    protected service: WindpowerProtectService
  ) {
    super(slModalService, slValidateService, mapLayerService, service)
  }

  get isAisMark() {
    return this.obj?.navigationType == '0105' || this.obj?.navigationType == '0104'
  }

  ngOnInit(): void {
    this.initObj()
    this.dictService.getDicList(['PL_AIDSTYPE', 'PL_LIGHTCOLOR', 'PL_RECCODE']).then(([list1, list2, list3]) => {
      this.navigationTypeList = <Array<Dictdetail>>list1
      this.colorCodeList = <Array<Dictdetail>>list2
      this.lightRhythmCodeList = <Array<Dictdetail>>list3
    })
  }


}
