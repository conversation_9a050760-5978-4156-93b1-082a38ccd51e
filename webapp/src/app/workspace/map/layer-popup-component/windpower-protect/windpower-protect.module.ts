import { NzEmptyModule } from 'ng-zorro-antd/empty';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { FormsModule } from '@angular/forms';
import { SlButtonModule } from './../../../../shared/modules/sl-button/sl-button.module';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ProtectListComponent } from './protect-list/protect-list.component';
import { ProtectInfoComponent } from './protect-info/protect-info.component';
import { ProtectHandleComponent } from './protect-handle/protect-handle.component';
import {
  ProtectSeaCableInfoComponent,
  ProtectWindMotorInfoComponent,
  ProtectNavMarkInfoComponent,
  ProtectWindpowerAreaInfoComponent
} from './protect-windpower-info';
import {
  ProtectSeaCableHandleComponent,
  ProtectWindMotorHandleComponent,
  ProtectNavMarkHandleComponent,
  ProtectWindpowerAreaHandleComponent
} from './protect-windpower-handle';
import {
  NavMarkTableComponent,
  SeaMarkTableComponent,
  WindpowerAreaTableComponent,
  WindMotorTableComponent
} from './protect-handle';
import { SharedModule } from 'src/app/shared/shared.module';
import { ProtectListItemComponent } from './protect-list/protect-list-item/protect-list-item.component';
import { WindpowerProtectLayerComponent } from './windpower-protect-layer/windpower-protect-layer.component';
import { ProtectShipAlarmComponent } from './protect-ship-alarm/protect-ship-alarm.component';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzUploadModule } from 'ng-zorro-antd/upload';
import { ProtectItemUnitDirective } from './protect-item-unit.directive';
import { NavMarkBroadcastComponent } from './protect-windpower-info/nav-mark-broadcast/nav-mark-broadcast.component';

// 单个详情组件
const SINGLE_INFO = [
  ProtectSeaCableInfoComponent,
  ProtectWindMotorInfoComponent,
  ProtectNavMarkInfoComponent,
  ProtectWindpowerAreaInfoComponent
]

// 单个新增/编辑组件
const SINGLE_HANDLE = [
  ProtectSeaCableHandleComponent,
  ProtectWindMotorHandleComponent,
  ProtectNavMarkHandleComponent,
  ProtectWindpowerAreaHandleComponent
]

// 保护区编辑
const PROTECT_HANDLE = [
  ProtectHandleComponent,
  NavMarkTableComponent,
  SeaMarkTableComponent,
  WindpowerAreaTableComponent,
  WindMotorTableComponent
]
@NgModule({
  declarations: [
    ProtectListComponent,
    ProtectInfoComponent,
    ...PROTECT_HANDLE,
    ...SINGLE_INFO,
    ...SINGLE_HANDLE,
    ProtectListItemComponent,
    WindpowerProtectLayerComponent,
    ProtectShipAlarmComponent,
    ProtectItemUnitDirective,
    NavMarkBroadcastComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    NzSelectModule,
    NzInputModule,
    SlButtonModule,
    SharedModule,
    NzSpinModule,
    NzEmptyModule,
    NzModalModule,
    NzUploadModule
  ]
})
export class WindpowerProtectModule { }