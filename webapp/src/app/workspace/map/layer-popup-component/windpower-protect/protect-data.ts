import { NavMarkTableComponent } from "./protect-handle/nav-mark-table/nav-mark-table.component";
import { SeaMarkTableComponent } from "./protect-handle/sea-mark-table/sea-mark-table.component";
import { WindMotorTableComponent } from "./protect-handle/wind-motor-table/wind-motor-table.component";
import { WindpowerAreaTableComponent } from "./protect-handle/windpower-area-table/windpower-area-table.component";
import { NavMarkProtect, ProtectHandleTabItem, ProtectInfoTabItem, ProtectTableItem } from "./windpower-protect.model";


const crawlTableItemList: Array<ProtectTableItem> = [
    { title: '区域名称', property: 'name', width: 300 },
    { title: '点号', property: 'markNum', width: 200 },
    { title: '坐标', property: 'latlngStr', width: 690 }
]

const cableTableItemList: Array<ProtectTableItem> = [
    { title: '电缆名称', property: 'name', width: 300 },
    { title: '点号', property: 'markNum', width: 200 },
    { title: '坐标', property: 'latlngStr', width: 690 }
]

const navigationTableItemList: Array<ProtectTableItem> = [
    { title: '航标名称', property: 'name', width: 120 },
    { title: '英文名称', property: 'aisNameEn', width: 120 },
    { title: '航标类别', property: 'navigationName', width: 95 },
    { title: '设计位置', property: 'latlngStr', width: 220 },
    { title: 'MMSI', property: 'mmsi', width: 90 },
    { title: '光色', property: 'colorName', width: 60 },
    { title: '闪光节奏', property: 'lightRhythmName', width: 95 },
    { title: '周期（s）', property: 'lightCycle', width: 70 },
    { title: '灯高（m）', property: 'lightHigh', width: 80 },
    { title: '射程（NM）', property: 'lightRange', width: 90 },
    { title: '构造', property: 'lightStructure', width: 140 },
    { title: '备注', property: 'remark', width: 110 },
]

const fanTableItemList: Array<ProtectTableItem> = [
    { title: '风机名称', property: 'name', width: 300 },
    { title: '设计位置', property: 'latlngStr', width: 500 },
    { title: '备注', property: 'remark', width: 450 }
]

// 详细 tabset 配置
export const INFO_TAB_LIST: Array<ProtectInfoTabItem> = [
    { listPro: 'crawlList', title: '电子围栏', listTableItemList: crawlTableItemList },
    { listPro: 'cableList', title: '海底电缆', listTableItemList: cableTableItemList },
    { listPro: 'navigationList', title: '航标', listTableItemList: navigationTableItemList },
    { listPro: 'fanList', title: '风机', listTableItemList: fanTableItemList },
]

// 新增编辑 tabset 配置
export const HANDLE_TAB_LIST: Array<ProtectHandleTabItem> = [
    { listPro: 'crawlList', type: 'crawl', title: '电子围栏', icon: 'sl-circle-number1', minListLen: 3, dynamic: { name: 'WindpowerAreaTableComponent', component: WindpowerAreaTableComponent } },
    { listPro: 'cableList', type: 'cable', title: '海底电缆', icon: 'sl-circle-number2', minListLen: 2, dynamic: { name: 'SeaMarkTableComponent', component: SeaMarkTableComponent } },
    { listPro: 'navigationList', type: 'navigation', title: '航标', icon: 'sl-circle-number3', dynamic: { name: 'NavMarkTableComponent', component: NavMarkTableComponent } },
    { listPro: 'fanList', type: 'fan', title: '风机', icon: 'sl-circle-number4', dynamic: { name: 'WindMotorTableComponent', component: WindMotorTableComponent } },
]


// 航标种类
export const NAV_MARK_LIST: Array<{ type: string, name: string, list: Array<NavMarkProtect>, icon: string }> = [
    { type: '0101', name: '灯桩', list: [], icon: 'light-beacon3' },
    { type: '0102', name: '灯浮标', list: [], icon: 'light-buoy' },
    { type: '0103', name: '登录牌标志', list: [], icon: 'landing-card' },
    { type: '0104', name: '实体AIS航标', list: [], icon: 'mark' },
    { type: '0105', name: '虚拟AIS航标', list: [], icon: 'virtual-mark' },
]