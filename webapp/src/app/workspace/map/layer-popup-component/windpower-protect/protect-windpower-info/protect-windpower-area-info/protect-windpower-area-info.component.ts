import { Component, Input, OnInit } from '@angular/core';
import { DynamicComponentData } from 'src/app/shared/models/dynamic-component.model';
import { transformPosUnit } from 'src/app/workspace/map/utils';
import { TblCrawl } from 'src/app/workspace/workspace-shared/models/tbl_crawl';

@Component({
  selector: 'app-protect-windpower-area-info',
  templateUrl: './protect-windpower-area-info.component.html',
  styleUrls: ['./protect-windpower-area-info.component.less']
})
export class ProtectWindpowerAreaInfoComponent implements OnInit {
  @Input() data: DynamicComponentData = {}
  list: Array<TblCrawl> = []
  name: string = ''
  get maxLen() {
    return this.list.length > 5
  }
  constructor() { }

  ngOnInit(): void {
    const { params } = this.data
    if (params) {
      this.name = params.name
      if (!params.infoData || !params.infoData.length) return
      this.list = <Array<TblCrawl>>params.infoData.map((ele: TblCrawl) => {
        ele.latlngStr = transformPosUnit(ele.latitude!, ele.longitude!, 4).latlngStr
        return ele
      })
    }
  }

}
