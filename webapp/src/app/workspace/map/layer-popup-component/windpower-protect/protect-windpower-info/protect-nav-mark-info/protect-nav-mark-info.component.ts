import { Component, Input, OnInit } from '@angular/core';
import { DynamicComponentData } from 'src/app/shared/models/dynamic-component.model';
import { MapLayerComponentService } from 'src/app/workspace/map/service/map-layer-component.service';
import { transformPosUnit } from 'src/app/workspace/map/utils';
import { TblNavigation, TblNavigationStatus } from 'src/app/workspace/workspace-shared/models/tbl_navigation';
@Component({
  selector: 'app-protect-nav-mark-info',
  templateUrl: './protect-nav-mark-info.component.html',
  styleUrls: ['./protect-nav-mark-info.component.less']
})
export class ProtectNavMarkInfoComponent implements OnInit {
  @Input() data: DynamicComponentData = {}
  nav: TblNavigation = {}
  navStatus: TblNavigationStatus = {}
  constructor(
    private mapLayService: MapLayerComponentService
  ) { }
  get isAisMark() {
    return this.nav?.navigationType == '0105' || this.nav?.navigationType == '0104'
  }
  ngOnInit(): void {
    const { params } = this.data
    if (params) {
      this.nav = params.infoData && params.infoData[0] || {}
      this.navStatus = this.nav.statuslist || {}
      this.nav.latlngStr = transformPosUnit(this.nav.latitude!, this.nav.longitude!, 4).latlngStr
    }
  }
  // 播发记录
  getBroadcastList() {
    const mmsi = this.nav.mmsi
    this.mapLayService.addComponent({
      title: '播发记录',
      name: 'NavMarkBroadcastComponent',
      titleType: 'default',
      type: 'popup',
      data: {
        params: { mmsi },
        position: {
          left: 360,
          top: 10
        }
      }
    })
  }
}
