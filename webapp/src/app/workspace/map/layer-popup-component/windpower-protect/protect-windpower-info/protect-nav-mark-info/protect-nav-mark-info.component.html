<div class="protect-windpower-info">
    <ng-container *ngIf="isAisMark;else otherMarkBlock">
        <ng-template *ngTemplateOutlet="aisMarkBlock"></ng-template>
    </ng-container>
</div>

<ng-template #aisMarkBlock>
    <table class="ais-mark-table">
        <tr>
            <td>航标名称：</td>
            <td>{{nav.name}}</td>
        </tr>
        <tr>
            <td>英文名称：</td>
            <td>{{nav.aisNameEn}}</td>
        </tr>
        <tr>
            <td>航标类别：</td>
            <td>{{nav.navigationName}}</td>
        </tr>
        <tr>
            <td>设计位置：</td>
            <td>{{nav.latlngStr}}</td>
        </tr>
        <tr>
            <td>MMSI：</td>
            <td>{{nav.mmsi}}</td>
        </tr>
        <tr>
            <td>构造：</td>
            <td>{{nav.lightStructure}}</td>
        </tr>
        <tr style="padding-bottom: 4px;">
            <td>备注：</td>
            <td>{{nav.remark}}</td>
        </tr>
    </table>
    <div style="border-top: 1px solid #ECEEF4; display: flex;align-items: center;justify-content: center;    padding-top: 15px;
    margin-left: -18px;
    margin-right: -12px;
    margin-top: 15px;
    height: 70px;">
        <button sl-button slType="primary" (click)="getBroadcastList()">播发记录</button>
    </div>
</ng-template>

<ng-template #otherMarkBlock>
    <table class="other-mark-table">
        <tr>
            <td>航标名称：</td>
            <td style="width:253px;word-break: keep-all;">{{nav.name}}</td>
            <td>航标类别：</td>
            <td style="width: 84px;">{{nav.navigationName}}</td>
        </tr>
        <tr>
            <td>设计位置：</td>
            <td>{{nav.latlngStr}}</td>
            <td>设计灯高：</td>
            <td>{{nav.lightHigh?nav.lightHigh+'m':''}}</td>
        </tr>
        <tr>
            <td>设计灯质：</td>
            <td>{{nav.colorName}}{{nav.lightRhythmName}}</td>
            <td>设计射程：</td>
            <td>{{nav.lightRange?nav.lightRange+'NM':''}}</td>
        </tr>
        <tr>
            <td>构&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;造：</td>
            <td colspan="3">{{nav.lightStructure}}</td>
        </tr>
        <tr style="padding-bottom: 4px;">
            <td>备&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;注：</td>
            <td colspan="3">{{nav.remark}}</td>
        </tr>
        <!-- <tr>
            <td colspan="6">
                <div style="border-bottom: 1px dashed #C6D0E4;padding: 0;"></div>
            </td>
        </tr>
        <tr style="padding-top: 4px;">
            <td style="color: #2D74EF;">采集时间：</td>
            <td style="color: #2D74EF;" colspan="5">
                {{navStatus.collectionTime}}
            </td>
        </tr>
        <tr>
            <td>工作电压：</td>
            <td>{{navStatus.lightWorkVoltage?navStatus.lightWorkVoltage+'V':''}}</td>
            <td>工作电流：</td>
            <td>{{navStatus.lightWorkCurrent?navStatus.lightWorkCurrent+'A':''}}</td>
            <td>控制模式：</td>
            <td>{{navStatus.workModelName}}</td>
        </tr>
        <tr>
            <td>当前位置：</td>
            <td colspan="3">{{navStatus.coordinates}}</td>
            <td>位移信息：</td>
            <td>{{navStatus.shiftDirection?'距离：'+navStatus.shiftDirection+'m':''}}&nbsp;&nbsp;{{navStatus.shiftDirection?'方向：'+navStatus.shiftDirection+'°':''}}
            </td>
        </tr>
        <tr>
            <td>环境温度：</td>
            <td>{{navStatus.lightTemperature?navStatus.lightTemperature+'℃':''}}</td>
            <td>灯器湿度：</td>
            <td>{{navStatus.lightHumidity?navStatus.lightHumidity+'%':''}}</td>
            <td>环境亮度：</td>
            <td>{{navStatus.daylightValue}}</td>
        </tr>
        <tr>
            <td>电池电压：</td>
            <td>
                {{navStatus.batteryVoltage?navStatus.batteryVoltage+'V':''}}
            </td>
            <td>充电电流：</td>
            <td colspan="3">
                {{navStatus.chargingCurrent?navStatus.chargingCurrent+'A':''}}
            </td>
        </tr> -->
    </table>
</ng-template>