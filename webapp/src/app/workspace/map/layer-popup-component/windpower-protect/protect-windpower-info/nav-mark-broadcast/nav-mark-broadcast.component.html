<div class="broadcast-container">
    <nz-spin nzTip="数据加载中,请稍后..." [nzSpinning]="ifSpinning">
    </nz-spin>
    <table>
        <tr class="head">
            <td class="serial">序号</td>
            <!-- <td style="width: 360px;">航标名称</td> -->
            <td style="width: 360px;">英文名称</td>
            <td style="width: 332px;">MMSI</td>
            <td style="width: 332px;">播发时间</td>
        </tr>
        <tr class="content" *ngFor="let item of list;index as i">
            <td class="serial">{{i+1}}</td>
            <!-- <td>{{item.aisName}}</td> -->
            <td>{{item.aidsNameEn}}</td>
            <td>{{item.mmsi}}</td>
            <td>{{item.sendDate}}</td>
        </tr>
        <tr *ngIf="!list||!list.length" class="content">
            <td colspan="5">
                <div class="empty-container">
                    <nz-empty></nz-empty>
                </div>
            </td>
        </tr>
    </table>
    <sl-pagination [page]="page" [pageSizeOptions]="[10,15]" (pageChange)="pageChange($event)"></sl-pagination>
</div>