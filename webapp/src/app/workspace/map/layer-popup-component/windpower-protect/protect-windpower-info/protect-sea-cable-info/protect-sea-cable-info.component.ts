import { Component, Input, OnInit } from '@angular/core';
import { DynamicComponentData } from 'src/app/shared/models/dynamic-component.model';
import { transformPosUnit } from 'src/app/workspace/map/utils';
import { TblCable } from 'src/app/workspace/workspace-shared/models/tbl_cable';

@Component({
  selector: 'app-protect-sea-cable-info',
  templateUrl: './protect-sea-cable-info.component.html',
  styleUrls: ['./protect-sea-cable-info.component.less']
})
export class ProtectSeaCableInfoComponent implements OnInit {
  @Input() data: DynamicComponentData = {}
  list: Array<TblCable> = []
  name: string = ''
  get maxLen() {
    return this.list.length > 5
  }
  constructor() { }
  ngOnInit(): void {
    const { params } = this.data
    if (params) {
      this.name = params.name
      if (!params.infoData || !params.infoData.length) return
      this.list = <Array<TblCable>>params.infoData.map((ele: TblCable) => {
        ele.latlngStr = transformPosUnit(ele.latitude!, ele.longitude!, 4).latlngStr
        return ele
      })
    }
  }

}
