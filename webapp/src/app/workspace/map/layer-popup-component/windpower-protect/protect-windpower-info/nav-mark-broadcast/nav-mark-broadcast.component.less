.broadcast-container {
  padding: 28px 24px;
  width: 1140px;
  table {
    width: 100%;
    table-layout: fixed;
    margin-bottom: 40px;
    tr {
      td {
        height: 36px;
        text-align: center;
        border-left: 1px solid rgba(215, 219, 232, 1);
        border-top: 1px solid rgba(215, 219, 232, 1);
        &:last-child {
          border-right: 1px solid rgba(215, 219, 232, 1);
        }
        &.serial {
          width: 64px;
        }
      }
      &:last-child {
        border-bottom: 1px solid rgba(215, 219, 232, 1);
      }
      &.head {
        td {
          background: #eef4fe;
        }
      }
      &.content {
        td {
          .empty-container {
            height: 180px;
            display: flex;
            width: 100%;
            justify-content: center;
            align-items: center;
          }
        }
      }
    }
  }
}
