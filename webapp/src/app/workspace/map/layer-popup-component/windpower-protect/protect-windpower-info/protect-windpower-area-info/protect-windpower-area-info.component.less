@import "/src/styles/mixins";
.protect-info-container {
  width: 394px;
  background: transparent;
  padding: 10px 16px 15px 16px;
  table.info {
    width: 100%;
    table-layout: fixed;
    tr {
      td {
        vertical-align: top;
        word-break: break-all;
        padding: 2px 0;
        &.label {
          width: 70px;
          color: #666;
        }
      }
    }
  }
  .table-container {
    .coordinate-container-outer {
      overflow: hidden;
      .scrollbars(5px, #C8C9CC, rgba(0, 0, 0, 0));
      .coordinate-container-inner {
        overflow-y: auto;
        height: 100%;
      }
    }
    table {
      tr {
        border-top: 1px solid rgba(215, 219, 232, 1);
        &.title td {
          height: 30px;
          background: #e7f1ff;
        }
        &.content:last-child {
          border-bottom: 1px solid rgba(215, 219, 232, 1);
        }
        &.content td {
          height: 36px;
        }
        td {
          text-align: center;
          border-left: 1px solid rgba(215, 219, 232, 1);
          &:last-child {
            border-right: 1px solid rgba(215, 219, 232, 1);
          }
        }
      }
    }
  }
}
