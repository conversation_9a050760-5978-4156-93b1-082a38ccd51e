import { Component, Input, OnInit } from '@angular/core';
import { DynamicComponentData } from 'src/app/shared/models/dynamic-component.model';
import { transformPosUnit } from 'src/app/workspace/map/utils';
import { TblFan } from 'src/app/workspace/workspace-shared/models/tbl_fan';

@Component({
  selector: 'app-protect-wind-motor-info',
  templateUrl: './protect-wind-motor-info.component.html',
  styleUrls: ['./protect-wind-motor-info.component.less']
})
export class ProtectWindMotorInfoComponent implements OnInit {
  @Input() data: DynamicComponentData = {}
  fan: TblFan = {}
  constructor() { }

  ngOnInit(): void {
    const { params } = this.data
    if (params) {
      this.fan = params.infoData && params.infoData[0] || {}
      this.fan.latlngStr = transformPosUnit(this.fan.latitude!, this.fan.longitude!, 4).latlngStr
    }
  }

}
