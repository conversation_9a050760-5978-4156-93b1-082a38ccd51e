import { WindpowerProtectService } from './../../windpower-protect.service';
import { BaseSearch, DynamicComponentData, Page } from 'src/app/shared/models';
import { Component, Input, OnInit } from '@angular/core';
import { AisMarkBroadcast, AisMarkBroadcastSearch } from '../../windpower-protect.model';

@Component({
  selector: 'app-nav-mark-broadcast',
  templateUrl: './nav-mark-broadcast.component.html',
  styleUrls: ['./nav-mark-broadcast.component.less']
})
export class NavMarkBroadcastComponent implements OnInit {
  @Input() data: DynamicComponentData = new DynamicComponentData()
  // 播发记录
  list: AisMarkBroadcast[] = []
  page: Page<AisMarkBroadcast> = new Page()
  search: AisMarkBroadcastSearch = new AisMarkBroadcastSearch()
  ifSpinning: boolean = false
  constructor(private service: WindpowerProtectService) { }

  ngOnInit(): void {
    const { params } = this.data
    if (params && params.mmsi) {
      this.search.mmsi = params.mmsi
      this.search.stationId = params.id
      this.getList()
    }
  }

  getList() {
    this.ifSpinning = true
    this.service.getBraodcastList(this.search).then(res => {
      this.ifSpinning = false
      this.page = res
      this.list = res.result || []
    }).catch(res => {
      this.ifSpinning = false
    })
  }

  pageChange($event: { currentPage: number; pageRecord: number }) {
    this.search.currentPage = $event.currentPage;
    this.search.pageRecord = $event.pageRecord;
    this.getList();
  }
}
