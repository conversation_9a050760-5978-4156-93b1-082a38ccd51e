<div class="protect-info-container">
    <table class="info">
        <tr>
            <td class="label">电缆名称：</td>
            <td class="content">{{name}}</td>
        </tr>
        <tr>
            <td colspan="2" class="label">
                海底电缆坐标点
            </td>
        </tr>
    </table>
    <div class="table-container" [style.paddingRight.px]="maxLen?5:0">
        <div class="table-container__title" [style.paddingRight.px]="maxLen?5:0">
            <table>
                <ng-container [ngTemplateOutlet]="tableCol"></ng-container>
                <tr class="title">
                    <td class="serial">序号</td>
                    <td>点</td>
                    <td>坐标</td>
                </tr>
            </table>
        </div>
        <div class="coordinate-container-outer" [style.height]="maxLen?'181px':'auto'">
            <div class="coordinate-container-inner">
                <table>
                    <ng-container [ngTemplateOutlet]="tableCol"></ng-container>
                    <tr class="content" *ngFor="let item of list;index as i">
                        <td class="serial">{{i+1}}</td>
                        <td>{{item.markNum}}</td>
                        <td>{{item.latlngStr}}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<ng-template #tableCol>
    <colgroup>
        <col style="width: 48px;" />
        <col style="width: 50px;" />
        <col style="width: 258px;" />
    </colgroup>
</ng-template>