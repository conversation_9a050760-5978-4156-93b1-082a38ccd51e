@import "/src/styles/mixins";
.protect-info-container {
  width: 1550px;
  background: transparent;
  padding: 16px 0 30px 0;
  table.info-table {
    tr {
      td {
        position: relative;
        &.label {
          width: 130px;
          text-align: right;
        }
      }
    }
  }
  .tabset-container {
    padding: 0 12px;
    margin-top: 10px;
    ul {
      width: 100%;
      position: relative;
      height: 60px;
      display: flex;
      align-items: center;
      li {
        display: flex;
        height: 40px;
        position: absolute;
        align-items: center;
        justify-content: center;
        background-repeat: no-repeat;
        // background-position-x: -7px;
        a {
          font-size: 16px;
          display: flex;
          width: 100%;
          align-items: center;
          justify-content: center;
          span {
            margin-left: 6px;
          }
        }
        &:nth-child(1) {
          width: 356px;
          left: 0;
          background-image: url("/assets/img/map/protect/1.png");
          &.active {
            background-image: url("/assets/img/map/protect/2.png");
          }
        }
        &:nth-child(2),
        &:nth-child(3) {
          width: 394px;
          background-image: url("/assets/img/map/protect/3.png");
          &.active {
            background-image: url("/assets/img/map/protect/4.png");
          }
        }
        &:nth-child(2) {
          left: calc(~"356px - 12px * 1");
        }
        &:nth-child(3) {
          left: calc(~"356px + 394px - 12px * 2");
        }
        &:nth-child(4) {
          width: 370px;
          background-image: url("/assets/img/map/protect/5.png");
          left: calc(~"356px + 394px * 2 - 12px * 3");
          &.active {
            background-image: url("/assets/img/map/protect/6.png");
          }
        }
        &.active > a {
          color: #fff;
        }
      }
    }
    .title {
      display: flex;
      justify-content: space-between;
      height: 48px;
      align-items: center;
      padding: 0 4px 0 14px;
      position: relative;
    }
  }
  .list-table-container {
    padding: 0 16px;
    .coordinate-container-outer {
      overflow: hidden;
      .scrollbars(5px, #C8C9CC, rgba(0, 0, 0, 0));
      .coordinate-container-inner {
        overflow-y: auto;
        height: 100%;
      }
    }
    table {
      tr {
        border-top: 1px solid rgba(215, 219, 232, 1);
        &:last-child {
          border-bottom: 1px solid rgba(215, 219, 232, 1);
        }
        &.title > td {
          background: #eef4fe;
        }
        &.content.first {
          border-top: none;
        }
        td {
          text-align: center;
          height: 40px;
          padding: 0 6px;
          border-left: 1px solid rgba(215, 219, 232, 1);
          &:last-child {
            border-right: 1px solid rgba(215, 219, 232, 1);
          }
        }
      }
    }
  }
}
