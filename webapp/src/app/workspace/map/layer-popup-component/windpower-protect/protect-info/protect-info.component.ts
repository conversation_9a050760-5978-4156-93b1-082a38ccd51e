import { Component, Input, OnInit } from '@angular/core';
import * as _ from 'lodash';
import { DynamicComponentData } from 'src/app/shared/models/dynamic-component.model';
import { TblProarea } from 'src/app/workspace/workspace-shared/models/tbl_proarea';
import { UUIDModel } from 'src/app/workspace/workspace-shared/models/uuid_model';
import { transformPosUnit } from '../../../utils';
import { INFO_TAB_LIST } from '../protect-data';
import { ProtectInfoTabItem } from '../windpower-protect.model';

@Component({
  selector: 'app-protect-info',
  templateUrl: './protect-info.component.html',
  styleUrls: ['./protect-info.component.less']
})
export class ProtectInfoComponent implements OnInit {
  @Input() data: DynamicComponentData = {}

  // 保护区
  proarea: TblProarea = new TblProarea()
  currentIndex: number = 0
  tabList: Array<ProtectInfoTabItem> = []

  get list(): Array<UUIDModel> {
    return this.tabList[this.currentIndex].list || []
  }

  get maxLen() {
    return this.list.length > 5
  }
  get tabItemList() {
    return this.tabList[this.currentIndex].listTableItemList
  }

  constructor() {
    this.tabList = _.cloneDeep(INFO_TAB_LIST)
  }

  ngOnInit(): void {
    const { params } = this.data
    if (params) {
      const { tabIndex, infoData } = params
      this.currentIndex = tabIndex
      this.proarea = infoData
      this.tabList.forEach(ele => {
        ele.list = this.proarea[ele.listPro]
        if (ele.list && ele.list.length) {
          ele.list.forEach(ele => {
            ele.latlngStr = transformPosUnit(ele.latitude!, ele.longitude!, 4).latlngStr
          })
        }
      })
    }
  }
  /**
       * trackBy
       * @param index 
       * @param item 
       * @returns 
       */
  trackByItems(index: number, item: any): string {
    return item.id;
  }
}
