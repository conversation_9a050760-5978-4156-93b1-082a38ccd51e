<div class="protect-info-container">
    <!--公用表单 start-->
    <form #validateForm="ngForm">
        <table class="info-table">
            <tr>
                <td class="label">
                    <span>保护区名称：</span>
                </td>
                <td class="control">
                    {{proarea.areaName}}
                </td>
                <td class="label">
                    <span>海底电缆保护范围：</span>
                </td>
                <td class="control">
                    {{proarea.cableDistance}}
                    <span class="unit">米</span>
                </td>
                <td class="label">
                    <span>风机保护范围：</span>
                </td>
                <td class="control">
                    {{proarea.fanDistance}}
                    <span class="unit">米</span>
                </td>
            </tr>
        </table>
    </form>
    <!--公用表单 end-->
    <!--tabset start-->
    <div class="tabset-container">
        <ul>
            <li *ngFor="let item of tabList;index as i;" [class.active]="currentIndex==i">
                <a href="javascript:void(0)" (click)="currentIndex=i">
                    <span>{{item.title}}</span>
                </a>
            </li>
        </ul>
        <div class="title">
            <span>{{tabList[currentIndex].title}}</span>
        </div>
    </div>
    <!--tabset end-->
    <!--数据集主体table start-->
    <div class="list-table-container">
        <div class="table-container" [style.paddingRight.px]="maxLen?5:0">
            <div class="table-container__title" [style.paddingRight.px]="maxLen?5:0">
                <table>
                    <ng-container [ngTemplateOutlet]="tableCol"></ng-container>
                    <tr class="title">
                        <td class="serial">序号</td>
                        <td *ngFor="let item of tabItemList">
                            {{item.title}}
                        </td>
                    </tr>
                </table>
            </div>
            <form #validateForm="ngForm">
                <div class="coordinate-container-outer" [style.height]="maxLen?'319px':'auto'">
                    <div class="coordinate-container-inner">
                        <table style="table-layout: fixed;">
                            <ng-container [ngTemplateOutlet]="tableCol"></ng-container>
                            <tr class="content" *ngFor="let item of list;index as i;trackBy:trackByItems"
                                [class.first]="i==0">
                                <td class="serial">{{i+1}}</td>
                                <ng-container *ngFor="let td of tabItemList">
                                    <td>{{item[td.property]}}</td>
                                </ng-container>
                            </tr>
                        </table>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!--数据集主体table end-->
</div>

<ng-template #tableCol>
    <colgroup>
        <col style="width:55px;" />
        <col [style.width.px]="item.width" *ngFor="let item of tabItemList" />
    </colgroup>
</ng-template>