import { NgForm } from "@angular/forms"
import { BaseSearch, DynamicComponent } from "src/app/shared/models"
import { TblFan } from "src/app/workspace/workspace-shared/models/tbl_fan"
import { TblNavigation } from "src/app/workspace/workspace-shared/models/tbl_navigation"
import { UUIDModel } from "src/app/workspace/workspace-shared/models/uuid_model"

/**
 * 保护区编辑、详细操作 状态
 */
export interface ProtectHanldeStatus {
    id: string // 当前操作的数据id
    type: 'edit' | 'info' // 编辑 、 详情
    componentType: proareaType // 组件类型
    selectedIconLatlng?: L.LatLng // 选中图标坐标点 // 风机和航标才有
}

export interface ProtectHandleStatusNotify {
    [key: string]: any
}

export interface ProtectTableItem {
    title: string
    property: string
    width: number
}

export interface ProtectBaseTabItem {
    // blockIdPro: string,
    listPro: string
    title: string
    list?: Array<UUIDModel>
}
export interface ProtectInfoTabItem extends ProtectBaseTabItem {
    listTableItemList: Array<ProtectTableItem>
}

export interface ProtectHandleTabItem extends ProtectBaseTabItem {
    dynamic: DynamicComponent
    icon: string
    form?: NgForm,
    minListLen?: number // list 数组最小长度
    type?: proareaType
}
export type proareaType = 'crawl' | 'cable' | 'navigation' | 'fan' | 'proarea'

// tool tip 位置
export type toolTipPosition = 'topright' | 'topleft' | 'bottomright' | 'bottomleft'
export interface ProtectTreeNode {
    areaId: string
    label: string
    level: string,
    searchLevel: string,
    index: number
    type: proareaType
    blockId?: string
    leaf?: boolean
    expanded?: boolean
    children?: Array<ProtectTreeNode>
    list?: Array<any>
    icon?: string
    id?: string
    latlng?: L.LatLng
    infoSelected?: boolean
    editSelected?: boolean
    center?: L.LatLng,
    mmsi?: string
}

/**
 * 坐标点 ：例如航标、风机
 */
export class MarkerPoint {
    id: string;
    latlng: L.LatLng;
    imgUrl: string;
    imgSize: [number, number];
    canvasImg?: HTMLImageElement;
    toolTipText?: string;
    toolTipPos?: toolTipPosition;
    nameText?: string // 图标名称（底部显示）
    constructor(id: string, latlng: L.LatLng, imgUrl: string, imgSize: [number, number]) {
        this.id = id
        this.latlng = latlng
        this.imgUrl = imgUrl
        this.imgSize = imgSize
    }
}

/**
 * 风机保护区
 */
export class FanProtect extends MarkerPoint {
    distanceRadius: number
    name?: string
    type: string
    infoData?: TblFan
    constructor(id: string, latlng: L.LatLng, imgUrl: string, imgSize: [number, number], distance: number) {
        super(id, latlng, imgUrl, imgSize)
        this.distanceRadius = distance
        this.type = 'fan'
    }
}

/**
 * 航标点
 */
export class NavMarkProtect extends MarkerPoint {
    name: string
    navigationType: string
    blockId: string
    type: string // 类型
    infoData?: TblNavigation // 记录原始数据，弹窗（info popup）时使用
    constructor(id: string, latlng: L.LatLng, imgUrl: string, imgSize: [number, number], name: string, navigationType: string, blockId: string) {
        super(id, latlng, imgUrl, imgSize)
        this.name = name
        this.navigationType = navigationType
        this.blockId = blockId
        this.type = 'nav'
    }
}


/**
 * 电子围栏
 */
export class CrawlProtect {
    id: string
    name: string
    latlngs: Array<L.LatLng>; // 电子围栏坐标点围成的一级预警区（警戒区）
    navLatlngs: Array<L.LatLng>; // 灯桩坐标点围成的二级预警区（电场区）
    center?: L.Point; // 中心点
    constructor(id: string, name: string) {
        this.id = id
        this.name = name
        this.latlngs = []
        this.navLatlngs = []
    }
}

/**
 * 海底电缆
 */
export class CableProtect {
    id: string; // blockId
    name: string;
    latlngs: Array<L.LatLng>; // 海底电缆的坐标点 点连成的线
    center?: L.Point // 中心点
    constructor(id: string, name: string) {
        this.id = id
        this.name = name
        this.latlngs = []
    }
}
/**
 * 海底电缆外围(后台返回)
 */
export class CableWarmProtect {
    latlngs: Array<L.LatLng>;
    name: string
    constructor(name: string) {
        this.latlngs = []
        this.name = name
    }
}

/**
 * 风电场保护区
 */
export class WindpowerProtect {
    id: string; // id
    navList: Array<NavMarkProtect>; // 航标
    crawlList: Array<CrawlProtect>;// 电子围栏
    cableList: Array<CableProtect>;// 海底电缆
    fanList: Array<FanProtect>; // 风机
    cableWarmLists: Array<CableWarmProtect> = []
    constructor(id: string) {
        this.id = id
        this.navList = []
        this.crawlList = []
        this.cableList = []
        this.fanList = []
        this.cableWarmLists = []
    }
}
export class AisMarkBroadcastSearch extends BaseSearch {
    mmsi?: string;
    stationId?: string;
}
export interface AisMarkBroadcast {
    mmsi?: string
    sendDate?: string  //播发时间
    aidsNameEn?: string//航标英文名
    aisName: string//航标中文名
}

