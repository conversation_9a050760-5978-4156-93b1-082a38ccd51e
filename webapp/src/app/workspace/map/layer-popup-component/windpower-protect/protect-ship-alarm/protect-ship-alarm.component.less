@import "/src/styles/mixins";
.table-container {
  background: transparent;
  width: 100%;
  padding: 11px 10px;
  .table-container__body {
    height: 181px; // 固定高度，5条数据的高度
    overflow: hidden;
    .scrollbars(5px, #C8C9CC, rgba(0, 0, 0, 0));
    .table-container__body__inner {
      height: 100%;
      overflow-y: auto;
    }
  }
}
table {
  tr {
    height: 36px;
    td {
      border: 1px solid rgba(215, 219, 232, 1);
      overflow: hidden;
      word-break: keep-all;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    &.tr-title {
      td {
        background: #eef4fe;
        border-bottom: none;
      }
    }
    &.tr-content:nth-child(odd) {
      background: #eef4fe;
    }
  }
}
