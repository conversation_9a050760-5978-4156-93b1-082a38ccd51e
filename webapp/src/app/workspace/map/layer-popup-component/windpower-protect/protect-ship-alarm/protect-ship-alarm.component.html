<div class="table-container" #handleBottomContainer>
    <div class="table-container__title" [style.paddingRight.px]="list.length>5?5:0">
        <table>
            <ng-container [ngTemplateOutlet]="tableCol"></ng-container>
            <tr class="tr-title">
                <td class="text-center">序号</td>
                <td class="text-center">船舶名称</td>
                <td class="text-center">MMSI</td>
                <td class="text-center">关联保护区</td>
                <td class="text-center">报警类型</td>
                <td class="text-center">报警内容</td>
                <td class="text-center">报警时间</td>
                <td class="text-center">报警状态</td>
            </tr>
        </table>
    </div>
    <!--不固定高度的做法-->
    <!--[style.height]="list.length>5?'181px':'auto'"-->
    <div class="table-container__body" #tableContainer>
        <div class="table-container__body__inner">
            <table>
                <ng-container [ngTemplateOutlet]="tableCol"></ng-container>
                <tr class="tr-content" *ngFor="let item of list;index as i">
                    <td class="text-center">{{i+1}}</td>
                    <td style="padding-left: 8px;">
                        <a href="javascript:void(0)" style="display: flex;align-items: center;"
                            [style.color]="item.color" (click)="toShipInfo(item.mmsi!)">
                            <i class="{{item.shipIcon}}"></i>&nbsp;&nbsp;
                            <span>{{item.shipName || item.mmsi}}</span>
                        </a>
                    </td>
                    <td class="text-center" [style.color]="item.color">{{item.mmsi}}</td>
                    <td class="text-center" [style.color]="item.color">{{item.areaName}}</td>
                    <td class="text-center" [style.color]="item.color">{{item.alarmTypeText}}</td>
                    <td style="padding-left: 8px;" [style.color]="item.color">{{item.alarmContent}}</td>
                    <td class="text-center" [style.color]="item.color">{{item.alarmTime}}</td>
                    <!--报警状态固定为红色-->
                    <!--二三级报警装全部字段为红色，一级报警除了报警状态其他字段显示为#333-->
                    <td class="text-center" style="color:#ED1313">{{item.alarmStatusText}}</td>
                </tr>
                <tr *ngIf="!list.length">
                    <td colspan="8" style="height: 180px;">
                        <nz-spin *ngIf="spining" [nzSpinning]="true" nzTip="数据加载中..." style="height: 100px;"></nz-spin>
                        <nz-empty *ngIf="!spining" style="margin:10px 0"></nz-empty>
                    </td>
                </tr>
            </table>
        </div>
    </div>
</div>

<ng-template #tableCol>
    <colgroup>
        <!--serial 50 MMSI 120 ,报警类型 120  报警时间140 报警状态120-->
        <!--50 + 120 + 120 + 140 + 120 -->
        <col style="width: 50px;" />
        <col style="width: 200px;" />
        <col style="width: 120px;" />
        <col style="width: 300px;" />
        <col style="width: 120px;" />
        <col style="width: 300px;" />
        <col style="width: 140px;" />
        <col style="width: 120px;" />
    </colgroup>
</ng-template>