import { debounceTime } from 'rxjs/operators';
import { Subscription } from 'rxjs';
import { Component, ElementRef, OnInit, ViewChild, OnDestroy } from '@angular/core';
import { TblAlarmrecord, } from 'src/app/workspace/workspace-shared/models/tbl_alarmrecord';
import { HandleLayerControlService } from '../../../handle-layer-control/handle-layer-control.service';
import * as $ from 'jquery'
import { WindpowerShipService } from '../../windpower-ship/windpower-ship.service';
import { MapLayerComponentService } from '../../../service/map-layer-component.service';
import * as L from 'leaflet';
import { DynamicComponent } from 'src/app/shared/models';
import { MapStateService } from '../../../service/map-state.service';
@Component({
  selector: 'app-protect-ship-alarm',
  templateUrl: './protect-ship-alarm.component.html',
  styleUrls: ['./protect-ship-alarm.component.less']
})
export class ProtectShipAlarmComponent implements OnInit, OnDestroy {
  spining: boolean = false
  list: Array<TblAlarmrecord> = []
  map: L.Map

  @ViewChild('handleBottomContainer') handleBottomContainer!: ElementRef;
  @ViewChild('tableContainer') tableContainer!: ElementRef;

  private alarmSub: Subscription

  constructor(
    private handleLayerControlService: HandleLayerControlService,
    private windpowerShipService: WindpowerShipService,
    private mapLayerService: MapLayerComponentService,
    private mapState: MapStateService

  ) {
    this.map = this.mapState.getMapInstance()
    this.alarmSub = this.handleLayerControlService.alarmSearching$.subscribe(({ search, list }) => {
      if (search) {
        this.spining = true
        setTimeout(() => {
          this.list = list
          this.spining = false
        }, 100);
      } else {
        this.list = list
      }
    })
  }
  ngOnDestroy(): void {
    this.alarmSub.unsubscribe()
  }
  ngOnInit(): void {

  }

  private computedContainerHeight() {
    setTimeout(() => {
      const height = $(this.handleBottomContainer.nativeElement).height()
      if (height !== undefined) {
        this.handleLayerControlService.alarmHeight = height + 17
      }
    });
  }
  /**
   * 跳转海图 船舶详细
   * @param mmsi 
   */
  toShipInfo(mmsi: string) {
    this.windpowerShipService.getInfo(mmsi).then(res => {
      const item = res
      const latlng: L.LatLng = L.latLng(item.latitude, item.longitude)!
      const shipInfoComponent: DynamicComponent = {
        title: `${item.shipName || item.mmsi}（详细）`,
        name: 'ShipWindpowerInfoComponent',
        type: 'popup',
        titleType: 'primary',
        data: {
          params: item,
          // 使用海图的定位弹出详情
          position: {
            latlng: latlng,
            offset: [0, 24]
          }
        }
      }
      this.mapLayerService.addComponent(shipInfoComponent)
      this.map.setView(latlng, 14)
    }).catch(err => console.log(err))
  }
}
