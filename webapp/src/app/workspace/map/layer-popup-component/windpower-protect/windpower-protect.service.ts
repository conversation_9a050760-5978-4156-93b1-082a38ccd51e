import { Tbl<PERSON><PERSON><PERSON>, TblProareaSearch } from './../../../workspace-shared/models/tbl_proarea';
import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import { BaseCURDService } from 'src/app/shared/services/base-curd.service';
import { BaseHttpService } from 'src/app/shared/services/base-http.service';
import { NgForm } from '@angular/forms';
import { UUIDModel } from 'src/app/workspace/workspace-shared/models/uuid_model';
import { MapLayerComponentService } from '../../service/map-layer-component.service';
import { SlValidateService } from 'src/app/shared/modules/sl-validate/sl-validate.service';
import { SlModalService } from 'src/app/shared/modules/sl-modal/sl-modal.service';
import { AisMarkBroadcast, AisMarkBroadcastSearch, ProtectHandleStatusNotify, ProtectHanldeStatus } from './windpower-protect.model';
import { Page } from 'src/app/shared/models';


@Injectable({
  providedIn: 'root'
})
export class WindpowerProtectService extends BaseCURDService<TblProarea> {

  // 打开组件，用来比对列表中图标（编辑、详细）状态 
  private _cachedComponentList: Array<ProtectHanldeStatus> = []

  private _cachedChange$: Subject<ProtectHandleStatusNotify> = new Subject()

  private nextTab$: Subject<any> = new Subject()
  private tabForm$: Subject<any> = new Subject()

  private _protectCoverageChange$: Subject<Array<{ type: 'crawl' | 'cable' | 'nav' | 'fan', values?: string[] }>> = new Subject()

  constructor(
    protected http: BaseHttpService,
    private mapLayerService: MapLayerComponentService,
    private slModalService: SlModalService,
    private slValidateService: SlValidateService
  ) {
    super(http, '/api/Proarea')
  }

  get componentChange$() {
    return this._cachedChange$.asObservable()
  }

  get protectCoverageChange$() {
    return this._protectCoverageChange$.asObservable()
  }

  get nextTabChange() {
    return this.nextTab$.asObservable()
  }

  get tabFormStateChange() {
    return this.tabForm$.asObservable()
  }

  set tabState(data: { form: NgForm, list: Array<UUIDModel> }) {
    this.nextTab$.next(data)
  }

  get cachedComponetList() {
    return this._cachedComponentList
  }

  addComponent(state: ProtectHanldeStatus) {
    // const item: ProtectHanldeStatus = { id, type, componentType }
    // 每一种组件类型只可打开一个 （风机、航标、电缆、电子围栏 的详情和编辑可以同时）
    // const idx = this._cachedComponentList.findIndex(ele => ele.componentType == state.componentType && ele.type == state.type)
    // 每一种 popup 类型只能打开一个 （风机、航标、电缆、电子围栏 的详情只能同时存在一个并且4者编辑同时也只能存在一个）
    const idx = this._cachedComponentList.findIndex(ele => ele.type == state.type)
    if (idx > -1) {
      // 每一种组件类型只可打开一个 （风机、航标、电缆、电子围栏 的详情和编辑可以同时）
      // const existItem = this._cachedComponentList[idx]
      // existItem.id = state.id

      // 每一种 popup 类型只能打开一个 （风机、航标、电缆、电子围栏 的详情只能同时存在一个并且4者编辑同时也只能存在一个）
      this._cachedComponentList.splice(idx, 1, state)

    } else {
      this._cachedComponentList.push(state)
    }
    this._cachedChange$.next({ active: true, state, list: this._cachedComponentList })
  }

  removeComponent(state: ProtectHanldeStatus) {
    const idx = this._cachedComponentList.findIndex(ele => ele.componentType == state.componentType && ele.type == state.type)
    if (idx > -1) {
      this._cachedComponentList.splice(idx, 1)
      this._cachedChange$.next({ active: false, state, list: this._cachedComponentList })
    }
  }

  // 传递tab 中的form 表单
  setTabForm(form: NgForm, index: number, list: Array<UUIDModel>) {
    this.tabForm$.next({ form, index, list })
  }

  /**
   *
   * 保护区图层选中的类型
   * @param {Array<{ type: string, values?: string[] }>} selectedList
   * @memberof WindpowerProtectService
   */
  notyfyProtectTypeChange(selectedList: Array<{ type: 'crawl' | 'cable' | 'nav' | 'fan', values?: string[] }>) {
    this._protectCoverageChange$.next(selectedList)
  }

  /**
   * 关闭popup
   * @param componentName 
   * @param hasTip  是否需要确认
   */
  closePopup(componentName: string, confirm: boolean = true) {
    if (confirm) {
      this.slModalService.openPromptModal({
        type: 'confirm',
        content: '确定取消吗？',
        okCb: () => {
          this.removePopup(componentName)
        }
      })
    } else {
      this.removePopup(componentName)
    }
  }

  /**
   * 直接关闭popup
   * @param componentName 
   */
  removePopup(componentName: string) {
    this.mapLayerService.removeComponent({ destroy: true, name: componentName })
  }

  /**
   * 校验表单并弹出错误信息
   * @param form 
   * @returns 
   */
  validateFormWithAlert(form: NgForm) {
    return this.slValidateService.validateFormWithAlert(form)
  }

  /**
   * 关闭整体handle 
   */
  removeHandleComponent() {
    this.mapLayerService.removeComponent(
      {
        destroy: true,
        name: 'ProtectHandleComponent'
      }
    )
  }

  /**
   * 刷新列表
   */
  refreshListComponent() {
    this.mapLayerService.refreshComponent(
      {
        name: 'ProtectListComponent'
      }
    )
  }

  getAllList = (): Promise<Array<TblProarea>> => {
    return this.http.post(`/api/Proarea/getAllList`, { ifPage: false });
  };

  getList2 = (data: TblProareaSearch): Promise<Array<TblProarea>> => {
    return this.http.post(`/api/Proarea/getList2`, data);
  };

  // 获取电场详情（单个、或整体）
  getInfo2 = (data: TblProareaSearch): Promise<TblProarea> => {
    return this.http.post(`/api/Proarea/getInfo2`, data);
  }
  // 单个保存
  commit = (data: { searchLevel: string, list: Array<UUIDModel> }) => {
    return this.http.post(`/api/Proarea/commit`, data);
  }
  // 获取播发记录
  getBraodcastList(search: AisMarkBroadcastSearch) {
    return this.http.post<Page<AisMarkBroadcast>, AisMarkBroadcastSearch>(`/api/Virtualatonrecord/getList`, search)
  }
}
