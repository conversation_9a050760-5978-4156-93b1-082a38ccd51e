<!--[class.has-bg]="item.index % 2 ==1"-->
<li class="item" [style.paddingLeft.px]="level*12">
    <a href="javascript:void(0)" (click)="clickItem($event)">
        <i class="sl-{{item.icon}}-16" *ngIf="item.icon"></i>
        <i *ngIf="item.children" class="sl-caret-{{item.expanded?'down':'right'}}-16"
            [class.expanded]="item.expanded"></i>
        <span>
            {{ item.label }}
            <span *ngIf="item.children"> （{{item.children.length }}）</span>
        </span>
    </a>
    <div class="btn">
        <a href="javascript:void(0)" title="编辑" *slHasAnyAuthority="'1.1.2'" (click)="edit($event)">
            <i class="sl-edit{{item.editSelected?'-active':''}}-16"></i>
        </a>
        <a href="javascript:void(0)" title="详情" *slHasAnyAuthority="'1.1.4'" (click)="toInfo($event)">
            <i class="sl-info{{item.infoSelected?'-active':''}}-16"></i>
        </a>
    </div>
</li>
<ng-container *ngIf="item.children?.length">
    <ul class="ul-sublist" [class.expanded]="item.expanded">
        <ng-container *ngFor="let subItem of item.children;index as i">
            <app-protect-list-item [item]="subItem" [level]="level+1" (onInfoItem)="toInfo($event)"
                (onEditItem)="edit($event)" (onClickItem)="clickItem($event)">
            </app-protect-list-item>
        </ng-container>
    </ul>
</ng-container>