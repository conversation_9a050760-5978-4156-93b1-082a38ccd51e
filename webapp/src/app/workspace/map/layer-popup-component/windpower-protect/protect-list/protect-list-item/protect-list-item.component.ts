import { stopBubble, stopDefault } from 'src/app/shared/utils/document-event';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ProtectTreeNode } from '../../windpower-protect.model';

@Component({
  selector: 'app-protect-list-item',
  templateUrl: './protect-list-item.component.html',
  styleUrls: ['./protect-list-item.component.less']
})
export class ProtectListItemComponent implements OnInit {

  @Input() item!: ProtectTreeNode

  @Input() level: number = 0


  @Output() onClickItem: EventEmitter<any> = new EventEmitter()

  @Output() onEditItem: EventEmitter<any> = new EventEmitter()

  @Output() onInfoItem: EventEmitter<any> = new EventEmitter()

  constructor() { }

  ngOnInit(): void {
  }

  clickItem(event: MouseEvent) {
    const selNode = this.getSelectedNode(event)
    this.onClickItem.emit(selNode);
  }

  edit(event: MouseEvent) {
    const selNode = this.getSelectedNode(event)
    this.onEditItem.emit(selNode);
  }

  toInfo(event: MouseEvent) {
    const selNode = this.getSelectedNode(event)
    this.onInfoItem.emit(selNode);
  }

  private getSelectedNode(event: MouseEvent) {
    let selNode: any;
    if (event instanceof MouseEvent) {
      stopBubble(event)
      stopDefault(event)
      selNode = this.item
    } else {
      selNode = event
    }
    return selNode
  }
}
