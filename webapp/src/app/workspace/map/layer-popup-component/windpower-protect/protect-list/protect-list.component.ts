import { MapStateService } from './../../../service/map-state.service';
import { isEmpty } from 'src/app/shared/utils/convert';
import { NAV_MARK_LIST } from './../protect-data';
import { MapLayerComponentService } from './../../../service/map-layer-component.service';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { WindpowerProtectService } from '../windpower-protect.service';
import { TblProarea, TblProareaSearch } from 'src/app/workspace/workspace-shared/models/tbl_proarea';
import { DynamicComponent } from 'src/app/shared/models';
import { NavMarkProtect, proareaType, ProtectHanldeStatus, ProtectTreeNode } from '../windpower-protect.model';
import * as _ from 'lodash';
import * as L from 'leaflet';
import * as turf from '@turf/turf'
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-protect-list',
  templateUrl: './protect-list.component.html',
  styleUrls: ['./protect-list.component.less']
})
export class ProtectListComponent implements OnInit, OnDestroy {

  spinning: boolean = false
  map: L.Map
  // 搜索实体
  search: TblProareaSearch = new TblProareaSearch()
  // 数据索引（列表没有使用斑马条纹，可以废弃）
  dataIndex: number = 0
  // 保护区子级数据
  dataList: Array<any> = [
    { type: 'crawl', icon: 'crawl', label: '电子围栏', searchLevel: '2', listPro: 'crawlList', children: [] },
    { type: 'cable', icon: 'cable', label: '海底电缆', searchLevel: '3', listPro: 'cableList', children: [] },
    { type: 'navigation', label: '航标', searchLevel: '4', listPro: 'navigationList', children: [] },
    { type: 'fan', icon: 'fan', label: '风机', searchLevel: '5', listPro: 'fanList', children: [] },
  ]
  private navList: Array<{ type: string, name: string, list: Array<NavMarkProtect>, icon: string }> = []
  // 树形数组
  list: Array<ProtectTreeNode> = []
  // 从图层中打开详细组件的订阅
  // currentICChangeSub: Subscription
  // 从图层中打开详细、编辑组件的订阅
  componentChangeSub: Subscription
  // 所有的动态组件
  componentList: Array<{ index: number, type: string, singleComponents: Array<DynamicComponent> }> = [
    {
      index: 0,
      type: 'crawl', // 电子围栏
      singleComponents:
        [
          { name: 'ProtectWindpowerAreaInfoComponent' }, //  单个详细
          { name: 'ProtectWindpowerAreaHandleComponent' }// 电子围栏 编辑
        ]
    },
    {
      index: 1,
      type: 'cable',// 海底电缆 
      singleComponents:
        [
          { name: 'ProtectSeaCableInfoComponent' },
          { name: 'ProtectSeaCableHandleComponent' }
        ]
    },
    {
      index: 2,
      type: 'navigation', // 航标
      singleComponents:
        [
          { name: 'ProtectNavMarkInfoComponent' },
          { name: 'ProtectNavMarkHandleComponent' }
        ]
    },
    {
      index: 3,
      type: 'fan', // 风机
      singleComponents:
        [
          { name: 'ProtectWindMotorInfoComponent' },
          { name: 'ProtectWindMotorHandleComponent' }
        ]
    },
  ]

  constructor(
    private mapLayerService: MapLayerComponentService,
    private service: WindpowerProtectService,
    private mapState: MapStateService
  ) {
    this.navList = _.cloneDeep(NAV_MARK_LIST)
    this.map = this.mapState.getMapInstance()
    this.componentChangeSub = this.service.componentChange$.subscribe(res => {
      this.computedListIconState(this.list, res.list)
    })
  }
  ngOnDestroy(): void {
    this.componentChangeSub.unsubscribe()
  }

  ngOnInit(): void {
    this.getList(false);
  }

  private computedListIconState(list: ProtectTreeNode[], stateList: Array<ProtectHanldeStatus>) {
    for (let i = 0; i < list.length; i++) {
      const item = list[i]
      item.infoSelected = item.editSelected = false
      this.computedTreeNodeState(item, stateList)
      if (item.children && item.children.length) {
        this.computedListIconState(item.children, stateList)
      }
    }
  }



  private computedTreeNodeState(node: ProtectTreeNode, stateList: Array<ProtectHanldeStatus>) {
    const existList = stateList.filter(ele => ele.id == node.id)
    if (existList && existList.length) {
      existList.forEach(ele => {
        ele.type == 'edit' ? node.editSelected = true : node.infoSelected = true
      })
    }
  }


  private getList(expandedChild: boolean) {
    this.spinning = true
    this.service.getList2(this.search).then(res => {
      this.list = this.genTreeList(res, expandedChild)
      console.log('list', this.list);
      this.spinning = false
    }).catch(err => {
      this.spinning = false
    })
  }
  /**
   * 关键字搜索
   */
  searchKeyword() {
    this.getList(true)
  }

  searchType() {
    this.getList(true)
  }
  /**
      * 点击事件
      * @param item 
      */
  clickItem(item: ProtectTreeNode) {
    if (item.children && item.children.length) {
      item.expanded = !item.expanded
      if (!item.expanded) return
      if (item.level == '1') {
        // 定位到第一个电子围栏中心点位置
        const crawl = item.children[0]
        if (crawl && crawl.children) {
          const first = crawl.children[0]
          if (first.center) {
            this.map.setView(first.center, 13)
          }
        }
      }
    } else {
      if (item.center) {
        this.map.setView(item.center!, 13)
      } else {
        // 定位到具体的绘制图层
        if (item.latlng) {
          this.map.setView(item.latlng!, 15)
        }
      }
    }
  }
  /**添加保护区 */
  addProtect() {
    this.mapLayerService.addComponent(
      {
        title: '保护区（新增）',
        name: 'ProtectHandleComponent',
        type: 'popup',
        data: {
          params: {
            id: '',
          },
          position: {
            left: 358,
            top: 50
          }
        }
      }
    )
  }

  /**
   * 编辑保护区
   * @param item 
   */
  edit(item: ProtectTreeNode) {
    const search: TblProareaSearch = { searchLevel: item.searchLevel, areaId: item.areaId }
    let editComponent: DynamicComponent = {
      type: 'popup',
      title: `${item.label}（编辑）`,
      data: {
        position: {
          left: 358,
          top: 50
        },
      }
    }
    let params: any = {}
    if (item.leaf) { // 第三级 跳转到单个编辑页面
      search.blockId = item.blockId
      const comItem = this.componentList.find(ele => ele.type === item.type)!
      editComponent.name = comItem.singleComponents[1].name
      params.name = item.label
      if (editComponent.data && item.latlng) {
        // editComponent.data.position = { latlng: item.center || item.latlng, offset: [0, 30] } // 定位到海图上具体的绘制点
        this.map.setView(item.center || item.latlng)
        if (this.map.getZoom() < 10) this.map.setZoom(10)
      }
    } else { //跳转到整体编辑页面
      params.tabIndex = 0 // 默认定位到第一个tab 
      editComponent.name = 'ProtectHandleComponent'
      if (item.level != '1') {
        const comItem = this.componentList.find(ele => ele.type === item.type)!
        params.tabIndex = comItem.index
      }
    }

    this.service.getInfo2(search).then(res => {
      params.infoData = res
      editComponent.data!.params = params
      // const id = item.leaf ? search.blockId : search.areaId // blockId  说明是单个详情
      const state: ProtectHanldeStatus = { id: item.id!, selectedIconLatlng: item.latlng, type: 'edit', componentType: item.leaf ? item.type : 'proarea' }
      editComponent.closeCb = this.closeComponentCb(state)
      this.mapLayerService.addComponent(editComponent)
      // 通知列表打开了详细组件
      this.service.addComponent(state)
    })
  }

  /**
   * 打开详情
   */
  toInfo(item: ProtectTreeNode) {
    // console.log(item);
    const search: TblProareaSearch = { searchLevel: item.searchLevel, areaId: item.areaId }

    // 整体详细
    const isPart = item.searchLevel.split('-').length > 1 // 整体详情 searchlevel 1-5, 单个详情 1-1，1-2

    let infoComponent: DynamicComponent = {
      type: 'popup',
      title: `${item.label}（详细）`,
      // titleType: item.searchLevel == '4-1' && item.type == 'navigation' ? 'primary' : 'default',// 航标单个详细显示 primary title

      titleType: isPart ? 'primary' : 'default', // 单个详情时显示 primary title
      // 默认整体详情，固定位置打开
      data: {
        position: {
          left: 358,
          top: 50
        },
      }
    }
    let params: any = {}
    if (item.leaf) { // 第三级 跳转到单个详情页面
      search.blockId = item.blockId
      const comItem = this.componentList.find(ele => ele.type === item.type)!
      infoComponent.name = comItem.singleComponents[0].name
      params.name = item.label
      if (infoComponent.data && item.latlng) {
        this.map.setView(item.center || item.latlng)
        if (this.map.getZoom() < 10) this.map.setZoom(10)
        // 单个详情页面需要进行定位
        infoComponent.data.position = { latlng: item.center || item.latlng, offset: [0, 30] }
      }
    } else { //跳转到整体详情页面
      params.tabIndex = 0 // 默认定位到第一个tab 
      infoComponent.name = 'ProtectInfoComponent'
      if (item.level != '1') {
        const comItem = this.componentList.find(ele => ele.type === item.type)!
        params.tabIndex = comItem.index
      }
    }

    this.service.getInfo2(search).then(res => {
      params.infoData = res
      infoComponent.data!.params = params
      // const id = item.leaf ? search.blockId : search.areaId // blockId  说明是单个详情
      const state: ProtectHanldeStatus = { id: item.id!, selectedIconLatlng: item.latlng, type: 'info', componentType: item.leaf ? item.type : 'proarea' }
      infoComponent.closeCb = this.closeComponentCb(state)
      this.mapLayerService.addComponent(infoComponent)
      this.service.addComponent(state)
    })
  }

  /**
   * 关闭详细、编辑popup 回调 (点击popup上的 关闭图标)
   */
  private closeComponentCb = (state: ProtectHanldeStatus) => {
    return () => {
      this.service.removeComponent(state)
    }

  }

  /**
    *
    * 通过坐标点数组获取绝对中心点
    * @private
    * @param {Array<L.LatLng>} latlngs
    * @return {*}  {L.Point}
    * @memberof WindpowerProtectCanvasLayer
    */
  private getGeoJSONCenter(latlngs: Array<L.LatLng>): L.LatLng {
    const features = turf.featureCollection(
      latlngs.map((ele) => turf.point([ele.lat, ele.lng]))
    )
    const center = turf.center(features).geometry.coordinates
    return L.latLng(center[0], center[1])
  }

  /**
   *
   * 生成树形数组
   * @private
   * @param {Array<TblProarea>} arr
   * @return {*}  {Array<ProtectTreeNode>}
   * @memberof ProtectListComponent
   */
  private genTreeList(arr: Array<TblProarea>, expandedChild: boolean): Array<ProtectTreeNode> {
    this.dataIndex = 0
    return arr.map(proarea => {
      this.dataIndex++
      const item: ProtectTreeNode = {
        searchLevel: '1',
        expanded: true,
        type: 'proarea',
        label: proarea.areaName!,
        level: '1',
        areaId: proarea.id!,
        id: proarea.id!,
        index: this.dataIndex,
        children: []
      }
      this.dataList.forEach((e) => {
        const list: Array<any> = proarea[e.listPro]
        if (list && list.length) {
          this.dataIndex++
          const subItem: ProtectTreeNode = {
            expanded: expandedChild,
            label: e.label,
            index: this.dataIndex,
            areaId: proarea.id!,
            id: proarea.id! + e.type,
            level: e.searchLevel,
            type: e.type,
            searchLevel: e.searchLevel,
          }
          subItem.children = this.genItemChildren(e.searchLevel, list, proarea.id!, e.type, e.icon)
          if (item.children)
            item.children.push(subItem)
        }
      })
      return item
    })
  }

  /**
   * 根据航标类型获取航标的icon
   * @param type 
   * @returns 
   */
  private getNavMarkIcon(type: string): string | undefined {
    return this.navList.find(ele => ele.type == type)?.icon
  }

  /**
   *
   * 获取保护区子级的children
   * @private
   * @param {string} parentLevel
   * @param {Array<any>} child
   * @param {string} blockId 区域主键（前端生成:name 相同为同一blockId）
   * @return {*} 
   * @memberof ProtectListComponent
   */
  private genItemChildren(parentLevel: string, child: Array<any>, areaId: string, type: proareaType, icon: string): ProtectTreeNode[] {
    const isNav = isEmpty(icon)
    return child.map((ele, index) => {
      this.dataIndex++
      if (isNav) {
        icon = this.getNavMarkIcon(ele.navigationType)!
      }
      // 计算中心点：电子围栏
      let center: L.LatLng
      if (ele.list && ele.list.length) {
        const latlngs = ele.list.map((e: any) => L.latLng(e.latitude, e.longitude))
        center = this.getGeoJSONCenter(latlngs)
      }
      // 计算海底电缆中心点
      else if (ele.cableWarmLists && ele.cableWarmLists.length) {
        const list = ele.cableWarmLists[0].cableWarmList
        const latlngs = list.map((e: any) => L.latLng(e.y, e.x))
        center = this.getGeoJSONCenter(latlngs)
      }

      return {
        label: ele.name,
        level: `${parentLevel}-${index + 1}`,
        id: ele.id,
        index: this.dataIndex,
        blockId: ele.blockId,
        areaId,
        leaf: true,
        type,
        searchLevel: `${parentLevel}-1`,
        icon,
        latlng: L.latLng(ele.latitude, ele.longitude),
        list: ele.list,
        center: center!,// 中心点（电子围栏 、海底电缆）
        mmsi: ele.mmsi
      }
    })
  }
}
