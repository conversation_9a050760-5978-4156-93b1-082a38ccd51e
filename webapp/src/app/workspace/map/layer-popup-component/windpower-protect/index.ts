import { DynamicComponent } from 'src/app/shared/models';
import { ProtectListComponent } from './protect-list/protect-list.component';
import { ProtectInfoComponent } from './protect-info/protect-info.component';

// 单个详情组件
import {
    ProtectSeaCableInfoComponent,
    ProtectWindMotorInfoComponent,
    ProtectNavMarkInfoComponent,
    ProtectWindpowerAreaInfoComponent,
    NavMarkBroadcastComponent
} from './protect-windpower-info';

// 单个新增/编辑组件
import {
    ProtectSeaCableHandleComponent,
    ProtectWindMotorHandleComponent,
    ProtectNavMarkHandleComponent,
    ProtectWindpowerAreaHandleComponent
} from './protect-windpower-handle';
import { ProtectHandleComponent } from './protect-handle/protect-handle.component';
// 保护区图层
import { WindpowerProtectLayerComponent } from './windpower-protect-layer';
// 报警列表
import { ProtectShipAlarmComponent } from './protect-ship-alarm/protect-ship-alarm.component';

// 保护区
export const DYNAMIC_WINDPOWER_PROTECT_COMPONENTS: Array<DynamicComponent> = [
    // 保护图层
    { name: 'WindpowerProtectLayerComponent', component: WindpowerProtectLayerComponent },
    // 风电场保护区列表
    { name: 'ProtectListComponent', component: ProtectListComponent, hasChild: true },
    // 保护区详细
    { name: 'ProtectInfoComponent', component: ProtectInfoComponent, parentName: 'ProtectListComponent', aliasName: 'SingleProtectInfoComponent' },
    // 保护区新增||保护区编辑 同时打开一个 (如想要同时打开，需要多配置一条)
    { name: 'ProtectHandleComponent', component: ProtectHandleComponent, parentName: 'ProtectListComponent', aliasName: 'SingleProtectEditComponent' },
    // 风电场灯桩 单个详细
    { name: 'ProtectNavMarkInfoComponent', component: ProtectNavMarkInfoComponent, parentName: 'ProtectListComponent', aliasName: 'SingleProtectInfoComponent' },
    // 风电场灯桩 单个新增/编辑
    { name: 'ProtectNavMarkHandleComponent', component: ProtectNavMarkHandleComponent, parentName: 'ProtectListComponent', aliasName: 'SingleProtectEditComponent' },
    // 风电场区域 单个详细
    { name: 'ProtectWindpowerAreaInfoComponent', component: ProtectWindpowerAreaInfoComponent, parentName: 'ProtectListComponent', aliasName: 'SingleProtectInfoComponent' },
    // 风电场区域 单个新增/编辑
    { name: 'ProtectWindpowerAreaHandleComponent', component: ProtectWindpowerAreaHandleComponent, parentName: 'ProtectListComponent', aliasName: 'SingleProtectEditComponent' },
    // 海底电缆 单个详细
    { name: 'ProtectSeaCableInfoComponent', component: ProtectSeaCableInfoComponent, parentName: 'ProtectListComponent', aliasName: 'SingleProtectInfoComponent' },
    // 海底电缆 单个新增/编辑
    { name: 'ProtectSeaCableHandleComponent', component: ProtectSeaCableHandleComponent, parentName: 'ProtectListComponent', aliasName: 'SingleProtectEditComponent' },
    // 风机 单个详细
    { name: 'ProtectWindMotorInfoComponent', component: ProtectWindMotorInfoComponent, parentName: 'ProtectListComponent', aliasName: 'SingleProtectInfoComponent' },
    // 风机 单个新增/编辑
    { name: 'ProtectWindMotorHandleComponent', component: ProtectWindMotorHandleComponent, parentName: 'ProtectListComponent', aliasName: 'SingleProtectEditComponent' },
    // 保护区报警船舶列表
    { name: 'ProtectShipAlarmComponent', component: ProtectShipAlarmComponent },
    // AIS航标播发记录
    { name: 'NavMarkBroadcastComponent', component: NavMarkBroadcastComponent },
];