import * as L from "leaflet"
import * as turf from '@turf/turf'
import * as _ from "lodash"
import rbush from 'rbush'
import { TblProarea } from "src/app/workspace/workspace-shared/models/tbl_proarea"
import { TblFan } from "src/app/workspace/workspace-shared/models/tbl_fan"
import { TblNavigation } from "src/app/workspace/workspace-shared/models/tbl_navigation"
import { TblCrawl } from "src/app/workspace/workspace-shared/models/tbl_crawl"
import { TblCable } from "src/app/workspace/workspace-shared/models/tbl_cable"
import { CableProtect, CrawlProtect, FanProtect, MarkerPoint, NavMarkProtect, toolTipPosition, WindpowerProtect } from "../windpower-protect.model"
import { NAV_MARK_LIST } from "../protect-data"
export class WindpowerProtectCanvasLayer extends L.Layer {
    private fanImgUrl: string
    private fanNavImgSize: [number, number] = [28, 28]
    private baseNavImgUrl: string
    private _canvas!: HTMLCanvasElement
    private _context!: CanvasRenderingContext2D
    options: any
    animationLoop: number | undefined
    private _onClickListeners: Array<any> = [];
    // 保护区图层数据
    cachedProtectList: Array<TblProarea> = []

    /** 保护区图层 */
    cachedProtectLayerList: Array<WindpowerProtect> = []

    private marks = new rbush()

    // 航标
    private navList: Array<{ type: string, name: string, list: Array<NavMarkProtect>, icon: string }> = []

    // 当前图层选中的保护区类型（电子围栏、海底电缆，风机，航标）
    selectedList: Array<{ type: 'crawl' | 'cable' | 'nav' | 'fan', values?: Array<string> }> = [
        { type: 'crawl' },
        { type: 'cable' },
        { type: 'fan' },
        { type: 'nav', values: ['0101', '0102', '0103', '0104', '0105'] },
    ]


    constructor() {
        super()
        this.fanImgUrl = `/assets/img/map/protect/fan.png`
        this.baseNavImgUrl = `/assets/img/map/protect`
        this.navList = _.cloneDeep(NAV_MARK_LIST)
    }

    /**addTo时会自动调用 */
    onAdd(map: L.Map) {
        this._map = map
        if (!this._canvas) this.initCanvas()
        if (this.options.pane) this.getPane()?.appendChild(this._canvas)
        else map.getPanes().overlayPane.appendChild(this._canvas)
        map.on('moveend', this.reset, this)
        map.on('resize', this.reset, this)
        map.on('click', this._executeListeners, this)
        map.on('mousemove', this._executeListeners, this)
        if (map.options.zoomAnimation && L.Browser.any3d) {
            /**缩放动画 */
            map.on('zoomanim', this._animateZoom, this)
        }
        this.reset()
        return this
    }

    /**移除时会自动调用 */
    onRemove(map: any) {
        if (this.options.pane) {
            this.getPane()?.removeChild(this._canvas)
        } else {
            map.getPanes().overlayPane.removeChild(this._canvas)
        }
        map.off('moveend', this.reset, this)
        map.off('resize', this.reset, this)
        if (map.options.zoomAnimation) {
            map.off('zoomanim', this._animateZoom, this)
        }
        if (this.animationLoop) cancelAnimationFrame(this.animationLoop)
        return this
    }

    private initCanvas() {
        this._canvas = L.DomUtil.create(
            'canvas',
            'leaflet-canvas-map leaflet-layer'
        )
        var originProp =
            '' +
            L.DomUtil.testProp([
                'transformOrigin',
                'WebkitTransformOrigin',
                'msTransformOrigin',
            ])
        this._canvas.style[originProp] = '50% 50%'
        this._canvas.style['z-index'] = 50
        var size = this._map.getSize()
        this._canvas.width = size.x
        this._canvas.height = size.y
        this._context = this._canvas.getContext('2d')!
        var animated = this._map.options.zoomAnimation && L.Browser.any3d
        L.DomUtil.addClass(
            this._canvas,
            'leaflet-zoom-' + (animated ? 'animated' : 'hide')
        )
    }

    reset() {
        const topLeft = this._map.containerPointToLayerPoint([0, 0])
        L.DomUtil.setPosition(this._canvas, topLeft)
        var size = this._map.getSize()
        this._canvas.width = size.x
        this._canvas.height = size.y
        this._redraw()
    }

    private _redraw() {
        this._clearContext()
        if (this.marks) this.marks.clear();
        const tmp: any[] = []
        this.cachedProtectLayerList.forEach(ele => {
            const retList = this._redrawProtectLayer(ele)
            if (retList.length) tmp.push(...retList)
        })
        this.marks.load(tmp)
    }

    /**
     * 重绘保护区
     */
    redrawProtectLayer(selectedList: Array<{ type: 'crawl' | 'cable' | 'nav' | 'fan', values?: Array<string> }>) {
        this.selectedList = selectedList
        this._redraw()
    }

    /**
     * 添加保护区图层
     * @param list  
     */
    addProtectLayer(list: Array<TblProarea>) {
        this.cachedProtectList = _.cloneDeep(list)
        this.cachedProtectLayerList = []
        const tmp: any[] = []
        list.forEach(ele => {
            const retList = this._addProtectLayer(ele)
            if (retList.length) tmp.push(...retList)
        })
        this.marks.load(tmp)
    }
    addOnClickListener(listener: any) {
        // this._onClickListeners.push(listener);
        // zj 避免多次触发事件
        this._onClickListeners = [listener];
    }
    private _executeListeners(event: any) {
        if (!this.marks) return;
        if (!this.cachedProtectLayerList || this.cachedProtectLayerList.length == 0) return;
        const x = event.containerPoint.x;
        const y = event.containerPoint.y;
        const ret = this.marks.search({ minX: x, minY: y, maxX: x, maxY: y });
        if (ret && ret.length > 0) {
            this._map['_container'].style.cursor = 'pointer';
            if (event.type === 'click') {
                this._onClickListeners.forEach((listener: any) => {
                    listener(event, ret);
                });
            }
            if (event.type === 'mousemove') {
            }
        } else {
            if (this._map)
                this._map['_container'].style.cursor = '';
        }
    }

    /**
     * 重绘保护区
     * @param ele 
     */
    private _redrawProtectLayer(ele: WindpowerProtect) {
        const retList: any[] = []
        const { crawlList, navList, fanList, cableList, cableWarmLists } = ele
        this.selectedList.forEach(item => {
            if (item.type == 'crawl') {
                // 电子围栏
                crawlList.forEach(ele => {
                    // 一级预警区
                    this._drawPloygon(ele.latlngs, 1)
                    // 二级预警区
                    const center = this._getGeoJSONCenter(ele.latlngs)
                    this._drawPloygon(ele.navLatlngs, 0, ele.name, center)
                })
            } else if (item.type == 'cable') {
                // 海底电缆
                cableList.forEach(ele => {
                    this._drawLine(ele.latlngs)
                })
                // 海底电缆外围
                cableWarmLists.forEach((ele) => {
                    const center = this._getGeoJSONCenter(ele.latlngs)
                    this._drawPloygon(ele.latlngs, 1, ele.name, center)
                })
            } else if (item.type == 'nav') {
                // 航标
                const selectedNavTypes = item.values!
                const selectedNavList = navList.filter(e => selectedNavTypes.includes(e.navigationType))
                selectedNavList.forEach(ele => {
                    const ret = this._addMarkLayer(ele)
                    retList.push(ret[0])
                })
            } else if (item.type == 'fan') {
                // 风机
                fanList.forEach(ele => {
                    const ret = this._addMarkLayer(ele, ele.distanceRadius)
                    retList.push(ret[0])
                })
            }
        })
        return retList
    }

    /**
     * 绘制保护区
     * @param ele 
     */
    private _addProtectLayer(tblPro: TblProarea) {
        const retList: any[] = []
        const wProtect = new WindpowerProtect(tblPro.id!)
        const { fanList, crawlList, cableList, navigationList, cableWarmLists } = tblPro
        // 电子围栏（一级预警--警戒区,二级预警---电场区）
        if (crawlList && crawlList.length) {
            const beaconMarkList = navigationList?.filter(ele => ele.navigationType === '0101') // 灯桩列表
            wProtect.crawlList = this._drawCrawl(crawlList, beaconMarkList!)
        }
        // 海底电缆
        if (cableList && cableList.length) {
            wProtect.cableList = this._drawCable(cableList)
            // 海底电缆外围
            if (cableWarmLists && cableWarmLists.length) {
                wProtect.cableWarmLists = []
                cableWarmLists.forEach((ele) => {
                    const latlngs = ele.cableWarmList.map((ele: { x: number, y: number }) => L.latLng(ele.y, ele.x))
                    const center = this._getGeoJSONCenter(latlngs)
                    this._drawPloygon(latlngs, 1, ele.name, center)
                    wProtect.cableWarmLists.push({ latlngs, name: ele.name })
                })
            }
        }

        // 风机（三级预警---风机保护区）
        if (fanList && fanList.length) {
            const radius = tblPro.fanDistance!
            fanList.forEach(e => {
                const ret = this._drawFan(e, radius)
                retList.push(ret[0])
                wProtect.fanList.push(<FanProtect>ret[0].data)
            })
        }
        // 航标
        if (navigationList && navigationList.length) {
            navigationList.forEach(e => {
                const ret = this._drawNav(e)
                if (ret) {
                    wProtect.navList.push(<NavMarkProtect>ret[0].data)
                    retList.push(ret[0])
                }
            })
        }
        this.cachedProtectLayerList.push(wProtect)
        return retList
    }

    /**
     * 绘制风机
     * @param fan 
     */
    private _drawFan(fan: TblFan, radius: number) {
        const latlng = L.latLng(fan.latitude!, fan.longitude!)
        const markerPoint: FanProtect = new FanProtect(fan.id!, latlng, this.fanImgUrl, this.fanNavImgSize, radius)
        markerPoint.infoData = fan
        markerPoint.toolTipText = fan.name
        markerPoint.toolTipPos = 'topleft' // 风机tip 左上角显示
        return this._addMarkLayer(markerPoint, radius)
    }

    /**
     * 绘制航标
     * @param nav 
     */
    private _drawNav(nav: TblNavigation) {
        const latlng = L.latLng(nav.latitude!, nav.longitude!)
        const navObj = this.navList.find(ele => ele.type == nav.navigationType)
        if (navObj) {
            const navImgUrl = `${this.baseNavImgUrl}/${navObj.icon}.png`
            const markerPoint: NavMarkProtect =
                new NavMarkProtect(nav.id!, latlng, navImgUrl, this.fanNavImgSize, nav.name!, nav.navigationType!, nav.blockId!)
            markerPoint.infoData = nav
            markerPoint.toolTipText = nav.name
            if (navObj.type == '0101') { // 灯桩 tip 右上角
                markerPoint.toolTipPos = 'topright'
            } else if (navObj.type == '0104') {  // 实体ais 航标
                markerPoint.toolTipText = ''
                markerPoint.nameText = '含实体AIS'
            }
            else {
                markerPoint.toolTipPos = 'topright' // 其他左下角
            }
            navObj.list.push(markerPoint)
            return this._addMarkLayer(markerPoint)
        }
        return null
    }

    /**
     * 绘制电子围栏 （一级预警区和二级预警区）
     * 
     * @param crawl 
     */
    private _drawCrawl(crawlList: TblCrawl[], navList: TblNavigation[]) {
        // 转换后的二维数组
        const arrayTwo: TblCrawl[][] = Object.values(crawlList.reduce((res, item) => {
            res[item.blockId!] ? res[item.blockId!].push(item) : res[item.blockId!] = [item]
            return res
        }, {}))
        const arr: CrawlProtect[] = []
        arrayTwo.forEach(ele => {
            // 电子围栏 外层
            const latlngs: L.LatLng[] = ele.map(e => L.latLng(e.latitude!, e.longitude!))
            // 航标（灯桩） 内
            const navLatlngs = navList.filter(e => e.crawlBlockId === ele[0].blockId).map(e => L.latLng(e.latitude!, e.longitude!))
            // 根据电子围栏的坐标点获取中心点（电子围栏一定会存在）
            const center = this._getGeoJSONCenter(latlngs)
            const crawlP: CrawlProtect = { id: ele[0].blockId!, name: ele[0].name!, latlngs, navLatlngs, center }
            arr.push(crawlP)
            // 绘制电子围栏多边形(外)
            this._drawPloygon(crawlP.latlngs, 1)
            // 根据灯桩绘制多边形（内）
            this._drawPloygon(crawlP.navLatlngs, 0, ele[0].name, center)
        })
        return arr
    }

    /**
     * 绘制海底电缆
     * @param cable 
     */
    private _drawCable(cableList: TblCable[]) {
        // 转换后的二维数组
        const arrayTwo: TblCrawl[][] = Object.values(cableList.reduce((res, item) => {
            res[item.blockId!] ? res[item.blockId!].push(item) : res[item.blockId!] = [item]
            return res
        }, {}))
        const arr: CableProtect[] = []
        arrayTwo.forEach(ele => {
            const latlngs: L.LatLng[] = ele.map(e => L.latLng(e.latitude!, e.longitude!))
            const cableP: CableProtect = { id: ele[0].blockId!, name: ele[0].name!, latlngs, }
            arr.push(cableP)
            this._drawLine(latlngs)
        })
        return arr
    }

    /**
     * 添加单个marker
     * @param markerPoint 
     */
    private _addMarkLayer(markerPoint: MarkerPoint, radius?: number) {
        if (radius) {
            this._drawCircle(markerPoint.latlng, radius!, 0)
        }
        this._drawImage(markerPoint)
        const point = this._map.latLngToContainerPoint(markerPoint.latlng)
        if (markerPoint.nameText) {
            this._setAreaName(point, markerPoint.nameText, 15, 14 + 6)//offsetY= imgSize 28 / 2 
        }
        if (markerPoint.toolTipText) {
            setTimeout(() => {
                this.genToolTip(point, markerPoint.toolTipText!, 14, '#333', '#fff', markerPoint.toolTipPos)
            });
        }
        if (!this.marks) this.marks = new rbush()
        const iconSize = markerPoint.imgSize
        const adj_x = iconSize[0] / 2
        const adj_y = iconSize[1] / 2
        const ret = [
            {
                minX: point.x - adj_x,
                minY: point.y - adj_y,
                maxX: point.x + adj_x,
                maxY: point.y + adj_y,
                data: markerPoint,
            },
            {
                minX: markerPoint.latlng.lng,
                minY: markerPoint.latlng.lat,
                maxX: markerPoint.latlng.lng,
                maxY: markerPoint.latlng.lat,
                data: markerPoint,
            },
        ]
        return ret
    }

    /**
     *
     * 绘制圆形
     * @param {L.LatLng} latlng
     * @param {number} radius
     * @param {number} type
     * @memberof WindpowerProtectCanvasLayer
     */
    _drawCircle(latlng: L.LatLng, radius: number, type: number) {
        const point = this._map.latLngToContainerPoint(latlng)
        const _radius = this._getLatRadius(radius, latlng)
        let ctx = this._context
        this._setCtxStyle(type)
        ctx.beginPath()
        ctx.arc(point.x, point.y, _radius, 0, 2 * Math.PI)
        ctx.closePath()
        ctx.fill()
        ctx.stroke()
    }

    /**
     *
     * 绘制线
     * @param {L.LatLng[]} latlngs
     * @param {string} name
     * @memberof WindpowerProtectCanvasLayer
     */
    _drawLine(latlngs: L.LatLng[], name?: string) {
        const points = this.transformLatlngsToPoints(latlngs);
        let ctx = this._context
        ctx.beginPath()
        ctx.globalAlpha = 1
        ctx.lineDashOffset = 0
        ctx.setLineDash([0, 0])
        const len = points.length
        ctx.strokeStyle = "#FD6666"
        ctx.lineWidth = 7.5
        for (let i = 0; i < len; i++) {
            const { x, y } = points[i]
            if (i == 0) {
                ctx.moveTo(x, y)
            } else if (i == len - 1) {
                ctx.lineTo(x, y);
                ctx.stroke()
            }
            else {
                ctx.lineTo(x, y)
            }
        }
        ctx.lineWidth = 4.5
        ctx.strokeStyle = "#2DB4FC"
        ctx.beginPath()
        for (let i = 0; i < len; i++) {
            const { x, y } = points[i]
            if (i == 0) {
                ctx.moveTo(x, y)
            } else if (i == len - 1) {
                ctx.lineTo(x, y);
                ctx.stroke()
            }
            else {
                ctx.lineTo(x, y)
            }
        }
        ctx.lineWidth = 1.5
        ctx.strokeStyle = "#FFED93"
        ctx.beginPath()
        for (let i = 0; i < len; i++) {
            const { x, y } = points[i]
            if (i == 0) {
                ctx.moveTo(x, y)
            } else if (i == len - 1) {
                ctx.lineTo(x, y);
                ctx.stroke()
            }
            else {
                ctx.lineTo(x, y)
            }
        }
        if (name) {
            let centerIndex = len % 2 == 0 ? len / 2 : (len - 1) / 2
            const center = points[centerIndex]
            this._setAreaName(center, name)
        }
    }

    /**
     * 绘制多边形
     * @param latlngs 
     * @param type 0 内 1 外
     * @param name 
     */
    private _drawPloygon(latlngs: L.LatLng[], type: number, name?: string, center?: L.Point) {
        const points = this.transformLatlngsToPoints(latlngs)
        const ctx = this._context
        this._setCtxStyle(type)
        const len = points.length
        ctx.beginPath()
        for (let i = 0; i < len; i++) {
            const { x, y } = points[i]
            if (i == 0) {
                ctx.moveTo(x, y)
            } else if (i == len - 1) {
                ctx.lineTo(x, y);
                ctx.closePath()
            } else {
                ctx.lineTo(x, y)
            }
        }
        ctx.fill()
        ctx.stroke()

        if (name && center) {
            // 设置保护区名称
            this._setAreaName(center, name)
        }
    }

    // 绘制图标
    private _drawImage(markPoint: MarkerPoint) {
        const ctx = this._context
        const point = this._map.latLngToContainerPoint(markPoint.latlng)
        const size = markPoint.imgSize
        const x = point.x - size[0] / 2,
            y = point.y - size[1] / 2
        if (!markPoint.canvasImg) {
            const canvasImg = new Image()
            canvasImg.src = markPoint.imgUrl
            markPoint.canvasImg = canvasImg
            canvasImg.onload = () => {
                ctx.drawImage(canvasImg, x, y, size[0], size[1])
            }
        } else {
            ctx.drawImage(markPoint.canvasImg, x, y, size[0], size[1])
        }
    }

    /**
    *
    * 设置画笔样式
    * @private
    * @param {number} type 0 内环 1 外环
    * @memberof WindpowerProtectCanvasLayer
    */
    private _setCtxStyle(type: number) {
        let ctx = this._context
        let color, fillColor
        if (type === 0) { // 内环 
            color = 'rgba(255,0,0,0.40)'//#4D9CDD
            fillColor = 'rgba(255,0,0,0.20)'//#3696E0
            ctx.setLineDash([12, 6])
        } else if (type == 1) { // 外环
            color = 'rgba(255,102,0,0.4)' //9B4FD3
            fillColor = 'rgba(255,102,0,0.15)'//9B4FD3
            ctx.setLineDash([12, 6])
        } else {
            color = '#FD6666 '
            fillColor = '#2DB4FC'
            ctx.setLineDash([0, 0])
        }
        ctx.globalAlpha = 1
        ctx.lineWidth = 2
        ctx.strokeStyle = color
        ctx.fillStyle = fillColor
    }

    /**
     *
     * 通过坐标点数组获取绝对中心点
     * @private
     * @param {Array<L.LatLng>} latlngs
     * @return {*}  {L.Point}
     * @memberof WindpowerProtectCanvasLayer
     */
    private _getGeoJSONCenter(latlngs: Array<L.LatLng>): L.Point | undefined {
        if (latlngs.length) {
            const features = turf.featureCollection(
                latlngs.map((ele) => turf.point([ele.lat, ele.lng]))
            )
            const center = turf.center(features).geometry.coordinates
            return this._map.latLngToContainerPoint(L.latLng(center[0], center[1]))
        }
        return undefined
    }

    /**
     *
     * 设置保护区名称
     * @private
     * @param {L.Point} point
     * @param {string} areaName
     * @return {*} 
     * @memberof WindpowerProtectCanvasLayer
     */
    private _setAreaName(point: L.Point, areaName: string, minZoom = 10, offsetY = 0) {
        const zoom = this._map.getZoom()
        if (!areaName || zoom < minZoom) return
        const ctx = this._context
        this._setCtxStyle(0)
        ctx.beginPath()
        ctx.font = '14px "微软雅黑"'
        ctx.textAlign = 'center'
        ctx.fillStyle = '#fff'
        ctx.textBaseline = 'middle'
        ctx.globalAlpha = 1
        ctx.fillText(areaName, point.x, point.y + offsetY)
        ctx.stroke()
    }

    /**
     *
     * 绘制tooltip
     * @private
     * @param {L.Point} point
     * @param {string} text
     * @param {number} fontsize
     * @param {string} fontcolor
     * @param {string} bgcolor
     * @memberof WindpowerProtectCanvasLayer
     */
    private genToolTip(point: L.Point, text: string, fontsize: number, fontcolor: string, bgcolor: string, pos: toolTipPosition = 'topright') {
        const zoom = this._map.getZoom()
        if (!text || zoom < 15) return
        const context = this._context
        context.font = `${fontsize}px "微软雅黑"`;
        context.textBaseline = 'middle';
        context.fillStyle = fontcolor;
        //画框子
        const width = context.measureText(text).width
        const boundWidth = width + 10
        this.fillRoundRect(point.x, point.y, boundWidth, 24, bgcolor, pos);
        if (pos == 'topright') {
            // 右上角 航标（灯桩）
            context.fillText(text, point.x + boundWidth / 2.0 + 12, point.y - fontsize / 2.0 - 3);
        } else if (pos == 'topleft') {
            // 左上角 风机
            context.fillText(text, point.x - boundWidth / 2.0 - 12, point.y - fontsize / 2.0 - 3);
        } else if (pos == 'bottomright') {
            // 右下角 航标（其他类型）
            context.fillText(text, point.x + boundWidth / 2.0 + 12, point.y + fontsize);
        } else if (pos == 'bottomleft') {
            // 左下角(未使用)
            context.fillText(text, point.x - boundWidth / 2.0 - 12, point.y + fontsize);
        }
    }

    private fillRoundRect(x: number, y: number, width: number, height: number, fillColor: string, type: toolTipPosition) {
        const cxt = this._context
        cxt.save();
        if (type == 'topright') { // 右上角
            cxt.translate(x + 12, y - height);
        } else if (type == 'topleft') { // 左上角
            cxt.translate(x - width - 12, y - height);
        } else if (type == 'bottomright') { // 右下角
            cxt.translate(x + 12, y);
        } else if (type == 'bottomleft') { // 左下角
            cxt.translate(x - width - 12, y);
        }
        //绘制圆角矩形的各个边
        this.drawRoundRectPath(width, height, type);
        cxt.fillStyle = fillColor || "#000"; //若是给定了值就用给定的值否则给予默认值
        cxt.fill();
        cxt.restore();
    }
    private drawRoundRectPath(width: number, height: number, type: toolTipPosition) {
        const cxt = this._context
        cxt.beginPath();
        const radius = 2
        //从右下角顺时针绘制，弧度从0到1/2PI
        cxt.arc(width - radius, height - radius, radius, 0, Math.PI / 2);
        //矩形下边线
        cxt.lineTo(width - radius, height);
        //左下角圆弧，弧度从1/2PI到PI
        cxt.arc(radius, height - radius, radius, Math.PI / 2, Math.PI);
        //绘制提示框尖角(尖角在左边 )
        if (type == 'topright' || type == 'bottomright') {
            cxt.lineTo(0, height / 2.0 + 5);
            cxt.lineTo(-6, height / 2.0);
            cxt.lineTo(0, height / 2.0 - 5);
        }
        // //矩形左边线
        cxt.lineTo(0, radius);
        //左上角圆弧，弧度从PI到3/2PI
        cxt.arc(radius, radius, radius, Math.PI, Math.PI * 3 / 2);
        //上边线
        cxt.lineTo(width - radius, 0);
        //右上角圆弧
        cxt.arc(width - radius, radius, radius, Math.PI * 3 / 2, Math.PI * 2);
        //绘制提示框尖角 尖角在右边
        if (type == 'topleft' || type == 'bottomleft') {
            cxt.lineTo(width, height / 2.0 + 5);
            cxt.lineTo(width + 6, height / 2.0);
            cxt.lineTo(width, height / 2.0 - 5);
        }
        //右边线
        cxt.lineTo(width, height - radius);
        cxt.closePath();
    }

    /**
     *
     * 清空画布
     * @private
     * @return {*}  {boolean}
     * @memberof WindpowerProtectCanvasLayer
     */
    private _clearContext(): boolean {
        let map = this._map
        if (L.Browser.canvas && map) {
            let ctx = this._context,
                ww = this._canvas.width,
                hh = this._canvas.height
            ctx.clearRect(0, 0, ww, hh) // 清空画布
            return true
        }
        return false
    }

    private _animateZoom(e: any) {
        let map: any = this._map
        var scale = map.getZoomScale(e.zoom),
            offset = map
                ._getCenterOffset(e.center)
                ._multiplyBy(-scale)
                .subtract(map._getMapPanePos())
        L.DomUtil.setTransform(this._canvas, offset, scale)
    }

    /**
    *
    * 将坐标点数组转为point数组
    * @private
    * @param {string} coordinateStr
    * @return {*}  {Array<L.Point>}
    * @memberof WindpowerProtectCanvasLayer
    */
    private transformLatlngsToPoints(latlngs: Array<L.LatLng>): Array<L.Point> {
        return latlngs.map((ele) =>
            this._map.latLngToContainerPoint(ele)
        )
    }

    /**
    *
    * 根据坐标点计算 canvas 上的对应的point 点
    * @private
    * @param {number} radius
    * @param {L.LatLng} latlng
    * @return {*} 
    * @memberof WindpowerProtectCanvasLayer
    */
    private _getLatRadius(radius: number, latlng: L.LatLng) {
        const lng = latlng.lng,
            lat = latlng.lat,
            map = this._map,
            crs = map.options.crs!,
            Earth: any = L.CRS.Earth
        let _radius = 0
        if (crs.distance === Earth.distance) {
            var d = Math.PI / 180,
                latR = radius / Earth.R / d,
                top = map.project([lat + latR, lng]),
                bottom = map.project([lat - latR, lng]),
                p = top.add(bottom).divideBy(2),
                lat2 = map.unproject(p).lat,
                lngR =
                    Math.acos(
                        (Math.cos(latR * d) - Math.sin(lat * d) * Math.sin(lat2 * d)) /
                        (Math.cos(lat * d) * Math.cos(lat2 * d))
                    ) / d

            if (isNaN(lngR) || lngR === 0) {
                lngR = latR / Math.cos((Math.PI / 180) * lat) // Fallback for edge case, #2425
            }
            const _point = p.subtract(map.getPixelOrigin())
            _radius = isNaN(lngR) ? 0 : p.x - map.project([lat2, lng - lngR]).x
            const _radiusY = p.y - top.y
        } else {
            const latlng2 = crs.unproject(crs.project(latlng).subtract([radius, 0]))
            const _point = this._map.latLngToLayerPoint(latlng)
            _radius = _point.x - this._map.latLngToLayerPoint(latlng2).x
            // console.log('_radius', _radius)
        }
        return _radius
    }
}