import { Component, OnIni<PERSON>, OnDestroy, Input } from '@angular/core';
import * as L from 'leaflet';
import { Subscription } from 'rxjs';
import { DynamicComponent, DynamicComponentData } from 'src/app/shared/models';
import { Principal } from 'src/app/shared/services/principal.service';
import { TblProarea } from 'src/app/workspace/workspace-shared/models/tbl_proarea';
import { MapLayerComponentService } from '../../../service/map-layer-component.service';
import { MapStateService } from '../../../service/map-state.service';
import { ProtectHanldeStatus } from '../windpower-protect.model';
import { WindpowerProtectService } from '../windpower-protect.service';
import { WindpowerProtectCanvasLayer } from './windpower-protect-canvas-layer';

@Component({
  selector: 'app-windpower-protect-layer',
  template: ``
})
export class WindpowerProtectLayerComponent implements OnIni<PERSON>, On<PERSON><PERSON>roy {
  @Input() data: DynamicComponentData = {}
  map: L.Map
  protectCanvasLayer: WindpowerProtectCanvasLayer
  list: Array<TblProarea> = []
  sub: Subscription
  // 从图层中打开详细、编辑组件的订阅
  componentChangeSub: Subscription
  selectedIcon: L.Icon
  selectedMarkerGroup: L.FeatureGroup

  constructor(
    private mapState: MapStateService,
    private service: WindpowerProtectService,
    private mapLayerService: MapLayerComponentService,
    private principal: Principal
  ) {
    this.map = this.mapState.getMapInstance()
    this.protectCanvasLayer = new WindpowerProtectCanvasLayer().addTo(this.map)
    this.selectedIcon = L.icon({
      iconUrl: '/assets/img/map/protect/info-selected.png',
      iconSize: [32, 32],
    })
    this.selectedMarkerGroup = L.featureGroup().addTo(this.map)

    this.sub = this.service.protectCoverageChange$.subscribe(res => {
      // 航标永远最后绘制，避免用户操作图层时打乱顺序，导致风机遮盖了航标
      const index = res.findIndex(ele => ele.type == 'nav')
      if (index > -1) {
        const item = res.splice(index, 1)[0]
        res.push(item)
      }
      this.protectCanvasLayer.redrawProtectLayer(res)
    })
    // 订阅打开的popup  并添加selected icon 图标
    this.componentChangeSub = this.service.componentChange$.subscribe(res => {
      this.selectedMarkerGroup.clearLayers()
      if (res.list && res.list.length) {
        res.list.forEach((ele: ProtectHanldeStatus) => {
          this.addSelectedIcon(ele)
        })
      }
    })
  }

  ngOnInit(): void {
    this.getList()
  }
  ngOnDestroy(): void {
    this.selectedMarkerGroup.remove()
    if (this.protectCanvasLayer) this.protectCanvasLayer.remove()
    if (this.sub) this.sub.unsubscribe()
    if (this.componentChangeSub) this.componentChangeSub.unsubscribe()
  }

  /**
   * 添加selected icon
   * 只考虑单个坐标的风机和航标
   * @param item 
   */
  private addSelectedIcon(item: ProtectHanldeStatus) {
    if (item.selectedIconLatlng && (item.componentType == 'navigation' || item.componentType == 'fan')) {
      const marker = L.marker(item.selectedIconLatlng, { icon: this.selectedIcon }).addTo(this.map)
      this.selectedMarkerGroup.addLayer(marker)
    }
  }

  private getList() {
    this.service.getAllList().then(res => {
      this.list = res
      this.protectCanvasLayer.addProtectLayer(this.list)
      this.protectCanvasLayer.addOnClickListener((event: any, ret: any[]) => {
        // const isCover = ret.length > 1 // 风机和航标覆盖的情况
        let dynamic: DynamicComponent = {}
        for (let i = 0; i < ret.length; i++) {
          const ele = ret[i]
          const data = ele.data
          // 如果没有查看权限，不打开详细popup
          if (!this.principal.hasAuthority('1.1.4')) return
          if (data.type == 'nav') {
            const state: ProtectHanldeStatus = { id: data.infoData.id, selectedIconLatlng: data.latlng, type: 'info', componentType: 'navigation' }
            dynamic = {
              title: `${data.infoData && data.infoData.name}（详细）`,
              titleType: 'primary',
              name: 'ProtectNavMarkInfoComponent',
              type: 'popup',
              data: {
                position: {
                  latlng: data.latlng,
                  offset: [0, 30]
                },
                params: {
                  infoData: [data.infoData]
                }
              },
              onOpenCb: () => {
                // 通知列表页面打开了航标详情，将航标的详细图标激活
                this.service.addComponent(state)
              },
              closeCb: () => {
                // 通知列表页面打开了关闭了航标详情，将航标的详细图标置灰
                this.service.removeComponent(state)
              }
            }
            // 优先打开航标详情
            break;
          }
          if (data.type == 'fan') { // 风机 单个详情
            const state: ProtectHanldeStatus = { id: data.infoData.id, selectedIconLatlng: data.latlng, type: 'info', componentType: 'fan' }
            dynamic = {
              title: `${data.infoData && data.infoData.name}（详细）`,
              name: 'ProtectWindMotorInfoComponent',
              type: 'popup',
              titleType: 'primary',
              data: {
                position: {
                  latlng: data.latlng,
                  // offset: isCover ? [0, 138] : [0, 30]
                  offset: [0, 30]// 优先显示航标，不存在覆盖
                },
                params: {
                  infoData: [data.infoData]
                }
              },
              onOpenCb: () => {
                // 通知列表页面打开了风机详情，将航标的详细图标激活
                this.service.addComponent(state)
              },
              closeCb: () => {
                // 通知列表页面打开了风机了航标详情，将航标的详细图标置灰
                this.service.removeComponent(state)
              }
            }
          }
        }
        if (dynamic.name)
          this.mapLayerService.addComponent(dynamic)
      })
    })
  }
}
