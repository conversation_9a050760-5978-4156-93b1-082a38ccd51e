import {
  Component,
  ElementRef,
  HostListener,
  OnInit,
  Renderer2,
  ViewChild,
} from '@angular/core';
import * as _ from 'lodash';
import { MapLayerComponentService } from '../../../service/map-layer-component.service';
import { WindpowerProtectService } from '../../windpower-protect/windpower-protect.service';
import { WindpowerShipService } from '../../windpower-ship/windpower-ship.service';
import { MapConverage, MAP_CONVERAGE_LAYERS } from './map-converage.data';

@Component({
  selector: 'app-map-coverage',
  templateUrl: './map-coverage.component.html',
  styleUrls: ['./map-coverage.component.less'],
})
export class MapCoverageComponent implements OnInit {
  coverageLayerList: Array<MapConverage>;
  // 报警列表高度
  alarmListHeight: number = 0;
  // private alarmSubscription: Subscription;
  @ViewChild('converageContainer')
  converageContainer!: ElementRef<HTMLDivElement>;
  @ViewChild('converageContent') converageContent!: ElementRef<HTMLDivElement>;
  private initHeight: number = 0;

  constructor(
    private mapLayerService: MapLayerComponentService,
    private render: Renderer2,
    private windpowerShipService: WindpowerShipService,
    private protectedService: WindpowerProtectService
  ) {
    this.coverageLayerList = _.cloneDeep(MAP_CONVERAGE_LAYERS);
  }
  ngOnDestroy(): void {
  }
  ngAfterViewInit(): void {

  }

  ngOnInit(): void { }
  /**
   *
   * 船舶图层 change 事件
   * @memberof MapCoverageComponent
   */
  layerChildChange(item: MapConverage, subItem: MapConverage) {
    subItem.selected = !subItem.selected;
    item.selected = item.children?.some((ele) => ele.selected);
    this.layerSelectedChange(item);
  }

  /**
   * 父级checkbox change 事件
   * @param event
   * @param item
   * @returns
   */
  layerParentChange(event: boolean, item: MapConverage) {
    item.selected = event;
    item.children &&
      item.children.map((ele) => {
        ele.selected = item.selected;
      });
    this.layerSelectedChange(item);
  }

  /**
   *  图层选中 change 事件
   * @param item
   * @returns
   */
  private layerSelectedChange(item: MapConverage) {
    const dynamicComponent = item.dynamicComponent;
    if (!dynamicComponent) return;
    const name = dynamicComponent.name;
    if (name === 'WindpowerShipLayerComponent') {
      // 通知船舶图层
      const values = this.computedShipType(item);
      this.windpowerShipService.notyfyShipChanges(values);
    } else if (name === 'WindpowerProtectLayerComponent') {
      const selectedList = this.computedProtectType();
      this.protectedService.notyfyProtectTypeChange(selectedList);
    } else if (name === 'WindpowerAccidentLayerComponent') {
      item.selected
        ? this.mapLayerService.addComponent(item.dynamicComponent!)
        : this.mapLayerService.removeComponent({
          name: 'WindpowerAccidentLayerComponent',
        });
    }
  }

  /**
   * 计算选中的船舶类型
   * @param item
   * @returns
   */
  private computedShipType(item: MapConverage) {
    const childrenValues: Array<string> = [];
    item.children?.forEach((ele) => {
      if (ele.selected === true) {
        childrenValues.push(ele.value!);
      }
    });
    return childrenValues;
  }

  /**
   * 计算选中的保护区图层
   */
  private computedProtectType() {
    const selectedList: Array<any> = [];
    const protectLayers = this.coverageLayerList.filter(
      (ele) =>
        ele.dynamicComponent &&
        ele.dynamicComponent.name == 'WindpowerProtectLayerComponent'
    );
    protectLayers.forEach((ele) => {
      // 如果是航标类型，计算children 选中状态
      if (!ele.selected) return;
      if (ele.protectType == 'nav') {
        const childrenValues = ele.children
          ?.filter((ele) => ele.selected)
          ?.map((ele) => ele.value!);
        if (childrenValues)
          selectedList.push({ type: ele.protectType, values: childrenValues });
      } else if (ele.protectType == 'protect') {
        //  保护区:包含 电缆和 电子围栏
        ele.children?.forEach((e) => {
          if (e.selected) {
            selectedList.push({ type: e.protectType });
          }
        });
      } else {
        //风机
        selectedList.push({ type: ele.protectType });
      }
    });
    return selectedList;
  }

  @HostListener('window:resize')
  resize() {
    // this.computeHeight();
  }
  private computeHeight() {
    setTimeout(() => {
      const contentH = this.converageContainer.nativeElement.clientHeight;
      if (contentH == 0) return;
      const documentH = document.body.clientHeight;
      // 50 header -- top 8px --ul 48px -- top 2px
      // title 36px -- alarm icon 32
      let usableHeight =
        documentH - this.alarmListHeight - 50 - 8 - 48 - 2 - 36 - 32;
      let h = this.initHeight > contentH ? this.initHeight : contentH;
      if (usableHeight > h) {
        usableHeight = h;
      }
      this.render.setStyle(
        this.converageContainer.nativeElement,
        'height',
        usableHeight + 'px'
      );
    });
  }
}
