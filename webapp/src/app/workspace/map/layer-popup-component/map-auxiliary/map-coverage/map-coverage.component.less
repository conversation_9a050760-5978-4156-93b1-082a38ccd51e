@import "/src/styles/mixins";
.map-converage-container {
  width: 284px;
  background: transparent;
  .scrollbars(5px, #C8C9CC, rgba(0, 0, 0, 0));
  overflow: hidden;
  .map-converage-container__inner {
    overflow-y: auto;
    overflow-x: hidden;
    height: 100%;
    .item {
      .item__title {
        height: 30px;
        background: rgba(120, 180, 255, 0.09);
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 10px;
        margin-bottom: 15px;
        position: relative;
        h4 {
          color: #333;
          margin-bottom: 0;
        }
        label {
          position: absolute;
          top: 5px;
          right: 10px;
        }
      }
      .item__content {
        display: flex;
        flex-wrap: wrap;
        .content_li {
          margin-left: 15px;
          margin-bottom: 12px;
          border-radius: 2px;
        }
        .content_li > a {
          display: flex;
          position: relative;
          min-width: 72px;
          height: 28px;
          padding: 0 10px;
          align-items: center;
          justify-content: center;
          background: #f3f4f5;
          color: #333;
          box-sizing: border-box;
          span {
            margin-left: 4px;
          }
        }
        .content_li.active > a {
          background: #d1e2ff;
          color: #2c3d5a;
          i {
            background-color: #cfe0fa;
            padding: 2px;
          }
        }
      }
    }
  }
}
