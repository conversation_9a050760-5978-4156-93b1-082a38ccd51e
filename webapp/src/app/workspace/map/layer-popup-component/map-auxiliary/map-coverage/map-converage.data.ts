import { DynamicComponent } from "src/app/shared/models";

export class MapConverage {
    label: string = '';
    icon?: string = '';
    protectType?: 'crawl' | 'cable' | 'nav' | 'fan' | 'protect' = 'crawl'; // 类型
    selected?: boolean = false; // 节点选中状态
    value?: string; // 种类识别 根据数据库字段
    children?: MapConverage[];
    dynamicComponent?: DynamicComponent
}
export const MAP_CONVERAGE_LAYERS: Array<MapConverage> = [
    {
        label: '保护区',
        selected: true,
        dynamicComponent: { name: 'WindpowerProtectLayerComponent', type: 'layer' },
        protectType: 'protect',
        children: [
            {
                label: '电子围栏',
                icon: 'crawl',
                protectType: 'crawl',
                selected: true
            },
            {
                label: '海底电缆',
                icon: 'cable',
                protectType: 'cable',
                selected: true
            }
        ]
    },
    {
        label: '航标',
        selected: true,
        dynamicComponent: { name: 'WindpowerProtectLayerComponent', type: 'layer' },
        protectType: 'nav',
        children: [
            {
                label: '灯桩',
                icon: 'light-beacon3',
                selected: true,
                value: '0101'
            },
            {
                label: '灯浮',
                icon: 'light-buoy',
                selected: true,
                value: '0102'
            },
            {
                label: '实体AIS',
                icon: 'mark',
                selected: true,
                value: '0104'
            },
            {
                label: '虚拟AIS',
                icon: 'virtual-mark',
                selected: true,
                value: '0105'
            },
            {
                label: '登录牌标志',
                icon: 'landing-card',
                selected: true,
                value: '0103'
            }
        ]
    },
    {
        label: '船舶类型',
        selected: true,
        dynamicComponent: { name: 'WindpowerShipLayerComponent', type: 'layer' },
        children: [
            {
                label: '货船',
                icon: 'cargo-ship',
                selected: true,
                value: '1'
            },
            {
                label: '油轮',
                icon: 'oil-ship',
                selected: true,
                value: '2'
            },
            {
                label: '客船',
                icon: 'passenger-ship',
                selected: true,
                value: '3'
            },
            {
                label: '高速船',
                icon: 'high-speed-ship',
                selected: true,
                value: '4'
            },
            {
                label: '渔船',
                icon: 'fisher-ship',
                selected: true,
                value: '5'
            },
            {
                label: '游艇',
                icon: 'yacht-ship',
                selected: true,
                value: '6'
            },
            {
                label: '拖船/特种船',
                icon: 'special-ship',
                selected: true,
                value: '7'
            },
            {
                label: '其他',
                icon: 'other-ship',
                selected: true,
                value: '8'
            }
        ]
    },
    {
        label: '风机',
        selected: true,
        dynamicComponent: { name: 'WindpowerProtectLayerComponent', type: 'layer' },
        protectType: 'fan',
    },
    {
        label: '事故',
        selected: true,
        dynamicComponent: { name: 'WindpowerAccidentLayerComponent', type: 'layer' }
    },
]