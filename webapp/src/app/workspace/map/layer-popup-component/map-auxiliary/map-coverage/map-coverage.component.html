<div class="map-converage-container" #converageContainer>
    <div class="map-converage-container__inner" #converageContent>
        <ng-container *ngFor="let item of coverageLayerList;index as i">
            <div class="item">
                <div class="item__title">
                    <h4>{{item.label}}</h4>
                    <label nz-checkbox (ngModelChange)="layerParentChange($event,item)"
                        [(ngModel)]="item.selected"></label>
                </div>
                <div class="item__content" *ngIf="item.children && item.children.length">
                    <ng-container *ngFor="let subItem of item.children">
                        <div class="content_li" [class.active]="subItem.selected">
                            <a href="javascript:void(0)" (click)="layerChildChange(item,subItem)">
                                <i class="sl-{{subItem.icon}}-16"></i>
                                <span>{{subItem.label}}</span>
                            </a>
                        </div>
                    </ng-container>
                </div>
            </div>
        </ng-container>
    </div>
</div>