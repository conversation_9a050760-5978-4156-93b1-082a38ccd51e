import { debounceTime, filter } from 'rxjs/operators';
import { Component, Input, OnInit, OnDestroy } from '@angular/core';
// import { MOCK_DATA } from './mock-data';

import { Subscription } from 'rxjs';

import * as L from 'leaflet';
import { MapStateService } from '../../service/map-state.service';
import { DynamicComponentData } from 'src/app/shared/models';
import { SlLeafletCanvasMarkerRetItem, SlLeafletCanvasMarkers } from '../../utils';
import { TblNavigationstation } from './station.model';
import { StationService } from './station.service';

@Component({
  selector: 'app-navigation-aid-layer',
  template: ``,
})
export class NavigationAidLayerComponent implements OnInit, OnDestroy {
  @Input() data: DynamicComponentData = new DynamicComponentData();
  private map: L.Map;
  private navigationAidLayer2: SlLeafletCanvasMarkers; // 助航设备图层
  private sub: Subscription = new Subscription();
  // 已关注的航标
  // private shownFocusMarks: TblNavigationstation[] = [];
  // 需隐藏的已关注航标id
  private hiddenFocusIds: string[] = [];
  // 是否只显示已经关注的虚拟航标（助航设备）
  private ifFocus: boolean = false;
  private mapStateSub: Subscription;
  private viewBounds: L.LatLngBounds;
  // private areaSearch?: ShipAisAreaSearch;
  constructor(
    // private service: NavigationAidService,
    private service: StationService,
    private mapState: MapStateService,
    // private aisLinkTreeHandleService: AisLinkTreeHandleService
  ) {
    this.map = this.mapState.getMapInstance();
    this.navigationAidLayer2 = new SlLeafletCanvasMarkers().addTo(this.map);
    this.navigationAidLayer2.addOnClickListener(this.clickListener);
    this.viewBounds = this.map.getBounds();
    // 海图viewBounds
    this.mapStateSub = this.mapState.mapOptChange
      .pipe(
        filter((res) => res && res.viewBounds != undefined),
        debounceTime(500)
      )
      .subscribe(({ viewBounds }) => {
        this.viewBounds = viewBounds!;
        // this.getAreaVirtualMarks();
      });
    // // 关注的航标
    // this.sub.add(
    //   this.service.focusChanage.pipe(debounceTime(500)).subscribe((res) => {
    //     this.getFollowNavAids(res);
    //   })
    // );
    // // 搜索区域
    // this.sub.add(
    //   this.service.boundsChanges.subscribe((areaSearch) => {
    //     this.areaSearch = areaSearch;
    //     this.getAreaVirtualMarks();
    //   })
    // );
  }
  ngOnDestroy(): void {
    this.sub.unsubscribe();
    this.mapStateSub.unsubscribe();
    this.navigationAidLayer2.remove();
  }
  ngOnInit(): void {
    this.getAreaVirtualMarks();
  }

  /**
   * 获取所有的虚拟航标
   */
  private getAreaVirtualMarks() {
    // 只显示关注的船舶
    // if (this.ifFocus) {
    //   this.genNavigationAidLayer(this.shownFocusMarks);
    // } else {
    //   let areaSearch = this.areaSearch;
    //   // 不存在自定义区域的情况，默认使用map 的最大边界坐标
    //   if (!areaSearch) {
    //     areaSearch = new ShipAisAreaSearch();
    //     const bounds = this.viewBounds;
    //     areaSearch.lat = bounds.getNorthEast().lat;
    //     areaSearch.lat1 = bounds.getSouthWest().lat;
    //     areaSearch.lng = bounds.getNorthEast().lng;
    //     areaSearch.lng1 = bounds.getSouthWest().lng;
    //   }
    //   // this.service.getAllNavigationstation(areaSearch).then((res) => {
    //   //   this.genNavigationAidLayer(res);
    //   // });
    // }

    this.service.getAllVirtualatons().then(res => {
      this.genNavigationAidLayer(res);
    })
  }

  private genNavigationAidLayer(navAids: TblNavigationstation[]) {
    this.removeCanvasMarkers();
    // // 过滤需要隐藏的虚拟航标
    // if (this.hiddenFocusIds.length) {
    //   navAids = navAids.filter(
    //     (item) => this.hiddenFocusIds.findIndex((ele) => item.id == ele) == -1
    //   );
    // }
    const markers = this.genNaiMarker(navAids);
    this.navigationAidLayer2.addLayers(markers);
  }

  /**
   * 点击虚拟航，打开aislink 底部popup
   * @param event
   * @param ret
   */
  private clickListener = (
    event: L.LeafletMouseEvent,
    ret: SlLeafletCanvasMarkerRetItem[]
  ) => {
    const marker = ret[0].marker;
    if (marker) {
      const navAid = marker['navAid'];

      // const node: AisLinkTreeNode = {
      //   label: navAid.aidsNameEn,
      //   orgTypeCode: '06', //虚拟航标
      //   type: 'mark',
      //   id: navAid.id,
      //   ifOldData: navAid.ifOldData,
      // };
      // this.aisLinkTreeHandleService.openBottomInfoPopup(node, true);
    }
  };

  /**
   * 航标marker
   * @param navAids
   * @returns
   */
  private genNaiMarker(navAids: TblNavigationstation[]) {
    const markers = [];
    for (const item of navAids) {
      // 坐标点
      const latlng: L.LatLng = L.latLng(item.latitude!, item.longitude!);
      // icon
      const iconOpt: L.IconOptions = {
        iconUrl: '/assets/img/map/navigation/virtual.png',
        iconSize: [24, 24],
        iconAnchor: [12, 12],
      };
      const icon = L.icon(iconOpt);
      const marker: L.Marker = L.marker(latlng, {
        icon,
        autoPan: false,
        zIndexOffset: 205,
      });
      marker['navAid'] = item;
      marker['title'] = item.aidsNameEn;
      marker['textHeight'] = 22;
      marker['latlng'] = latlng;
      markers.push(marker);
    }
    return markers;
  }

  private removeCanvasMarkers() {
    if (this.navigationAidLayer2) this.navigationAidLayer2.clearLayers();
  }
}
