import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { stopBubble, stopDefault } from 'src/app/shared/utils';
import { StationTreeNode } from '../station.model';
import * as moment from 'moment';

@Component({
  selector: 'app-station-list-item',
  templateUrl: './station-list-item.component.html',
  styleUrls: ['./station-list-item.component.less']
})
export class StationListItemComponent implements OnInit {

  @Input() item!: StationTreeNode

  @Input() level: number = 0


  @Output() onClickItem: EventEmitter<any> = new EventEmitter()

  @Output() onEditItem: EventEmitter<any> = new EventEmitter()

  @Output() onInfoItem: EventEmitter<any> = new EventEmitter()

  
  constructor() { }

  ngOnInit(): void {
  }

  clickItem(event: MouseEvent) {
    const selNode = this.getSelectedNode(event)
    this.onClickItem.emit(selNode);
  }

  edit(event: MouseEvent) {
    const selNode = this.getSelectedNode(event)
    this.onEditItem.emit(selNode);
  }

  toInfo(event: MouseEvent) {
    const selNode = this.getSelectedNode(event)
    this.onInfoItem.emit(selNode);
  }
  
  private getSelectedNode(event: MouseEvent) {
    let selNode: any;
    if (event instanceof MouseEvent) {
      stopBubble(event)
      stopDefault(event)
      selNode = this.item
    } else {
      selNode = event
    }
    return selNode
  }
  /**10分钟内基站正常 */
  isStationNormal(updateTime?: string) {
      return updateTime && moment(new Date()).diff(moment(updateTime), 'minutes') <= 10;
  }
}
