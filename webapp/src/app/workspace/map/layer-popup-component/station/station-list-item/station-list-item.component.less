li {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 32px;
  &.station {
    a > span {
      width: 100%;
    }
    .update-time {
      color: rgb(75, 208, 75);
      &.abnormal {
        color: rgb(235, 32, 32)
      }
    }
  }
  a,
  .btn {
    display: inline-flex;
    align-items: center;
  }
  a > i {
    margin-right: 4px;
  }
  a > span {
    color: #333;
    color: #333;
    overflow: hidden;
    white-space: nowrap;
    width: 210px;
    text-overflow: ellipsis;
  }
  .btn {
    a + a {
      margin-left: 8px;
    }
  }
  &.has-bg,
  &:hover {
    background: #f5f8fe;
  }
}
.ul-sublist {
  display: none;
  &.expanded {
    display: block;
  }
}
