<!--[class.has-bg]="item.index % 2 ==1"-->
<li class="item" [class.station]="item.type=='station'" [style.paddingLeft.px]="level*12">
    <a href="javascript:void(0)" (click)="clickItem($event)">
        <i class="sl-{{item.icon}}-16" *ngIf="item.icon"></i>
        <i *ngIf="item.children&&!item.spining" class="sl-caret-{{item.expanded?'down':'right'}}-16"
            [class.expanded]="item.expanded"></i>
        <i nz-icon *ngIf="item.spining" nzType="loading" nzTheme="outline"></i>
        <span>
            {{ item.label }}
            <span class="update-time" *ngIf="item.type=='station';else notStationBlock" [class.abnormal]="!isStationNormal(item.sysUpdated)">{{item.sysUpdated ? '（'+item.sysUpdated+'）' : ''}}</span>
            <ng-template #notStationBlock>
                <span *ngIf="item.children"> （{{item.children.length }}）</span>
            </ng-template>
        </span>
    </a>
    <div class="btn" *ngIf="item.type">
        <!-- <a href="javascript:void(0)" title="编辑" *slHasAnyAuthority="'1.1.2'" (click)="edit($event)">
            <i class="sl-edit{{item.editSelected?'-active':''}}-16"></i>
        </a> -->
        <!-- *slHasAnyAuthority="'1.1.4'" -->
        <!-- 项目 编辑 -->
        <ng-container *ngIf="item.type=='project'">
            <a href="javascript:void(0)" [attr.title]="'编辑'" (click)="edit($event)">
                <i class="sl-edit{{item.infoSelected?'-active':''}}-16"></i>
            </a>
            <a href="javascript:void(0)" [attr.title]="'详情'" (click)="toInfo($event)">
                <i class="sl-info{{item.infoSelected?'-active':''}}-16"></i>
            </a>
        </ng-container>
        <!-- 基站 船舶 航标 -->
        <a *ngIf="item.type !== 'project'" href="javascript:void(0)" [attr.title]="item.type=='ship' || item.type == 'station'?'详情':'播发记录'" (click)="toInfo($event)">
            <i class="sl-info{{item.infoSelected?'-active':''}}-16"></i>
        </a>
    </div>
</li>
<ng-container *ngIf="item.children?.length">
    <ul class="ul-sublist" [class.expanded]="item.expanded">
        <ng-container *ngFor="let subItem of item.children;index as i">
            <app-station-list-item [item]="subItem" [level]="level+1" (onInfoItem)="toInfo($event)"
                (onEditItem)="edit($event)" (onClickItem)="clickItem($event)">
            </app-station-list-item>
        </ng-container>
    </ul>
</ng-container>