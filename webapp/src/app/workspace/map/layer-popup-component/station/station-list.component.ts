import { Component, EventEmitter, Input, OnInit, Output, SimpleChanges } from '@angular/core';
import * as L from 'leaflet';
import * as _ from 'lodash';
import * as turf from '@turf/turf'
import { Subject, Subscription, interval } from 'rxjs';
import { DynamicComponent } from 'src/app/shared/models';
import { MapLayerComponentService } from '../../service/map-layer-component.service';
import { MapStateService } from '../../service/map-state.service';
import { Station, StationSearch, StationTreeNode } from './station.model';
import { StationService } from './station.service';
import { WindpowerShipService } from '../windpower-ship/windpower-ship.service';
import { debounceTime, distinctUntilChanged, takeUntil } from 'rxjs/operators';
import { isNotEmpty } from 'src/app/shared/utils';
import { TestProjectInfo } from '../station-test-project/station-test.model';
import { StationTestProjectService } from '../station-test-project/station-test-project.service';

@Component({
  selector: 'app-station-list',
  templateUrl: './station-list.component.html',
  styleUrls: ['./station-list.component.less']
})
export class StationListComponent implements OnInit {
  /**所有基站列表 */
  @Input() inAll: boolean = true 
  /**当前项目 */
  @Input() inProject?: TestProjectInfo
  @Output() onEvent: EventEmitter<{type: 'edit' | 'info', data: StationTreeNode}> = new EventEmitter();
  private searchSub$: Subject<string> = new Subject()
  private destroy$ = new Subject()
  spinning: boolean = false
  map: L.Map
  // 搜索实体
  search: StationSearch = new StationSearch()
  // 数据索引（列表没有使用斑马条纹，可以废弃）
  dataIndex: number = 0
  // 保护区子级数据
  dataList: Array<any> = [
    { type: 'crawl', icon: 'crawl', label: '电子围栏', searchLevel: '2', listPro: 'crawlList', children: [] },
    { type: 'cable', icon: 'cable', label: '海底电缆', searchLevel: '3', listPro: 'cableList', children: [] },
    { type: 'navigation', label: '航标', searchLevel: '4', listPro: 'navigationList', children: [] },
    { type: 'fan', icon: 'fan', label: '风机', searchLevel: '5', listPro: 'fanList', children: [] },
  ]
  // private navList: Array<{ type: string, name: string, list: Array<NavMarkProtect>, icon: string }> = []
  // 树形数组
  list: Array<StationTreeNode> = []
  _cachedList: Array<StationTreeNode> = []
  // 从图层中打开详细组件的订阅
  // currentICChangeSub: Subscription
  // 从图层中打开详细、编辑组件的订阅
  componentChangeSub: Subscription = new Subscription()
  // 所有的动态组件
  componentList: Array<{ index: number, type: string, singleComponents: Array<DynamicComponent> }> = [
    {
      index: 0,
      type: 'crawl', // 电子围栏
      singleComponents:
        [
          { name: 'ProtectWindpowerAreaInfoComponent' }, //  单个详细
          { name: 'ProtectWindpowerAreaHandleComponent' }// 电子围栏 编辑
        ]
    },
    {
      index: 1,
      type: 'cable',// 海底电缆 
      singleComponents:
        [
          { name: 'ProtectSeaCableInfoComponent' },
          { name: 'ProtectSeaCableHandleComponent' }
        ]
    },
    {
      index: 2,
      type: 'navigation', // 航标
      singleComponents:
        [
          { name: 'ProtectNavMarkInfoComponent' },
          { name: 'ProtectNavMarkHandleComponent' }
        ]
    },
    {
      index: 3,
      type: 'fan', // 风机
      singleComponents:
        [
          { name: 'ProtectWindMotorInfoComponent' },
          { name: 'ProtectWindMotorHandleComponent' }
        ]
    },
  ]

  constructor(
    private mapLayerService: MapLayerComponentService,
    private service: StationService,
    private mapState: MapStateService,
    private shipService: WindpowerShipService,
    private httpProject_: StationTestProjectService
  ) {
    // this.navList = _.cloneDeep(NAV_MARK_LIST)
    this.map = this.mapState.getMapInstance()
    // this.componentChangeSub = this.service.componentChange$.subscribe(res => {
    //   this.computedListIconState(this.list, res.list)
    // })
    this.searchSub$.pipe(
      distinctUntilChanged(),
      debounceTime(500),
      takeUntil(this.destroy$)
    ).subscribe((keyword) => {
      this.doSearch(keyword)
    })
  }
  ngOnChanges(changes: SimpleChanges) {
    const {inProject} = changes;
    if (inProject && !inProject.isFirstChange()) {
      this.getProjectList();
    }
  }
  ngOnDestroy(): void {
    this.componentChangeSub.unsubscribe()
    this.destroy$.next()
    this.destroy$.complete()
  }
  ngOnInit(): void {
    this.initList();
    interval(1000 * 60).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.initList();
    })
  }
  initList() {
    if (this.inAll) {
      this.getList(false);
    } else {
      this.getProjectList();
    }
  }
  // private computedListIconState(list: StationTreeNode[], stateList: Array<ProtectHanldeStatus>) {
  //   for (let i = 0; i < list.length; i++) {
  //     const item = list[i]
  //     item.infoSelected = item.editSelected = false
  //     this.computedTreeNodeState(item, stateList)
  //     if (item.children && item.children.length) {
  //       this.computedListIconState(item.children, stateList)
  //     }
  //   }
  // }



  // private computedTreeNodeState(node: StationTreeNode, stateList: Array<ProtectHanldeStatus>) {
  //   const existList = stateList.filter(ele => ele.id == node.id)
  //   if (existList && existList.length) {
  //     existList.forEach(ele => {
  //       ele.type == 'edit' ? node.editSelected = true : node.infoSelected = true
  //     })
  //   }
  // }


  private getList(expandedChild: boolean) {
    this.spinning = true
    // this.service.getAll().then(res => {
    //   this.spinning = false
    //   console.log('res', res);
    //   this.list = this.genTreeList2(res)
    //   // this.list = this.genTreeList(res, expandedChild)
    // }).catch(err => {
    //   this.spinning = false
    // })
    this.service.getListWithoutPage(this.search).then(res => {
      this.list = this.genTreeList(res)
      this._cachedList = _.cloneDeep(this.list)
      this.spinning = false
    }).catch(err => {
      this.spinning = false
    })
  }
  // TODO 根据项目id获取list
  private async getProjectList() {
    if (!this.inProject) return;
    const res = await this.httpProject_.getInfo(this.inProject.id);
    this.list = this.genProjectTreeList(res);
  }
  private genProjectTreeList(project: TestProjectInfo) {
    return [
      {
        label: project.projectName,
        expanded: true,
        type: 'project',
        children: this.genTreeList(project.stationList),
      }
    ]
  }
  private genTreeList2(stationList: Station[]) {
    return stationList.map(station => {
      const { aispositionList, aisvirtualatonList } = station
      const item: StationTreeNode = {
        label: station.stationCode,
        expanded: true,
        type: 'station',
        sysUpdated: station.sysUpdated,
        children: [
          {
            label: '船舶',
            expanded: aispositionList && aispositionList.length > 0,
            children: aispositionList?.map(ele => {
              return {
                label: ele.mmsi,
                type: 'ship',
                mmsi: ele.mmsi,
                latlng: L.latLng(ele.latitude, ele.longitude)
              }
            }) || []
          },
          {
            label: '航标',
            expanded: aisvirtualatonList && aisvirtualatonList.length > 0,
            children: aisvirtualatonList?.map(ele => {
              return {
                type: 'virtual',
                mmsi: ele.mmsi,
                label: ele.aidsNameEn || ele.mmsi,
                latlng: L.latLng(ele.latitude, ele.longitude)
              }
            }) || []
          },
        ]

      }
      return item
    })
  }
  /**
   * 关键字搜索
   */
  searchKeyword() {
    this.getList(true)
  }

  searchType() {
    this.getList(true)
  }
  /**
      * 点击事件
      * @param item 
      */
  clickItem(item: StationTreeNode) {
    if (item.children) {
      item.expanded = !item.expanded
    }

    if (item.id && item.expanded) {
      this.getAisVirtualatonList(item)
    }

    if (item.latlng) {
      const level = this.map.getZoom()
      if (level < 15) {
        this.map.setView(item.latlng!, 15)
      } else {
        this.map.setView(item.latlng!)
      }
    }
  }


  private getAisVirtualatonList(item: StationTreeNode) {
    const stationId = item.id!
    item.spining = true
    this.service.getInfo(stationId).then(res => {
      if (res) {
        const { aispositionList, aisvirtualatonList } = res
        item.children = [
          {
            label: '船舶',
            children: aispositionList?.map(ele => {
              return {
                label: ele.mmsi,
                type: 'ship',
                mmsi: ele.mmsi,
                parentId: stationId,
                latlng: L.latLng(ele.latitude, ele.longitude)
              }
            }) || []
          },
          {
            label: '航标',
            children: aisvirtualatonList?.map(ele => {
              return {
                type: 'virtual',
                mmsi: ele.mmsi,
                parentId: stationId,
                label: ele.aidsNameEn || ele.mmsi,
                latlng: L.latLng(ele.latitude, ele.longitude)
              }
            }) || []
          },
        ]
      }
    }).finally(() => {
      item.spining = false
    })
  }

  searchByKeyWord() {
    this.searchSub$.next(this.search.keyword!)
  }

  doSearch(keyword?: string) {
    const word = keyword || this.search.keyword!
    if (isNotEmpty(word)) {
      this.spinning = true
      this.list = _.cloneDeep(this._cachedList.filter(ele => {
        const {label = ''} = ele;
        return label.toLowerCase().includes(word.toLowerCase())
      }))
      setTimeout(() => {
        this.spinning = false
      }, 100);
    } else {
      this.getList(false)
    }
  }

  /**
   * 编辑保护区
   * @param item 
   */
  edit(item: StationTreeNode) {
    this.onEvent.emit({type: 'edit', data: item})
  }

  /**
   * 打开详情
   */
  toInfo(item: StationTreeNode) {
    if (item.type == 'ship') {
      this.shipService.getInfo(item.mmsi!).then(res => {
        let newParams: any;
        newParams = res
        this.mapLayerService.addComponent({
          title: `${newParams.shipName || newParams.mmsi}（详细）`,
          name: 'ShipWindpowerInfoComponent',
          type: 'popup',
          titleType: 'primary',
          data: {
            params: newParams,
            // 使用海图的定位弹出详情
            position: { latlng: item.latlng, offset: [0, 24] }
          }
        })
      })
    } else if (item.type == 'virtual') {
      // console.log('虚拟航标', item)
      this.mapLayerService.addComponent({
        title: `播发记录(${item.mmsi})`,
        name: 'NavMarkBroadcastComponent',
        titleType: 'default',
        type: 'popup',
        data: {
          params: { mmsi: item.mmsi, id: item.parentId },
          position: {
            left: 360,
            top: 10
          }
        }
      })
    } else if (item.type == 'station') {
      const {latitude, longitude} = item
      if (latitude && longitude) {
        this.map.setView([latitude, longitude]);
      }
    }
    this.onEvent.emit({type: 'info', data: item});
  }

  // /**
  //  * 关闭详细、编辑popup 回调 (点击popup上的 关闭图标)
  //  */
  // private closeComponentCb = (state: ProtectHanldeStatus) => {
  //   return () => {
  //     this.service.removeComponent(state)
  //   }

  // }

  /**
    *
    * 通过坐标点数组获取绝对中心点
    * @private
    * @param {Array<L.LatLng>} latlngs
    * @return {*}  {L.Point}
    * @memberof WindpowerProtectCanvasLayer
    */
  private getGeoJSONCenter(latlngs: Array<L.LatLng>): L.LatLng {
    const features = turf.featureCollection(
      latlngs.map((ele) => turf.point([ele.lat, ele.lng]))
    )
    const center = turf.center(features).geometry.coordinates
    return L.latLng(center[0], center[1])
  }

  /**
   *
   * 生成树形数组
   * @private
   * @param {Array<TblProarea>} arr
   * @return {*}  {Array<StationTreeNode>}
   * @memberof ProtectListComponent
   */
  private genTreeList(arr: Array<Station>): Array<StationTreeNode> {
    this.dataIndex = 0
    return arr.map(station => {
      return {
        label: station.stationCode,
        id: station.id,
        sysUpdated: station.sysUpdated,
        type: 'station',
        children: [
          {
            label: '船舶',
            children: []
          },
          {
            label: '航标',
            children: []
          },
        ],
        latitude: station.latitude,
        longitude: station.longitude
      }
    })
    // return arr.map(proarea => {
    //   this.dataIndex++
    //   const item: StationTreeNode = {
    //     searchLevel: '1',
    //     expanded: true,
    //     type: 'proarea',
    //     label: proarea.areaName!,
    //     level: '1',
    //     areaId: proarea.id!,
    //     id: proarea.id!,
    //     index: this.dataIndex,
    //     children: []
    //   }
    //   this.dataList.forEach((e) => {
    //     const list: Array<any> = proarea[e.listPro]
    //     if (list && list.length) {
    //       this.dataIndex++
    //       const subItem: StationTreeNode = {
    //         expanded: expandedChild,
    //         label: e.label,
    //         index: this.dataIndex,
    //         areaId: proarea.id!,
    //         id: proarea.id! + e.type,
    //         level: e.searchLevel,
    //         type: e.type,
    //         searchLevel: e.searchLevel,
    //       }
    //       subItem.children = this.genItemChildren(e.searchLevel, list, proarea.id!, e.type, e.icon)
    //       if (item.children)
    //         item.children.push(subItem)
    //     }
    //   })
    //   return item
    // })
  }

  /**
   * 根据航标类型获取航标的icon
   * @param type 
   * @returns 
   */
  private getNavMarkIcon(type: string): string | undefined {
    // return this.navList.find(ele => ele.type == type)?.icon
    return undefined
  }

  /**
   *
   * 获取保护区子级的children
   * @private
   * @param {string} parentLevel
   * @param {Array<any>} child
   * @param {string} blockId 区域主键（前端生成:name 相同为同一blockId）
   * @return {*} 
   * @memberof ProtectListComponent
   */
  private genItemChildren(parentLevel: string, child: Array<any>, areaId: string, type: string, icon: string): StationTreeNode[] {
    // const isNav = isEmpty(icon)
    return [{
      areaId: '1',
      label: '1',
      level: '1',
      searchLevel: '1',
      index: 0
    }]
  }

}
