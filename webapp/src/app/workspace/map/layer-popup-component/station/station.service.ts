import { Injectable } from '@angular/core';
import { BaseCURDService } from 'src/app/shared/services/base-curd.service';
import { BaseHttpService } from 'src/app/shared/services/base-http.service';
import { Station, StationSearch } from './station.model';

@Injectable({
  providedIn: 'root'
})
export class StationService extends BaseCURDService<Station> {

  constructor(protected http: BaseHttpService) {
    super(http, '/api/Station')
  }

  getAll() {
    return this.http.get<Station[]>(`${this.baseUrl}/getAll`)
  }

  getAllVirtualatons() {
    return this.http.get<Station[]>(`/api/Aisvirtualaton/getAll`)
  }
}
