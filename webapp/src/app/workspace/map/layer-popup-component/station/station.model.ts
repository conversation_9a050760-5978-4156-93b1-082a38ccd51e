
export interface Station {
    aispositionList?: AisPosition[]
    aisvirtualatonList?: AisVirtualaton[]
    id: string
    stationCode: string
    expanded?: boolean
    sysUpdated?: string;
    // 经纬度
    longitude?: number
    latitude?: number
    stationName?: string
    remark?: string
}

export interface AisPosition {
    [key: string]: any
}

export interface AisVirtualaton {
    [key: string]: any
}


export interface StationTreeNode {
    areaId?: string
    label?: string
    level?: string,
    searchLevel?: string,
    index?: number
    type?: string
    blockId?: string
    leaf?: boolean
    expanded?: boolean
    children?: StationTreeNode[],
    list?: Array<any>
    icon?: string
    id?: string
    parentId?: string
    latlng?: L.LatLng
    infoSelected?: boolean
    editSelected?: boolean
    center?: L.LatLng,
    mmsi?: string
    sysUpdated?: string
    spining?: boolean
    longitude?: number
    latitude?: number
}

export class StationSearch {
    areaType?: string
    keyword?: string
    constructor() {
        // this.areaType = '0'
    }
}

export class TblNavigationstation {
    id?: string;
    baseId?: string;
    baseName?: string;
    aidsName?: string;
    aidsNameEn?: string;
    mmsi?: string;
    typeCode?: string;
    typeName?: string;
    targetingCode?: string;
    targetingName?: string;
    ts?: number;
    updateTimer?: number;
    mark?: string;
    latitude?: number;
    longitude?: number;
    transferType?: number;
    split?: number;
    sizeA?: number;
    sizeB?: number;
    sizeC?: number;
    sizeD?: number;
    splitWay?: string;
    userId?: string;
    userName?: string;
    ifEnable?: number;
    sysCreated?: string;
    sysUpdated?: string;
    sysDeleted?: number;
    updateTime?: string;
    utcTimeA?: number;
    utcTimeB?: number;
    startSplitA?: number;
    startSplitB?: number;
    timeAddA?: number;
    timeAddB?: number;
    number?: string;
    distance?: number;
    orgTypeCode?: string;
    orgLevel?: string;
    broadcastTime?: string;
    ifFollow?: number;
    navigationStationId?: string;
    ifOldData?: number;
    type?: string;
    orgName?: string;
    coordinate?: string;
    attribute?: string;
}