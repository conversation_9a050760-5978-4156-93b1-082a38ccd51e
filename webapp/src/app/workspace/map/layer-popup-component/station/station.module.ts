import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzEmptyModule } from 'ng-zorro-antd/empty';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { StationListComponent } from './station-list.component';
import { StationListItemComponent } from './station-list-item/station-list-item.component';
import { FormsModule } from '@angular/forms';
import { NavigationAidLayerComponent } from './navigation-aid-layer.component';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { StationLayerComponent } from './station-layer/station-layer.component';

@NgModule({
  declarations: [
    StationListComponent,
    StationListItemComponent,
    NavigationAidLayerComponent,
    StationLayerComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    NzInputModule,
    NzEmptyModule,
    NzSelectModule,
    NzSpinModule,
    NzIconModule
  ],
  exports: [
    StationListComponent,
  ]
})
export class StationModule { }
