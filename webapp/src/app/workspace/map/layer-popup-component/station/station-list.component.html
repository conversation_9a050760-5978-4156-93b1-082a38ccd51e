<div class="protect-list-wrapper">
    <!--  <div class="search-container" *ngIf="inAll">
        <nz-input-group nzCompact class="sl-select-primary-bg">
            <nz-select style="width: 86px" [(ngModel)]="search.areaType" (ngModelChange)="searchType()">
                <nz-option nzLabel="全部" nzValue="0"></nz-option>
                <nz-option nzLabel="电子围栏" nzValue="1"></nz-option>
                <nz-option nzLabel="海底电缆" nzValue="2"></nz-option>
                <nz-option nzLabel="航标" nzValue="3"></nz-option>
                <nz-option nzLabel="风机" nzValue="4"></nz-option>
            </nz-select>
            <nz-input-group [nzPrefix]="suffixIconSearch" [nzSuffix]="inputClearTpl" style="width: 196px;">
                <input type="text" nz-input placeholder="请输入关键字" name="keyword" [(ngModel)]="search.keyword" />
            </nz-input-group>
            <ng-template #suffixIconSearch>
                <a href=" javascript:void(0)" style="display: flex;" (click)="searchKeyword()">
                    <i class="sl-input-search-16"></i>
                </a>
            </ng-template>
            <ng-template #inputClearTpl>
                <a href=" javascript:void(0)" style="display: flex;" *ngIf="search.keyword"
                    (click)="search.keyword='';searchKeyword()">
                    <i class="sl-close-circle-white"></i>
                </a>
            </ng-template>
        </nz-input-group>
        <a href="javascript:void(0)" *slHasAnyAuthority="'1.1.1'" class="handle" (click)="addProtect()">
            <i class="sl-plus-circle-20"></i>
        </a> 
    </div>-->
    <div class="list-container">
        <div class="search-container" *ngIf="inAll">
            <nz-input-group [nzPrefix]="suffixIconSearch" [nzSuffix]="inputClearTpl">
                <input type="text" nz-input name="keyWord" [(ngModel)]="search.keyword" (keyup)="searchByKeyWord()">
            </nz-input-group>
            <ng-template #suffixIconSearch>
                <a href=" javascript:void(0)" style="display: flex;" (click)="doSearch()">
                    <i class="sl-input-search-16"></i>
                </a>
            </ng-template>
            <ng-template #inputClearTpl>
                <a href=" javascript:void(0)" style="display: flex;" *ngIf="search.keyword"
                    (click)="search.keyword='';doSearch()">
                    <i class="sl-close-circle-white"></i>
                </a>
            </ng-template>
        </div>
        <div class="body_content" [class.project-tree-list]="!inAll" #ulContainerBody>
            <div class="body_content__inner">
                <ul class="ul-list" #ulContent>
                    <ng-container *ngFor="let item of list;index as i">
                        <app-station-list-item [item]="item" (onInfoItem)="toInfo($event)" (onEditItem)="edit($event)"
                            (onClickItem)="clickItem($event)"></app-station-list-item>
                    </ng-container>
                </ul>
                <nz-spin [nzSpinning]="spinning" [nzTip]="'数据加载中...'">
                </nz-spin>
                <div style="margin-top: 40px;" *ngIf="(!list || list.length==0) && inAll">
                    <nz-empty></nz-empty>
                </div>
            </div>
        </div>
    </div>
</div>