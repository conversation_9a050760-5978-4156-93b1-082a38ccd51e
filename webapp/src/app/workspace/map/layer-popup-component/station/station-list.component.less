@import "/src/styles/mixins";

.protect-list-wrapper {
  background: #fff;

  .search-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 4px;
    padding: 11px 12px;
  }

  .list-container {

    // height: 460px;
    .search-container {
      padding: 5px 10px;
      position: relative;
    }

    height: 500px;
    overflow: hidden;
    .scrollbars(5px, #C8C9CC, rgba(0, 0, 0, 0));

    .body_content {
      padding: 0 12px;
      overflow-y: auto;
      height: calc(100% - 42px);
      .body_content__inner{
        position: relative;
        nz-spin{
          position: absolute;
          top: 40px;
          left: 50%;
          transform: translateX(-50%);
          width: 80px;
        }
      }
    }
  }
}

li.item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 32px;

  a,
  .btn {
    display: inline-flex;
    align-items: center;
  }

  a>i {
    margin-right: 4px;
  }

  a>span {
    color: #333;
    color: #333;
    overflow: hidden;
    white-space: nowrap;
    width: 210px;
    text-overflow: ellipsis;
  }

  .btn {
    a+a {
      margin-left: 8px;
    }
  }

  &.has-bg,
  &:hover {
    background: #f5f8fe;
  }
}

.ul-sublist {
  display: none;

  &.expanded {
    display: block;
  }
}