import { Component, OnInit } from '@angular/core';
import { MapStateService } from '../../../service/map-state.service';
import {
  SlLeafletCanvasMarkerRetItem,
  SlLeafletCanvasMarkers,
} from '../../../utils';
import { StationService } from '../station.service';
import { Station } from '../station.model';
import * as L from 'leaflet';

@Component({
  selector: 'app-station-layer',
  template: ``,
})
export class StationLayerComponent implements OnInit {
  private map: L.Map;
  private stationLayer: SlLeafletCanvasMarkers;
  constructor(
    private mapState: MapStateService,
    private httpStation_: StationService
  ) {
    this.map = this.mapState.getMapInstance();
    this.stationLayer = new SlLeafletCanvasMarkers().addTo(this.map);
    this.stationLayer.addOnClickListener(this.clickListener);
  }

  ngOnInit(): void {
    this.getStation();
  }
  clickListener = (
    event: L.LeafletMouseEvent,
    ret: SlLeafletCanvasMarkerRetItem[]
  ) => {
    const marker = ret[0].marker;
    if (marker['station']) {

    }
  };
  async getStation() {
    const res = await this.httpStation_.getAll();
    this.removeCanvasMarkers();
    const markers = this.genStationMarker(res);
    this.stationLayer.addLayers(markers);
  }
  /**
   * 航标marker
   * @param navAids
   * @returns
   */
  private genStationMarker(stations: Station[]) {
    const markers = [];
    for (const item of stations) {
      // 坐标点
      const latlng: L.LatLng = L.latLng(item.latitude!, item.longitude!);
      // icon
      const iconOpt: L.IconOptions = {
        iconUrl: '/assets/img/map/navigation/station.png',
        iconSize: [24, 24],
        iconAnchor: [12, 12],
      };
      const icon = L.icon(iconOpt);
      const marker: L.Marker = L.marker(latlng, {
        icon,
        autoPan: false,
        zIndexOffset: 205,
      });
      marker['station'] = item;
      marker['title'] = item.stationName || item.stationCode;
      marker['textHeight'] = 22;
      marker['latlng'] = latlng;
      markers.push(marker);
    }
    return markers;
  }
  private removeCanvasMarkers() {
    if (this.stationLayer) this.stationLayer.clearLayers();
  }
  ngOnDestroy() {
    this.stationLayer.remove();
  }
}
