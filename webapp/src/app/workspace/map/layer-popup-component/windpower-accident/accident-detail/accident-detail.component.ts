import { TblAccident } from './../../../../workspace-shared/models/tbl_accident';
import { Component, Input, OnInit } from '@angular/core';
import { WindpowerAccidentService } from '../windpower-accident.service';
import { transformPosUnit } from '../../../utils/map-tools';

@Component({
  selector: 'app-accident-detail',
  templateUrl: './accident-detail.component.html',
  styleUrls: ['./accident-detail.component.less'],
})
export class AccidentDetailComponent implements OnInit {
  @Input() data: any;
  accident: TblAccident = new TblAccident();
  latStr!: string;
  lngStr!: string;

  constructor(private service: WindpowerAccidentService) {}

  ngOnInit() {
    this.getInfo(this.data['params'].id);
  }
  getInfo(id: string) {
    this.service.getInfo2(id).then((res: TblAccident) => {
      this.accident = res!;
      const { latStr, lngStr } = transformPosUnit(
        this.accident.latitude!,
        this.accident.longitude!
      );
      this.latStr = latStr;
      this.lngStr = lngStr;
    });
  }
}
