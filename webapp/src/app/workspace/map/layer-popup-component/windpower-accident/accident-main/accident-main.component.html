<div class="wrapper-bar">
  <div class="top-search">
    <div>
      <nz-input-group [nzPrefix]="suffixIconSearch" [nzSuffix]="inputClearTpl"
        style="width: 282px;height: 36px;background-color: #eef3fc">
        <input type="text" nz-input style="background-color: #eef3fc" placeholder="请输入事故名称/船舶MMSI"
          [(ngModel)]="inputName" />
      </nz-input-group>
      <ng-template #suffixIconSearch>
        <a href=" javascript:void(0)" style="display: flex;" (click)="doSearch()">
          <i class="sl-input-search-16"></i>
        </a>
      </ng-template>
      <ng-template #inputClearTpl>
        <a href=" javascript:void(0)" style="display: flex;" *ngIf="inputName" (click)="inputName='';doSearch()">
          <i class="sl-close-circle-white"></i>
        </a>
      </ng-template>
    </div>
    <div class="boutton-wrapper">
      <a href=" javascript:void(0)"><i class="sl-plus-circle-20" *slHasAnyAuthority="'1.3.1'"
          (click)="addAccident()"></i></a>
    </div>
  </div>
  <div class="accident-list">
    <!-- <nz-avatar nzIcon="user" nzSrc="//zos.alipayobjects.com/rmsportal/ODTLcjxAfvqbxHnVXCYX.png"></nz-avatar> -->
    <nz-spin [nzSpinning]="spinning" [nzTip]="'数据加载中...'">
      <form>
        <tr class="one-color" style="width: 345px;">
          <td style="width: 42px;">序号</td>
          <td style="width: 143px;">事故名称</td>
          <td style="width: 86px;">发生时间</td>
          <td style="width: 66px;">操作</td>
        </tr>
        <tr [class]="(idx+1) % 2 == 0 ? 'one-color' : 'two-color'" *ngFor="let item of accidents; index as idx">
          <td>{{idx+1 + IndexNum}}</td>
          <td>
            <div
              style="width: 143px;height: 34px;overflow: hidden;text-overflow:ellipsis;white-space:nowrap;text-align: left;">
              <a href="javascript:void(0)" (click)="clickItem(item)" [title]="item.accidentName">
                {{item.accidentName}}</a>
            </div>
          </td>
          <td>{{ getAccidentTime(item.accidentTime!) }}</td>
          <td>
            <a href="javascript:void(0)" title="编辑" *slHasAnyAuthority="'1.3.2'" (click)="editAccident(item)">
              <i class="sl-edit{{item.editSelected?'-active':''}}-16"></i>
            </a>
            <a style="margin-left: 8px;" href="javascript:void(0)" title="详细" *slHasAnyAuthority="'1.3.4'"
              (click)="detailAccident(item)">
              <i class="sl-info{{item.infoSelected?'-active':''}}-16"></i>
            </a>
          </td>

        </tr>
      </form>

    </nz-spin>
    <nz-empty *ngIf="accidents&&accidents.length == 0"></nz-empty>

  </div>
  <div>
    <app-map-list-pagination (pageChange)="pageChanged($event)" [totalNum]="page.recordCount!"
      [totalPageNum]="page.pageCount!" [currentPage]="page.currentPage!" [pageRecord]="page.pageRecord!">
    </app-map-list-pagination>
  </div>


</div>
