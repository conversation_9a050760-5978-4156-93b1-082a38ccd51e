.wrapper-bar {
  background-color: #fdfeff;
  padding-top: 12px;
  height: 479px;
  width: 345px
}

.top-search {
  display: flex;
  padding-left: 12px;

  .boutton-wrapper {
    width: 51px;
    padding-top: 6px;
    text-align: center;
  }

}


.accident-list {
  overflow: hidden;
  padding-left: 4px;
  width: 345px;
  height: 400px;
  background-color: #fdfeff;
  padding-top: 8px;

  a {
    color: #333;
  }

  tr {
    td {
      line-height: 34px;
      text-align: center;

    }
  }


}

.page-footer {
  line-height: 32px;
  display: flex;
  justify-content: space-between;
  background-color: #f3f7ff;
  padding-right: 19.13px;

  padding-left: 16px;

  .right-handle {
    display: flex;
    align-items: center;

    span {
      width: 34px;
      line-height: 14px;
      padding: 0 4.25px;

    }
  }
}

// 序号记录背景颜色
.one-color {
  background-color: #f3f7ff;
}

.two-color {
  background-color: #fdfeff;
}
