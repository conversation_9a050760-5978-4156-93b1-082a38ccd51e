import { TblAccidentship } from 'src/app/workspace/workspace-shared/models/tbl_accidentship';
import { TblAccident } from './../../../../workspace-shared/models/tbl_accident';
import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { MapLayerComponentService } from '../../../service/map-layer-component.service';
import { WindpowerAccidentService } from '../windpower-accident.service';
import { BaseSearch, Page } from 'src/app/shared/models';
import { MapStateService } from './../../../service/map-state.service';
import * as _ from 'lodash';
import * as L from 'leaflet';
import { Subscription } from 'rxjs';
@Component({
  selector: 'app-accident-main',
  templateUrl: './accident-main.component.html',
  styleUrls: ['./accident-main.component.less'],
})
export class AccidentMainComponent implements OnInit, OnD<PERSON>roy {
  constructor(
    private mapLayerComponentService: MapLayerComponentService,
    private service: WindpowerAccidentService,
    private mapState: MapStateService
  ) {
    this.map = this.mapState.getMapInstance();
    this.subGetActive = this.service.activeClose$.subscribe((res) => {
      if (res) {
        this.closeEditComponentCb();
      }
    });
  }
  @Input() data: any;
  subGetActive: Subscription = new Subscription();
  // 按取消后取消活跃状态
  cancealActive: boolean = false;
  reqBody: BaseSearch = new BaseSearch();
  accidents: TblAccident[] = [];
  page: Page<TblAccident> = new Page();
  // 输入的名字
  spinning: boolean = true;
  inputName!: string;
  isActive: boolean = false;
  map: L.Map;
  shipList: Array<TblAccidentship> = [];
  // 记录当前编辑事故
  currentEditAccident?: TblAccident;
  // 记录当前事故详情
  currentInfoAccident?: TblAccident;
  ngOnInit() {
    // this.accidents = [{}];
    const { params } = this.data;
    if (params) {
      this.reqBody.keyword = params.mmsi;
      this.inputName = params.mmsi;
      this.getAccidentList(this.reqBody);
      return;
    } else {
      this.getAccidentList(this.reqBody);
    }
  }
  ngOnDestroy(): void {
    this.subGetActive.unsubscribe();
  }
  // 获取事故数据列表
  getAccidentList(reqBody: BaseSearch): void {
    reqBody.pageRecord = 10;
    this.service
      .getList2(reqBody)
      .then((res) => {
        this.page = res;
        this.accidents = res.result!;
        this.spinning = false;
      })
      .catch((res) => {
        this.spinning = false;
      });
  }

  getAccidentTime(accidentTime: string) {
    return accidentTime?.substring(0, 10);
  }

  get IndexNum() {
    return (this.page.currentPage - 1) * 10;
  }
  // 添加事故
  addAccident() {
    this.mapLayerComponentService.addComponent({
      name: 'AccidentAddComponent',
      title: '事故（新增）',
      titleType: 'primary',
      type: 'popup',
      data: {
        position: {
          left: 490,
          top: 59,
        },
        size: {
          width: 570,
        },
      },
    });
  }
  // 编辑事故
  editAccident(item: TblAccident) {
    if (this.currentEditAccident && this.currentEditAccident.id != item.id) {
      this.currentEditAccident.editSelected = false;
      item.editSelected = true;
      this.currentEditAccident = item;
    } else {
      if (item.editSelected) {
        item.editSelected = false;
        this.currentEditAccident = undefined;
        this.mapLayerComponentService.removeAllByName('AccidentAddComponent');
        return;
      }
      item.editSelected = true;
      this.currentEditAccident = item;
    }
    const latlng = L.latLng(item.latitude!, item.longitude!);
    this.service.getInfo2(item.id!).then((res) => {
      this.mapLayerComponentService.addComponent({
        name: 'AccidentAddComponent',
        title: '事故（编辑）',
        titleType: 'primary',
        type: 'popup',
        data: {
          position: {
            latlng: latlng,
            offset: [0, 25],
          },
          size: {
            width: 570,
          },
          params: {
            id: item.id,
            editAccident: res,
          },
        },
        closeCb: () => {
          this.closeEditComponentCb();
        },
      });
      if (item.latitude && item.longitude) {
        const latlng: L.LatLng = L.latLng(item.latitude!, item.longitude!);
        this.map.setView(latlng, 15);
      }
    });
  }
  // 事故详情
  detailAccident(item: TblAccident) {
    if (this.currentInfoAccident && this.currentInfoAccident.id != item.id) {
      this.currentInfoAccident.infoSelected = false;
      item.infoSelected = true;
      this.currentInfoAccident = item;
    } else {
      if (item.infoSelected) {
        item.infoSelected = false;
        this.currentInfoAccident = undefined;
        this.mapLayerComponentService.removeAllByName(
          'AccidentDetailComponent'
        );
        return;
      }
      item.infoSelected = true;
      this.currentInfoAccident = item;
    }
    const latlng = L.latLng(item.latitude!, item.longitude!);
    this.mapLayerComponentService.addComponent({
      name: 'AccidentDetailComponent',
      title: '事故（详细）',
      titleType: 'primary',
      type: 'popup',
      data: {
        position: {
          latlng: latlng,
          offset: [0, 25],
        },
        size: {
          width: 490,
        },
        params: {
          id: item.id!,
        },
      },
      closeCb: () => {
        this.closeInfoComponentCb();
      },
    });

    if (item.latitude && item.longitude) {
      const latlng: L.LatLng = L.latLng(item.latitude!, item.longitude!);
      this.map.setView(latlng, 15);
    }
  }
  doSearch() {
    this.spinning = true;
    const search = new BaseSearch();
    search.pageRecord = 10;
    search['keyword'] = this.inputName;
    this.service.getList2(search).then((res: any) => {
      this.spinning = false;
      this.accidents = res.result;
    });
  }

  /**
   * 改变分页
   */
  pageChanged(event: any) {
    this.reqBody.currentPage = event.currentPage;
    this.getAccidentList(this.reqBody);
  }

  clickItem(item: TblAccident) {
    if (item.latitude && item.longitude) {
      const latlng: L.LatLng = L.latLng(item.latitude!, item.longitude!);
      this.map.setView(latlng, 15);
    }
  }

  // 获取可疑船舶列表
  getShips(accident: TblAccident) {
    let search = {
      endTime: accident.endTime + ':00',
      lat: accident.latitude,
      lng: accident.longitude,
      rail: accident.rail,
      startTime: accident.startTime + ':00',
    };
    this.service.getShips(search).then((res: TblAccidentship[]) => {
      this.shipList = res;
    });
  }

  private closeEditComponentCb = () => {
    if (this.currentEditAccident) this.currentEditAccident.editSelected = false;
    this.currentEditAccident = undefined;
  };
  /**
   * 关闭详细popup 回调
   */
  private closeInfoComponentCb = () => {
    if (this.currentInfoAccident) this.currentInfoAccident.infoSelected = false;
    this.currentInfoAccident = undefined;
  };
}
