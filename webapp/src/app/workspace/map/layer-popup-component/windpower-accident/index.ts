import { DynamicComponent } from 'src/app/shared/models';
import { AccidentMainComponent } from './accident-main/accident-main.component';
import { AccidentAddComponent } from './accident-add/accident-add.component';
import { AccidentDetailComponent } from './accident-detail/accident-detail.component';
import { WindpowerAccidentLayerComponent } from './windpower-accident-layer';

// 基站
export const DYNAMIC_ACCIDENT_MAIN_COMPONENTS: Array<DynamicComponent> = [
  // 事故图层
  {
    name: 'WindpowerAccidentLayerComponent',
    component: WindpowerAccidentLayerComponent,
  },
  {
    name: 'AccidentMainComponent',
    component: AccidentMainComponent,
    hasChild: true,
  },
  {
    name: 'AccidentAddComponent',
    component: AccidentAddComponent,
    parentName: 'AccidentMainComponent',
  },
  {
    name: 'AccidentDetailComponent',
    component: AccidentDetailComponent,
    parentName: 'AccidentMainComponent',
  },
];
