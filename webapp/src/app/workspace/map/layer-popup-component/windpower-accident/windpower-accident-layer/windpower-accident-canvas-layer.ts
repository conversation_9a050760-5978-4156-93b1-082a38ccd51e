import { TblAccident } from 'src/app/workspace/workspace-shared/models/tbl_accident';
import * as L from 'leaflet';
import * as _ from 'lodash';
import rbush from 'rbush';
export class AccidentPoint {
  id: string;
  latlng: L.LatLng;
  imgUrl: string;
  accidentName: string;
  cavasImg?: HTMLImageElement;
  imgSize: [number, number];
  active?: boolean;
  constructor(
    id: string,
    latlng: L.LatLng,
    accidentName: string,
    imgUrl: string,
    imgSize: [number, number],
    active?: boolean
  ) {
    this.latlng = latlng;
    this.imgUrl = imgUrl;
    this.accidentName = accidentName;
    this.imgSize = imgSize;
    this.id = id;
    this.active = active === false ? false : true;
  }
}

/**
 *
 * 通过 canvas 绘制大量的marker 图标
 * @export
 * @class SlLeftleftCanvasMarkers
 * @extends {L.Layer}
 */

export class WindpowerAccidentCanvasLayer extends L.Layer {
  private _canvas!: HTMLCanvasElement;
  private _context!: CanvasRenderingContext2D;
  private _onClickListeners: Array<any> = [];
  private _onHoverListeners: Array<any> = ([] = []);

  // 当前加载的所有的事故点数据
  private accidentList: Array<TblAccident> = [];
  // 事故点
  private accidentPointList: Array<AccidentPoint> = [];

  private accidents = new rbush();

  options: any;
  animationLoop!: number;
  constructor() {
    super();
  }

  get activeAccidentLayers() {
    return this.accidentPointList.filter((ele) => ele.active);
  }

  /**addTo时会自动调用 */
  onAdd(map: L.Map) {
    this._map = map;
    if (!this._canvas) this.initCanvas();
    if (this.options.pane) this.getPane()?.appendChild(this._canvas);
    else map.getPanes().overlayPane.appendChild(this._canvas);
    map.on('moveend', this.reset, this);
    map.on('resize', this.reset, this);
    map.on('click', this._executeListeners, this);
    // map.on('mousemove', this._executeListeners, this);
    if (map.options.zoomAnimation && L.Browser.any3d) {
      /**缩放动画 */
      map.on('zoomanim', this._animateZoom, this);
    }
    this.reset();
    return this;
  }

  /**移除时会自动调用 */
  onRemove(map: any) {
    if (this.options.pane) {
      this.getPane()?.removeChild(this._canvas);
    } else {
      map.getPanes().overlayPane.removeChild(this._canvas);
    }
    map.off('moveend', this.reset, this);
    map.off('resize', this.reset, this);
    map.off('click', this._executeListeners, this);
    if (map.options.zoomAnimation) {
      map.off('zoomanim', this._animateZoom, this);
    }
    if (this.animationLoop) cancelAnimationFrame(this.animationLoop);
    return this;
  }

  private initCanvas() {
    this._canvas = L.DomUtil.create(
      'canvas',
      'leaflet-canvas-map leaflet-layer'
    );
    var originProp =
      '' +
      L.DomUtil.testProp([
        'transformOrigin',
        'WebkitTransformOrigin',
        'msTransformOrigin',
      ]);
    this._canvas.style[originProp] = '50% 50%';
    var size = this._map.getSize();
    this._canvas.width = size.x;
    this._canvas.height = size.y;
    this._context = this._canvas.getContext('2d')!;
    var animated = this._map.options.zoomAnimation && L.Browser.any3d;
    L.DomUtil.addClass(
      this._canvas,
      'leaflet-zoom-' + (animated ? 'animated' : 'hide')
    );
  }

  reset() {
    const topLeft = this._map.containerPointToLayerPoint([0, 0]);
    L.DomUtil.setPosition(this._canvas, topLeft);
    var size = this._map.getSize();
    this._canvas.width = size.x;
    this._canvas.height = size.y;
    this._redraw();
  }

  private _redraw() {
    this._clearContext();
    this.accidents.clear();
    let tmp: any[] = [];
    this.activeAccidentLayers.forEach((ele) => {
      const ret = this.redrawAccidentLayer(ele);
      tmp.push(ret[0]);
    });
    this.accidents.load(tmp);
  }

  private _executeListeners(event: any) {
    if (!this.accidentPointList || this.accidentPointList.length == 0) return;
    const x = event.containerPoint.x;
    const y = event.containerPoint.y;
    const ret = this.accidents.search({ minX: x, minY: y, maxX: x, maxY: y });
    if (event.type === 'click') {
      this._onClickListeners?.forEach((listener) => {
        listener(event, ret);
      });
    }
  }

  /**
   *
   * 清空画布
   * @private
   * @return {*}  {boolean}
   * @memberof ShipProtectCanvasUtil
   */
  private _clearContext(): boolean {
    let map = this._map;
    if (L.Browser.canvas && map) {
      let ctx = this._context,
        ww = this._canvas.width,
        hh = this._canvas.height;
      ctx.clearRect(0, 0, ww, hh); // 清空画布
      return true;
    }
    return false;
  }

  /**
   *
   * 添加事故点数组
   * @param {Array<Accident>} list
   * @memberof ShipProtectCanvasUtil
   */
  addAccidentLayers(list: Array<TblAccident>) {
    this.accidentList = list;
    this.accidentPointList = [];
    const accidentPointList = this.accidentList.map((element) => {
      const latlng = L.latLng(element.latitude!, element.longitude!);
      const accidentPoint = new AccidentPoint(
        element.id!,
        latlng,
        element.accidentName!,
        '/assets/img/map/accident/accident-point.png',
        [28, 34]
      );
      return accidentPoint;
    });
    let tmp: any[] = [];
    accidentPointList.forEach((ele) => {
      const ret = this._addAccidentLayer(ele);
      tmp.push(ret[0]);
    });
    this.accidents.load(tmp);
  }

  /**
   *
   * 条件点击事件
   * @param {*} listener
   * @memberof ShipAccidentCanvasUtil
   */
  addOnClickListener(listener: any) {
    this._onClickListeners.push(listener);
  }
  /**
   *
   * 添加事故点
   * @param {AccidentPoint} accident
   * @memberof ShipProtectCanvasUtil
   */
  _addAccidentLayer(accident: AccidentPoint) {
    this._drawImage(accident);
    const point = this._map.latLngToContainerPoint(accident.latlng);
    this._setAccidentName(point, accident.accidentName);
    this._updateAccidentList(accident);
    if (!this.accidents) this.accidents = new rbush();
    const iconSize = [28, 34];
    const adj_x = iconSize[0] / 2;
    const adj_y = iconSize[1] / 2;
    const ret = [
      {
        minX: point.x - adj_x,
        minY: point.y - adj_y,
        maxX: point.x + adj_x,
        maxY: point.y + adj_y,
        data: accident,
      },
      {
        minX: accident.latlng.lng,
        minY: accident.latlng.lat,
        maxX: accident.latlng.lng,
        maxY: accident.latlng.lat,
        data: accident,
      },
    ];
    return ret;
  }

  addAccidentLayer(accident: TblAccident) {
    const index = this.getIndex(accident.id!);
    if (index > -1) {
      const item = this.accidentPointList[index];
      item.active = true;
      this._redraw();
    } else {
      const latlng = L.latLng(accident.latitude!, accident.longitude!);
      const accidentPoint = new AccidentPoint(
        accident.id!,
        latlng,
        accident.accidentName!,
        '/assets/img/map/accident/accident-point.png',
        [28, 34]
      );
      this._addAccidentLayer(accidentPoint);
    }
  }
  /**
   *
   * 重绘事故点
   * @private
   * @param {AccidentPoint} accidentPoint
   * @memberof ShipProtectCanvasUtil
   */
  private redrawAccidentLayer(accidentPoint: AccidentPoint) {
    this._drawImage(accidentPoint);
    const point = this._map.latLngToContainerPoint(accidentPoint.latlng);
    this._setAccidentName(point, accidentPoint.accidentName);
    if (!this.accidents) this.accidents = new rbush();
    const iconSize = [28, 34];
    const adj_x = iconSize[0] / 2;
    const adj_y = iconSize[1] / 2;
    const ret = [
      {
        minX: point.x - adj_x,
        minY: point.y - adj_y,
        maxX: point.x + adj_x,
        maxY: point.y + adj_y,
        data: accidentPoint,
      },
      {
        minX: accidentPoint.latlng.lng,
        minY: accidentPoint.latlng.lat,
        maxX: accidentPoint.latlng.lng,
        maxY: accidentPoint.latlng.lat,
        data: accidentPoint,
      },
    ];
    return ret;
  }

  private getIndex(id: string) {
    return this.accidentPointList.findIndex((ele) => ele.id === id);
  }

  /**
   *
   * 清空事故点
   * @memberof ShipProtectCanvasUtil
   */
  clearAccidentLayers() {
    this.accidentPointList = [];
    this.accidents = null;
    this._redraw();
  }

  /**
   *
   *
   * @param {Accident} accident
   * @memberof ShipProtectCanvasUtil
   */
  removeAccidentLayer(accident: TblAccident) {
    const index = this.getIndex(accident.id!);
    if (index > -1) {
      const accidentPoint = this.accidentPointList[index];
      accidentPoint.active = false;
      this._redraw();
    }
  }

  private _updateAccidentList(accidentPoint: AccidentPoint) {
    const index = this.accidentPointList.findIndex(
      (ele) => ele.id === accidentPoint.id
    );
    if (index == -1) {
      this.accidentPointList.push(accidentPoint);
    } else {
      this.accidentPointList.splice(index, 1, accidentPoint);
    }
  }

  _drawImage(accidentPoint: AccidentPoint) {
    const ctx = this._context;
    const point = this._map?.latLngToContainerPoint(accidentPoint.latlng!);
    const size = accidentPoint.imgSize;
    const x = point.x - size[0] / 2,
      y = point.y - size[1] / 2;
    if (!accidentPoint.cavasImg) {
      const canvasImg = new Image();
      canvasImg.src = accidentPoint.imgUrl;
      accidentPoint.cavasImg = canvasImg;
      accidentPoint.cavasImg.onload = () => {
        // ctx.translate(point.x, point.y);
        // ctx.rotate(0);
        ctx.drawImage(accidentPoint.cavasImg!, x, y, size[0], size[1]);
        // ctx.rotate(-0);
        // ctx.translate(point.x, point.y);
      };
    } else {
      ctx.drawImage(accidentPoint.cavasImg!, x, y, size[0], size[1]);
    }
  }

  private _animateZoom(e: any) {
    let map: any = this._map;
    var scale = map.getZoomScale(e.zoom),
      offset = map
        ._getCenterOffset(e.center)
        ._multiplyBy(-scale)
        .subtract(map._getMapPanePos());
    L.DomUtil.setTransform(this._canvas, offset, scale);
  }

  /**
   *
   * 设置事故点名称
   * @private
   * @param {L.Point} point
   * @param {string} accidentName
   * @memberof ShipProtectCanvasUtil
   */
  private _setAccidentName(point: L.Point, accidentName: string) {
    let zoom = this._map.getZoom();
    if (!accidentName || zoom < 10) return;
    let ctx = this._context;
    ctx.beginPath();
    ctx.font = '16px "微软雅黑"';
    ctx.textAlign = 'center';
    ctx.fillStyle = 'red';
    ctx.textBaseline = 'bottom';
    ctx.globalAlpha = 1;
    ctx.fillText(accidentName, point.x, point.y + 32);
    ctx.stroke();
  }
  /**
   *
   * 将特定的坐标点字符串转为 L.Point数组
   * 后台返回的坐标点以分号(;)进行分割，坐标以逗号(,) 分割
   * @private
   * @param {string} coordinateStr
   * @return {*}
   * @memberof ShipProtectCanvasUtil
   */
  private transformCoordinateStrToPoints(
    coordinateStr: string
  ): Array<L.Point> {
    return this.transformLatlngsToPoints(
      this.transformCoordinateStrToLatlngs(coordinateStr)
    );
  }

  /**
   *
   * 将坐标点数组转为point数组
   * @private
   * @param {string} coordinateStr
   * @return {*}  {Array<L.Point>}
   * @memberof ShipProtectCanvasUtil
   */
  private transformLatlngsToPoints(latlngs: Array<L.LatLng>): Array<L.Point> {
    return latlngs.map((ele) => this._map.latLngToContainerPoint(ele));
  }
  /**
   *
   * 将特定的坐标点字符串转为 L.Latlng 数组
   * 后台返回的坐标点以分号(;)进行分割，坐标以逗号(,) 分割
   * @private
   * @param {string} coordinateStr
   * @return {*}  {Array<L.LatLng>}
   * @memberof ShipProtectCanvasUtil
   */
  private transformCoordinateStrToLatlngs(
    coordinateStr: string
  ): Array<L.LatLng> {
    return coordinateStr.split(';').map((ele) => {
      const [lng, lat] = ele.split(',');
      return L.latLng(parseFloat(lat), parseFloat(lng));
    });
  }
}
