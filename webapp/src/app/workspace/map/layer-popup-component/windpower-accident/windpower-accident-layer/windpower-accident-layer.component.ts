import { data } from 'jquery';
import { TblAccident } from './../../../../workspace-shared/models/tbl_accident';
import { BaseSearch } from './../../../../../shared/models/base-search.model';
import { WindpowerAccidentService } from './../windpower-accident.service';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { MapLayerComponentService } from '../../../service/map-layer-component.service';
import { MapStateService } from '../../../service/map-state.service';
import { WindpowerAccidentCanvasLayer } from './windpower-accident-canvas-layer';
import * as L from 'leaflet';

@Component({
  selector: 'app-windpower-accident-layer',
  template: ``,
})
export class WindpowerAccidentLayerComponent implements OnInit, OnDestroy {
  private map: L.Map;
  accidentLayer: WindpowerAccidentCanvasLayer;
  accidentList: Array<TblAccident> = [];
  get zoomLevel() {
    return this.map.getZoom();
  }
  viewBounds: L.LatLngBounds;
  baseSearch = new BaseSearch();
  constructor(
    private mapState: MapStateService,
    private mapLayerService: MapLayerComponentService,
    private service: WindpowerAccidentService
  ) {
    this.map = this.mapState.getMapInstance();
    this.accidentLayer = new WindpowerAccidentCanvasLayer().addTo(this.map); // 添加map到画布里
    this.viewBounds = this.map.getBounds();
  }
  ngOnDestroy(): void {
    this.accidentLayer.remove();
  }

  ngOnInit() {
    this.getAccident();
  }

  getAccident(): void {
    const bounds = this.viewBounds;
    this.service.getAllAccident().then((res: TblAccident[]) => {
      this.accidentList = res;
      this.accidentLayer.addAccidentLayers(this.accidentList);
      this.accidentLayer.addOnClickListener((event: any, ret: any) => {
        const rbushData = ret[0];
        if (rbushData) {
          const { data } = rbushData;
          this.mapLayerService.addComponent({
            name: 'AccidentDetailComponent',
            title: '事故（详细）',
            titleType: 'primary',
            type: 'popup',
            data: {
              position: {
                latlng: data.latlng,
                offset: [0, 25],
              },
              size: {
                width: 490,
              },
              params: {
                id: data.id!,
              },
            },
          });
          if (data) {
            this.map.setView(data.latlng, 15);
          }
        }
      });
    });
  }
}
