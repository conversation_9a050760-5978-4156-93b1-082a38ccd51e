import { MapSharedModule } from './../../map-shared/map-shared.module';
// 事故主要界面
import { AccidentMainComponent } from './accident-main/accident-main.component';
import { AccidentAddComponent } from './accident-add/accident-add.component';
import { AddRecordComponent } from './accident-add/add-record/add-record.component';
import { AddTraceComponent } from './accident-add/add-trace/add-trace.component';
import { AddAccountComponent } from './accident-add/add-account/add-account.component';
import { AccidentDetailComponent } from './accident-detail/accident-detail.component';

import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzAvatarModule } from 'ng-zorro-antd/avatar';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
// sl 按钮
import { SlButtonModule } from 'src/app/shared/modules/sl-button/sl-button.module';
import { SlCheckBoxModule } from './../../../../shared/modules/sl-checkbox/sl-checkbox.module';
import { SharedModule } from 'src/app/shared/shared.module';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { WindpowerAccidentLayerComponent } from './windpower-accident-layer/windpower-accident-layer.component';
import { NzEmptyModule } from 'ng-zorro-antd/empty';
import { NzButtonModule } from 'ng-zorro-antd/button';
@NgModule({
  declarations: [
    AccidentMainComponent,
    AccidentAddComponent,
    AddRecordComponent,
    AddTraceComponent,
    AddAccountComponent,
    AccidentDetailComponent,
    WindpowerAccidentLayerComponent,
  ],
  imports: [
    CommonModule,
    NzInputModule,
    NzSelectModule,
    NzAvatarModule,
    SlButtonModule,
    NzTabsModule,
    SlCheckBoxModule,
    SharedModule,
    NzRadioModule,
    NzDatePickerModule,
    FormsModule,
    MapSharedModule,
    NzCheckboxModule,
    NzSpinModule,
    NzEmptyModule,
    NzButtonModule,
  ],
  exports: [AccidentMainComponent],
})
export class WindpowerAccidentModule {}
