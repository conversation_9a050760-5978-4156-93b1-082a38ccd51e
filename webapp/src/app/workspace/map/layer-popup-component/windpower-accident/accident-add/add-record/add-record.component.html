<div>
  <div class="tittle">
    事故记录:
  </div>
  <form #validateForm="ngForm" style="margin-right: 24px;">
    <table class="add-accident">
      <tr>
        <td style="width: 104px;"><span>事故名称：</span></td>
        <!-- <td><input type="text" nz-input></td> -->
        <td>
          <input placeholder="请输入事故名称" [slValidate]="{required:true,label:'事故名称'}" type="text" name="id" nz-input
            [(ngModel)]="accident.accidentName" />
        </td>
      </tr>
      <tr>
        <td><span>发生时间：</span></td>
        <td>
          <nz-date-picker style="width: 406px;" placeholder="请选择发生时间" [nzShowTime]="{ nzFormat: 'HH:mm'}"
            nzFormat="yyyy-MM-dd HH:mm" [(ngModel)]="accident.accidentTimePage"
            [slValidate]="{required:true,label:'发生时间'}" name="accidentTime">
          </nz-date-picker>
        </td>
      </tr>
      <tr>
        <td><span>事故地点：</span></td>
        <td style="text-align: left;">
          <input type="number" nz-input style="width: 35.29px;padding-left: 4px;padding-right: 0px;"
            [slValidate]="{required:true,label:'纬度（度）'}" name="d" [(ngModel)]="coordinate.lat.d" />
          <sup>°</sup>
          <input type="number" nz-input style="width: 35.29px;padding-left: 4px;padding-right: 0px;"
            [slValidate]="{required:true,label:'纬度（分）'}" name="m" [(ngModel)]="coordinate.lat.m" />
          <sup>′</sup>
          <input type="number" nz-input style="width: 60px;padding-left: 4px;padding-right: 0px;"
            [slValidate]="{required:true,label:'纬度（秒）'}" name="s" [(ngModel)]="coordinate.lat.s" />
          <sup>″</sup>
          <span class="unit" style="margin-left: 4px;margin-right: 6px;">N</span>
          <input type="number" nz-input style="width: 35.29px;padding-left: 4px;padding-right: 0px;"
            [slValidate]="{required:true,label:'经度（度）'}" name="d2" [(ngModel)]="coordinate.lng.d" />
          <sup>°</sup>
          <input type="number" nz-input style="width: 35.29px;padding-left: 4px;padding-right: 0px;"
            [slValidate]="{required:true,label:'经度（分）'}" name="m2" [(ngModel)]="coordinate.lng.m" />
          <sup>′</sup>
          <input type="number" nz-input style="width: 60px;padding-left: 4px;padding-right: 0px;"
            [slValidate]="{required:true,label:'经度（秒）'}" name="s2" [(ngModel)]="coordinate.lng.s" />
          <sup>″</sup>
          <span class="unit" style="margin-left: 4px;">E</span>
          <div style="display: inline-block;margin-left: 10px;">
            <i class="sl-map-point"></i>
            <span style="margin-left: 4px;color: #2D74EF;"><a (click)="getPoint()">海图取点</a></span>
          </div>
        </td>


      </tr>
      <tr>
        <td style="text-align: right;padding-right: 10px;">
          关联保护区：
        </td>
        <td><input placeholder="系统自动录入" style="color:#333333 ;" type="text" [disabled]="true" nz-input
            [(ngModel)]="accident.areaName" name="areaName"></td>
      </tr>
      <tr>
        <td style="text-align: right;padding-right: 10px;">事故描述：</td>
        <td>
          <textarea type="text" nz-input [(ngModel)]="accident.description" name="description"
            style="margin-top: 4px;padding-bottom: 50px;"></textarea>
        </td>
      </tr>
    </table>

  </form>
  <div class="content-bottom">
    <button sl-button slType="default" (click)="cancel()">取消</button>
    <button sl-button slType="primary" style="margin-left: 24px;" (click)="save(validateForm)">保存</button>
  </div>
</div>
