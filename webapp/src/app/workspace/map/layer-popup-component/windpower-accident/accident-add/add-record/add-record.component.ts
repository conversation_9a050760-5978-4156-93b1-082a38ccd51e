import { MapLayerComponentService } from './../../../../service/map-layer-component.service';
import { NgForm } from '@angular/forms';
import {
  Component,
  OnInit,
  ViewChild,
  AfterViewInit,
  Input,
  OnDestroy,
  EventEmitter,
  Output,
} from '@angular/core';
import { TblAccident } from 'src/app/workspace/workspace-shared/models/tbl_accident';
import { SlValidateService } from 'src/app/shared/modules/sl-validate/sl-validate.service';
import { CoordinatePackage } from 'src/app/shared/models/coordinate-package.model';
import { transFormPosToNumber } from 'src/app/workspace/map/utils/map-tools';
import { MapDrawToolService } from '../../../map-draw-tool/map-draw-tool.service';
import { MapToolState } from '../../../map-draw-tool/map-tool';
import { Subscription } from 'rxjs';
import * as L from 'leaflet';
import { WindpowerAccidentService } from '../../windpower-accident.service';
import { Account } from 'src/app/shared/models/account.model';
import * as moment from 'moment';
@Component({
  selector: 'app-add-record',
  templateUrl: './add-record.component.html',
  styleUrls: ['./add-record.component.less'],
})
export class AddRecordComponent implements OnInit, AfterViewInit, OnDestroy {
  @Input() accident: TblAccident = new TblAccident();
  @Output() onSave: EventEmitter<boolean> = new EventEmitter();
  account: Account = new Account();
  // 海图取点坐标
  coordinate: CoordinatePackage = new CoordinatePackage();
  mapToolSub: Subscription = new Subscription();
  // 事故地点坐标
  constructor(
    private slValidateService: SlValidateService,
    private mapLayerComponentService: MapLayerComponentService,
    private drawToolService: MapDrawToolService,
    private service: WindpowerAccidentService
  ) {}
  ngOnInit(): void {
    this.mapToolSub = this.drawToolService.change$.subscribe(
      (res: MapToolState) => {
        // 转换坐标 成 CoordiangePackage
        const { latlng } = res;
        if (latlng) {
          const cp = this.latlngToCoordinatePackage(res.latlng);
          this.coordinate = cp;
          this.accident.latitude = this.coordinate.lat.value;
          this.accident.longitude = this.coordinate.lng.value;
          this.service
            .getProtectAre({
              lat: this.accident.latitude,
              lng: this.accident.longitude,
            })
            .then((res: any) => {
              // this.accident.areaName = res.areaName!;
              if (res) {
                this.accident.areaName = res.areaName!;
                this.accident.areaId = res.id!;
              } else {
                this.accident.areaName = '没有关联保护区';
              }
            });
        }
      }
    );
    if (this.accident.accidentTime) {
      this.accident.accidentTimePage = new Date(this.accident.accidentTime);
      var LatLng = L.latLng(this.accident.latitude!, this.accident.longitude!);
      this.coordinate = this.latlngToCoordinatePackage(LatLng);
    }
  }

  ngOnDestroy(): void {
    this.mapLayerComponentService.removeComponent({
      name: 'MapToolPointComponent',
    });
  }
  currentPointIcon: string = 'red';
  @ViewChild('validateForm') validateForm!: NgForm;

  ngAfterViewInit(): void {}

  save(validateForm: NgForm) {
    this.accident.accidentTime = moment(this.accident.accidentTimePage).format(
      'YYYY-MM-DD HH:mm'
    );
    const isValidate =
      this.slValidateService.validateFormWithAlert(validateForm);
    if (this.accident.sysRecordCreated) {
      this.accident.sysRecordCreated = moment(new Date()).format(
        'YYYY-MM-DD HH:mm'
      );
    } else {
      this.accident.sysRecordCreated = moment(new Date()).format(
        'YYYY-MM-DD HH:mm'
      );
    }
    if (isValidate) {
      this.onSave.emit(isValidate);
    }
  }

  getPoint() {
    this.mapLayerComponentService.addComponent({
      type: 'layer',
      name: 'MapToolPointComponent',
      data: {
        params: {
          iconUrl: `/assets/img/map/accident/accident-point.png`,
          iconSize: [28, 34],
        },
      },
    });
  }

  cancel() {
    this.service.closePopup('AccidentAddComponent');
  }
  /**
   * 将坐标数转换为CoordinatePackage
   */
  private latlngToCoordinatePackage(latlng: L.LatLng) {
    const item: CoordinatePackage = new CoordinatePackage();
    item.lat = transFormPosToNumber(latlng.lat);
    item.lng = transFormPosToNumber(latlng.lng);
    if (item.lat) {
      item.lat.value = latlng.lat;
      item.lat.type = latlng.lat >= 0 ? 'N' : 'S';
    }
    if (item.lng) {
      item.lng.value = latlng.lng;
      item.lng.type = latlng.lng >= 0 ? 'E' : 'W';
    }
    item.latlng = latlng;
    return item;
  }
}
