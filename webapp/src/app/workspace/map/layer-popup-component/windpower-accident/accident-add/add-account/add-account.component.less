.add-accident {
  font-family: SourceHanSansCN-Regular;
  font-size: 14px;
  color: #666666;

  tr {
    line-height: 38px;

    input {
      width: 406px;
    }

  }
}

.tittle {
  padding-left: 20px;
  line-height: 43px;
  font-family: SourceHanSansCN-Medium;
  font-size: 16px;
  color: #333333;
  letter-spacing: 0;
  font-weight: 500;

}

.unit {
  width: 8px;
  height: 12px;
  font-family: SourceHanSansCN-Regular;
  font-size: 12px;
  color: #333333;
  letter-spacing: 0;
  text-align: center;
  font-weight: 400;
}

.inpu-des {
  position: absolute;
  left: 104px;
  top: 132px;
  width: 406px;
  height: 72px;
  background: #FFFFFF;
  border: 1px solid rgba(205, 210, 218, 1);
  border-radius: 2px;
  padding-bottom: 51px;
}

span::before {
  content: "*";
  color: red;
  width: 10px;
  height: 10px;
  margin-right: 4px;
  display: inline-block;
}

.content-bottom {
  position: absolute;
  width: 534px;
  bottom: 0;
  border-top: 1px solid #eff1f6;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 55px;
}
