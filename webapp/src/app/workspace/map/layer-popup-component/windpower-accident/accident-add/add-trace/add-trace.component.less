@import "/src/styles/mixins";

.add-accident {
  font-family: SourceHanSansCN-Regular;
  font-size: 14px;
  color: #666666;

  tr {
    text-align: center;
    line-height: 38px;

    td:first-child {

      span::before {
        content: "*";
        color: red;
        width: 10px;
        height: 10px;
        margin-right: 4px;
        display: inline-block;
      }

    }

    input {
      width: 406px;
    }

  }
}

.tittle {
  padding-left: 20px;
  line-height: 43px;
  font-family: SourceHanSansCN-Medium;
  font-size: 16px;
  color: #333333;
  letter-spacing: 0;
  font-weight: 500;
  z-index: 1;
}

.unit {
  width: 8px;
  height: 12px;
  font-family: SourceHanSansCN-Regular;
  font-size: 12px;
  color: #333333;
  letter-spacing: 0;
  text-align: center;
  font-weight: 400;
}

.inpu-des {
  position: absolute;
  left: 104px;
  width: 406px;
  height: 72px;
  background: #FFFFFF;
  border: 1px solid rgba(205, 210, 218, 1);
  border-radius: 2px;
}

.meter {
  width: 14px;
  height: 14px;
  font-family: SourceHanSansCN-Regular;
  font-size: 14px;
  color: #333333;
  letter-spacing: 0;
  text-align: right;
  font-weight: 400;
}

.ship-list {
  text-align: center;

  tr {
    border-top: 1px solid rgba(215, 219, 232, 1);
    border-bottom: 1px solid rgba(215, 219, 232, 1);
  }

  td {
    border-left: 1px solid rgba(215, 219, 232, 1);

    &:last-child {
      border-right: 1px solid rgba(215, 219, 232, 1);
    }
  }

  .title {
    height: 30px;
    width: 484px;

    td {
      background: #E7F1FF;
    }
  }


}

.line {
  width: 14px;
  height: 14px;
}

.special {
  position: absolute;
  line-height: 38px;
  left: 10px;

  span::before {
    content: "*";
    color: red;
    width: 10px;
    height: 10px;
    margin-right: 4px;
    display: inline-block;
  }

}

.content-bottom {
  width: 534px;
  position: absolute;
  bottom: 0;

  border-top: 1px solid #eff1f6;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 55px;
}

.table-wrapper {
  position: absolute;
  height: 211px;
  top: 163px;
  left: 24px;
  // overflow: hidden;
  .scrollbars(5px, #C8C9CC, rgba(0, 0, 0, 0));

  .coordinate-container-inner {
    overflow-y: overlay;
    height: 100%;
  }
}
