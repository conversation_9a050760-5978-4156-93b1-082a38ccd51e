<div>
  <div class="tittle">
    事故追责：
  </div>
  <form #validateForm="ngForm">
    <table class="add-accident">
      <tr>
        <td style="text-align: right;padding-right: 10px;width: 104px;"><span>追责结果：</span></td>
        <td style="text-align: left;">
          <nz-radio-group nzSize="small" [slValidate]="{required:true,label:'追责结果'}" [(ngModel)]="accident.result"
            name="radio">
            <label nz-radio nzValue="0">成功</label>
            <label nz-radio nzValue="1">失败</label>
          </nz-radio-group>
        </td>
      </tr>
      <!-- 成功 -->
      <ng-template #success>
        <tr>
          <td style="text-align: right;padding-right: 10px"><span>肇事船舶：</span></td>
          <td>
            <!-- <nz-select [slValidate]="{required:true,label:'肇事船舶'}" style="width: 406px; text-align: left;"
              [nzMaxTagCount]="3" nzMode="multiple" nzPlaceHolder="选择肇事船舶" [(ngModel)]="shipNames" name="first">
              <nz-option *ngFor="let item of accident['accidentShipList']" [nzLabel]="item.shipName!"
                [nzValue]="item.shipName"></nz-option>
              <nz-option *ngFor="let item of shipNames" [nzLabel]="item" [nzValue]="item" nzHide="true"></nz-option>
            </nz-select> -->
            <nz-select style="width: 406px; text-align: left;" [nzMaxTagCount]="3" nzMode="multiple"
              nzPlaceHolder="选择肇事船舶" [slValidate]="{required:true,label:'肇事船舶'}" name="first"
              [(ngModel)]="selectShipMmsis">
              <nz-option *ngFor="let item of remarkShipList" [nzLabel]="item.shipName!||item.mmsi!"
                [nzValue]="item.mmsi">
              </nz-option>
              <!-- [slValidate]="{required:true,label:'肇事船舶'}" -->
              <!-- <nz-option *ngFor="let item of selectName" [nzLabel]="item" nzHide="true"></nz-option> -->
            </nz-select>
          </td>
        </tr>
        <tr style="height: 82px; vertical-align: top; margin-top: 5px;">
          <td style="text-align: right;padding-right: 10px;padding-bottom: 40px;">事故原因：</td>
          <td>
            <textarea style="width: 406px;height: 72px;padding-bottom: 20px;margin-top: 4px;" nz-input
              placeholder="输入事故原因" [(ngModel)]="accident.reason" name="description"></textarea>
          </td>
        </tr>
        <tr>
          <td style="text-align: right;padding-right: 10px">处理时间：</td>
          <td>
            <!-- <nz-date-picker style="width: 406px;" [(ngModel)]="accident.sysCreated" name="sysCreated"></nz-date-picker> -->
            <nz-date-picker style="width: 406px;" placeholder="请选择处理时间" [nzShowTime]="{ nzFormat: 'HH:mm'}"
              nzFormat="yyyy-MM-dd HH:mm" [(ngModel)]="accident.dealTimePage" name="dealTime"
              (ngModelChange)="dateChange()">
            </nz-date-picker>
          </td>
        </tr>
        <tr>
          <td style="text-align: right;padding-right: 10px">处理人：</td>
          <td>

            <input type="text" placeholder="请输入处理人" nz-input style="width: 406px; text-align: left;"
              [(ngModel)]="accident.dealName" name="userName">
          </td>
        </tr>
        <tr style="height: 82px;">
          <td style="padding-bottom: 40px;text-align: right;padding-right: 10px">处理结果：</td>
          <td><textarea style="width: 406px;height: 72px;padding-bottom: 40px;" nz-input placeholder="输入处理结果"
              [(ngModel)]="accident.dealReason" name="dealReason">{{accident.dealReason}}</textarea></td>
        </tr>
      </ng-template>

      <!-- 失败 -->
      <ng-container *ngIf="accident.result == '1';else success">
        <tr>
          <td style="text-align: right;padding-right: 10px"><span>可疑船舶：</span></td>
          <td>
            <nz-select [slValidate]="{required:true,label:'可疑船舶'}" style="width: 406px; text-align: left;"
              [nzMaxTagCount]="3" nzMode="multiple" nzPlaceHolder="选择可疑船舶" name="next" [(ngModel)]="selectShipMmsis">
              <nz-option *ngFor="let item of remarkShipList" [nzLabel]="item.shipName!||item.mmsi!"
                [nzValue]="item.mmsi">
              </nz-option>
              <!-- <nz-option *ngFor="let item of selectName" [nzLabel]="item" nzHide="true"></nz-option> -->
            </nz-select>
          </td>
        </tr>
        <tr style="height: 82px;">
          <td style="padding-bottom: 25px;text-align: right;padding-right: 10px;vertical-align: top;">失败原因：</td>
          <td>
            <textarea style="width: 406px;height: 72px;padding-bottom: 20px;" nz-input placeholder="输入失败原因"
              [(ngModel)]="accident.reason" name="description"></textarea>
          </td>
        </tr>
      </ng-container>


    </table>
    <div class="content-bottom">
      <button sl-button slType="danger" *slHasAnyAuthority="'1.3.3'" (click)="delete(accident.id!)">删除</button>
      <button sl-button slType="default" style="margin-left: 24px;" (click)=" cancel()">取消</button>
      <button sl-button slType="primary" style="margin-left: 24px;" (click)="save(validateForm)">保存</button>
    </div>
  </form>


</div>
