<div style="padding-right: 24px">
  <div class="tittle">
    事故追查:
  </div>
  <form #validateForm="ngForm">
    <table class="add-accident">
      <tr>
        <td style="width: 104px;"><span>追查半径：</span></td>
        <td> <input type="number" style="width: 368px;" placeholder="请输入追查半径"
            [slValidate]="{required:true,label:'追查半径'}" name="rail" nz-input [(ngModel)]="accident.rail"
            (keyup)="radiusChange()" /><span style="margin-left: 23px;" class="meter">米</span>
        </td>
      </tr>
      <tr>
        <td><span>追查时间：</span></td>
        <td style="text-align: left;">
          <nz-date-picker style="width: 150px;height: 28px;" [slValidate]="{required:true,label:'开始时间'}"
            [(ngModel)]="accident.startTimePage" nzFormat="yyyy-MM-dd" name="startTime"
            [nzShowTime]="{ nzFormat: 'HH:mm'}">
          </nz-date-picker>
          <span style="font-size: 20px;margin: 0 4px;">~</span>
          <nz-date-picker style="width: 150px;height: 28px;" [slValidate]="{required:true,label:'结束时间'}"
            [(ngModel)]="accident.endTimePage" nzFormat="yyyy-MM-dd" [nzShowTime]="{ nzFormat: 'HH:mm'}" name="endTime">
          </nz-date-picker>

          <button sl-button slType="success" style="position: absolute; top: 89px;left: 438px;line-height: 0%;"
            (click)="startTrace(validateForm)">开始追查</button>
        </td>
      </tr>
      <td class="special"><span>可疑船舶列表</span>

      </td>
      <!-- 1.有一个不满足都不出现 -->
      <!-- <button [disabled]="isDisabled||selectShipList.length==0" [class.disabled]="selectShipList.length==0" sl-button
        slType="warn" style="position: absolute; top: 126px;left: 438px;line-height: 0%;" (click)="startTrackLayer()"
        name="btn">轨迹回放</button> -->

      <button [disabled]="selectShipList.length==0" [class.disabled]="selectShipList.length==0" sl-button slType="warn"
        style="position: absolute; top: 126px;left: 438px;line-height: 0%;" (click)="startTrackLayer()"
        name="btn">轨迹回放</button>
    </table>
  </form>


  <div class="table-wrapper">
    <table class="ship-list">
      <tr class="title">
        <td style="width: 30px;">
          <!-- <label sl-checkbox style="margin-left: 8px;"></label> -->
          <label [nzDisabled]="notSelectabled" [class.sl-ant-disabled-checkbox]="notSelectabled" name="allSelected"
            [ngModel]="isSelectAll" [nzIndeterminate]="isSomeSelected" nz-checkbox
            (ngModelChange)="selectAll($event)"></label>
        </td>
        <td style="width: 40px">序号</td>
        <td style="width: 197px">船舶名称</td>
        <td style="width: 82px">MMSI</td>
        <td style="width: 64px">距离(米)</td>
        <td style="width: 71px">嫌疑船舶</td>
      </tr>
    </table>
    <nz-spin [nzSpinning]="spinning" [nzTip]="'数据加载中...'">
      <div style="height: 180px; overflow: hidden;">
        <div class="coordinate-container-inner">
          <table class="ship-list">
            <ng-container *ngFor="let item of accident.accidentShipList;index as idx">
              <tr style=" height: 30px;">
                <td style="width: 30px;">
                  <label nz-checkbox [nzDisabled]="notSelectabled" [class.sl-ant-disabled-checkbox]="notSelectabled"
                    [(nzChecked)]="item.ifCheck " (nzCheckedChange)="checkChange($event,item)"></label>
                </td>
                <td style="width: 40px">
                  {{idx+1}}
                </td>
                <td style="width: 197px">
                  {{item.shipName ||item.mmsi }}
                </td>
                <td style="width: 82px">
                  {{item.mmsi}}
                </td>
                <td style="width: 64px">
                  {{item.distance}}
                </td>
                <td style="width: 71px">
                  <label [(nzChecked)]="item.remark" (nzCheckedChange)="checkChange2($event,item)"
                    style="margin-right: 4px;" nz-checkbox></label>跟踪
                </td>
              </tr>
            </ng-container>
          </table>
          <ng-container *ngIf="accident.accidentShipList || accident.accidentShipList == undefined">
            <nz-empty *ngIf="accident.accidentShipList == undefined || accident.accidentShipList.length == 0">
            </nz-empty>
          </ng-container>
        </div>

      </div>
    </nz-spin>
  </div>

  <div class="content-bottom">
    <button sl-button slType="default" (click)="cancel()">取消</button>
    <button sl-button slType="primary" style="margin-left: 24px;" (click)="save(validateForm)">保存</button>
  </div>
</div>
