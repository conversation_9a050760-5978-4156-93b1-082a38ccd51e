import { BaseSearch } from 'src/app/shared/models';
import { TblAccidentship } from 'src/app/workspace/workspace-shared/models/tbl_accidentship';
import { WindpowerAccidentService } from './../../windpower-accident.service';
import { NgForm } from '@angular/forms';
import {
  Component,
  Input,
  OnInit,
  ViewChild,
  OnDestroy,
  Output,
  EventEmitter,
} from '@angular/core';
import { TblAccident } from 'src/app/workspace/workspace-shared/models/tbl_accident';
import { SlValidateService } from 'src/app/shared/modules/sl-validate/sl-validate.service';
import * as moment from 'moment';
import { SlModalService } from 'src/app/shared/modules/sl-modal/sl-modal.service';
import { Subscription } from 'rxjs';
@Component({
  selector: 'app-add-account',
  templateUrl: './add-account.component.html',
  styleUrls: ['./add-account.component.less'],
})
export class AddAccountComponent implements OnInit, OnDestroy {
  currentIndex: number = 0;
  @Input() accident: TblAccident = new TblAccident();
  // 选中的船列表的mmsi
  selectShipMmsis: Array<string> = [];
  // 显示默认值列表
  shipList: Array<TblAccidentship> = [];
  baseSearch = new BaseSearch();
  // 跟踪船列表
  subAccidentShip = new Subscription();
  traceShipList: TblAccidentship[] = [];
  // 让初始选中列表制空
  isShowSelectShip: boolean = false;
  subGetvalue: Subscription = new Subscription();

  @ViewChild('validateForm') validateForm!: NgForm;

  ngAfterViewInit(): void {}
  constructor(
    private slValidateService: SlValidateService,
    private service: WindpowerAccidentService,
    private slModalService: SlModalService
  ) {
    this.subGetvalue = this.service.valueId$.subscribe((res) => {
      this.isShowSelectShip = res;
      if (this.isShowSelectShip) {
        this.selectShipMmsis = [];
      }
    });
  }

  ngOnInit() {
    // if (!this.accident.result) {
    //   this.accident.result = '0';
    // }
    if (this.accident.dealTime) {
      this.accident.dealTimePage = new Date(this.accident.dealTime);
    } else {
      this.accident.dealTimePage = null!;
    }
    if (this.accident.shipMmsis) {
      this.selectShipMmsis = this.accident.shipMmsis!.split(',');
    }
  }

  ngOnDestroy() {
    this.subGetvalue.unsubscribe();
  }
  @Output() onSave: EventEmitter<boolean> = new EventEmitter();

  get remarkShipList() {
    return this.accident.accidentShipList
      ? this.accident.accidentShipList.filter((ele) => ele.ifDubious == '1')
      : [];
  }

  save(validateForm: NgForm) {
    if (this.accident.dealTimePage === null) {
      this.accident.dealTime = '';
      this.accident.dealTimePage = null!;
    }
    if (this.accident.result === '1') {
      this.accident.dealTime = '';
      this.accident.dealTimePage = new Date(this.accident.dealTime);
      this.accident.dealName = '';
      this.accident.dealReason = '';
    }
    const isValidate =
      this.slValidateService.validateFormWithAlert(validateForm);
    if (isValidate) {
      this.accident.shipMmsis = this.selectShipMmsis.join(',');
      this.accident.shipNames = this.accident
        .accidentShipList!.filter((ele) =>
          this.selectShipMmsis.includes(ele.mmsi!)
        )
        .map((ele) => ele.shipName)
        .join(',');
      this.slModalService.openPromptModal({
        type: 'confirm',
        content: '确定保存吗？',
        okCb: () => {
          this.onSave.emit(isValidate);
          this.service.removePopup('AccidentAddComponent');
        },
      });
      // this.accident.shipMmsis = this.selectShipMmsis.join(',');
      if (!this.accident.sysResultCreated) {
        this.accident.sysResultCreated = moment(new Date()).format(
          'YYYY-MM-DD HH:mm'
        );
      } else {
        this.accident.sysResultCreated = moment(new Date()).format(
          'YYYY-MM-DD HH:mm'
        );
      }
    }
  }
  cancel() {
    this.service.closePopup('AccidentAddComponent');
  }
  // 删除
  delete(id: string) {
    this.service.deleteData(id, 'AccidentAddComponent');
  }

  dateChange() {
    this.accident.dealTime = moment(this.accident.dealTimePage).format(
      'YYYY-MM-DD HH:mm'
    );
  }
}
