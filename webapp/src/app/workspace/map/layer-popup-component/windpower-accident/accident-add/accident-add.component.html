<div class="wrapper-bar">

  <div class="bottom-bar">
    <div class="lef-list">
      <ul>
        <li *ngFor="let item of tabList1; index as idx" [class.acitive]="currentIndex == idx">
          <a href="javascript:void(0)" class="text" (click)="switchTab(item,idx)">{{item.title}}</a>
        </li>
      </ul>
    </div>


    <div class="right-content">
      <div class="content-top">
        <!--key 不等于自身 hidden-->
        <div [hidden]="currentTabItem?.key!=='record'">
          <app-add-record [accident]="accident" (onSave)="onSave($event,'record')"></app-add-record>
        </div>
        <div [hidden]="currentTabItem?.key!=='trace'">
          <app-add-trace [accident]="accident" (onSave)="onSave($event,'trace')"></app-add-trace>
        </div>
        <div [hidden]="currentTabItem?.key!=='account'">
          <app-add-account [accident]="accident" (onSave)="onSave($event,'account')">
          </app-add-account>
        </div>
        <!-- <ng-container *ngFor="let item of dynamicList">
            <div [hidden]="currentDynamic.name!=item.name">
              <app-dynamic-compopnent [dynamicCom]="item"></app-dynamic-compopnent>
            </div>
          </ng-container> -->
      </div>
    </div>
  </div>
</div>
