.add-accident {
  font-family: SourceHanSansCN-Regular;
  font-size: 14px;
  color: #666666;
  tr {
    text-align: center;
    line-height: 38px;
    input {
      width: 406px;
    }
    
    td:first-child {
     
      span::before {
        content: "*";
        color: red;
        width: 10px;
        height: 10px;
        margin-right: 4px;
        display: inline-block;
      }
    
  }
    
  }
  tr:last-child {
    vertical-align: top;
    height: 92px;
  }
}
.tittle {
  padding-left: 20px;
  line-height: 43px;
  font-family: SourceHanSansCN-Medium;
  font-size: 16px;
  color: #333333;
  letter-spacing: 0;
  font-weight: 500;
 
}
.unit {
  width: 8px;
height: 12px;
font-family: SourceHanSansCN-Regular;
font-size: 12px;
color: #333333;
letter-spacing: 0;
text-align: center;
font-weight: 400;
}
.content-bottom {
  position: absolute;
  border-top: 1px solid #eff1f6;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 55px;
  bottom: 0;
  width: 534px;
}



