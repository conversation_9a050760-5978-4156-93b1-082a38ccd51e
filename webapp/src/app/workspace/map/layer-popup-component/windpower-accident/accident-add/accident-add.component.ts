import { TblAccident } from './../../../../workspace-shared/models/tbl_accident';
import { Component, Input, OnInit, ViewChild, OnDestroy } from '@angular/core';
import { TabContainerDirective } from '../../../../../shared/directives/tab-container.directive';
import { NgForm } from '@angular/forms';
// 动态组件数据类型
import {
  Account,
  BaseSearch,
  DynamicComponent,
  DynamicComponentData,
} from 'src/app/shared/models';
import { WindpowerAccidentService } from '../windpower-accident.service';
import { MapLayerComponentService } from '../../../service/map-layer-component.service';
import { Principal } from 'src/app/shared/services/principal.service';
import { SlModalService } from 'src/app/shared/modules/sl-modal/sl-modal.service';

@Component({
  selector: 'app-accident-add',
  templateUrl: './accident-add.component.html',
  styleUrls: ['./accident-add.component.less'],
})
export class AccidentAddComponent implements OnInit, OnDestroy {
  // 表单预校验
  form!: NgForm;
  // 已经加载的动态组件
  dynamicList: Array<DynamicComponent> = [];
  // 加载中状态...
  spinning: boolean = true;
  currentIndex: number = 0;
  accident: TblAccident = new TblAccident();
  account: Account = new Account();
  tabList1: Array<any> = [
    {
      title: '记录',
      index: 0,
      key: 'record',
      state: false,
    },
    {
      title: '追查',
      key: 'trace',
      index: 1,
      state: false,
    },
    {
      title: '追责',
      key: 'account',
      index: 2,
      state: false,
    },
  ];
  @Input() data: DynamicComponentData = new DynamicComponentData();
  @ViewChild(NgForm) validateForm!: NgForm;
  @ViewChild(TabContainerDirective) tabHost!: TabContainerDirective;
  constructor(
    private service: WindpowerAccidentService,
    private mapLayerComponentService: MapLayerComponentService,
    private principal: Principal,
    private slModalService: SlModalService
  ) {}
  ngOnInit() {
    this.spinning = false;
    this.principal.identity().then((res) => {
      this.account = res || {};
    });
    const params = this.data.params;
    if (params && params.editAccident) {
      this.accident = params.editAccident;
      this.tabList1.forEach((item) => {
        item.state = true;
      });
    }
  }
  /**
   * tab 标签页切换
   * @param item
   * @param idx
   */
  switchTab(item: any, idx: number) {
    if (!item.state) return;
    this.currentIndex = idx;
  }

  onSave(isValidate: boolean, key: string) {
    const item = this.tabList1.find((ele) => ele.key === key);
    if (item) {
      this.tabList1.forEach((item) => {
        item.state = true;
      });
    }

    if (isValidate) {
      this.currentIndex++;
      this.save();
      // console.log(this.data.params);
    }
  }

  // 当前选中的tab标签
  get currentTabItem() {
    return this.tabList1[this.currentIndex];
  }

  ngOnDestroy(): void {
    this.mapLayerComponentService.removeAllByName(
      'MapToolCircleLayerComponent'
    );
    // 关闭轨迹图层
    this.mapLayerComponentService.removeAllByName(
      'TrajectoryTimeControllerComponent'
    );
    this.mapLayerComponentService.removeAllByName(
      'ShipTrajectoryLayerComponent'
    );
  }

  save() {
    this.accident.userName = this.account.userName;
    this.accident.userId = this.account.id;
    this.service
      .save2(this.accident)
      .then((id) => {
        this.accident.id = id;

        // if (this.isEdit) {
        //   this.mapLayerComponentService.refreshComponent({
        //     name: 'AccidentMainComponent',
        //     onOpenCb: () => {
        //       this.service.removePopup('AccidentAddComponent');
        //     },
        //   });
        // } else {

        // }
        this.mapLayerComponentService.refreshComponent({
          name: 'AccidentMainComponent',
        });
        this.mapLayerComponentService.refreshComponent({
          name: 'WindpowerAccidentLayerComponent',
        });
      })
      .catch((err) => {
        this.slModalService.openPromptModal({
          type: 'error',
          content: err && '保存失败',
        });
      });
  }
}
