import { TblAccidentship } from './../../../../../workspace-shared/models/tbl_accidentship';
import {
  Component,
  OnInit,
  ViewChild,
  Input,
  OnDestroy,
  EventEmitter,
  Output,
} from '@angular/core';
import { NgForm } from '@angular/forms';
import * as moment from 'moment';
import { SlValidateService } from 'src/app/shared/modules/sl-validate/sl-validate.service';
import { TblAccident } from 'src/app/workspace/workspace-shared/models/tbl_accident';
import { WindpowerAccidentService } from '../../windpower-accident.service';
import { ShipTrajectoryService } from '../../../map-ship-trajectory/ship-trajectory.service';
import * as _ from 'lodash';
import { MapLayerComponentService } from 'src/app/workspace/map/service/map-layer-component.service';
import * as L from 'leaflet';

@Component({
  selector: 'app-add-trace',
  templateUrl: './add-trace.component.html',
  styleUrls: ['./add-trace.component.less'],
})
export class AddTraceComponent implements OnInit, OnDestroy {
  @Input() accident: TblAccident = new TblAccident();
  spinning: boolean = false;
  ngAfterViewInit(): void {}
  // 不可选择
  notSelectabled: boolean = false;
  // 是否全选
  isSelectAll: boolean = false;
  isSomeSelected: boolean = false; // 是否部分选中
  // 选中的状态数组
  selectedModuleList: Array<any> = [];
  // selectShipList: Array<TblAccidentship> = [];
  selectShipList: Array<any> = [];
  // 选中的跟踪船舶数组
  selectTraceList: Array<TblAccidentship> = [];
  // 船舶轨迹数组
  shipTra: Array<TblAccidentship> = [];
  @ViewChild('validateForm') validateForm!: NgForm;
  constructor(
    private slValidateService: SlValidateService,
    private service: WindpowerAccidentService,
    private shipTrajectoryService: ShipTrajectoryService,
    private mapLayerComponentService: MapLayerComponentService
  ) {}
  ngOnInit() {
    if (
      this.accident.accidentShipList &&
      this.accident.accidentShipList.length
    ) {
      this.accident.accidentShipList.forEach((ele) => {
        if (ele.ifDubious == '1') {
          ele.remark = true;
        }
      });
    }

    if (this.accident.startTime && this.accident.endTime) {
      this.accident.startTimePage = new Date(this.accident.startTime);
      this.accident.endTimePage = new Date(this.accident.endTime);
    }
    console.log(this.accident.accidentShipList);
  }
  ngOnDestroy(): void {}
  // 追查
  startTrace(validateForm: NgForm) {
    const isValidate =
      this.slValidateService.validateFormWithAlert(validateForm);
    if (isValidate) {
      this.spinning = true;
      this.getShips();
    }
  }

  // 获取可疑船舶列表
  getShips() {
    this.accident.startTime = moment(this.accident.startTimePage).format(
      'YYYY-MM-DD HH:MM:ss'
    );
    this.accident.endTime = moment(this.accident.endTimePage).format(
      'YYYY-MM-DD HH:MM:ss'
    );
    let search = {
      endTime: this.accident.endTime,
      lat: this.accident.latitude,
      lng: this.accident.longitude,
      rail: this.accident.rail,
      startTime: this.accident.startTime,
    };
    this.service
      .getShips(search)
      .then((res) => {
        if (res) {
          this.accident.accidentShipList = res;
          this.spinning = false;
        }
      })
      .catch((res) => {
        this.spinning = false;
      });
  }
  @Output() onSave: EventEmitter<boolean> = new EventEmitter();

  save(validateForm: NgForm) {
    this.accident.startTime = moment(this.accident.startTimePage).format(
      'YYYY-MM-DD HH:MM:ss'
    );
    this.accident.endTime = moment(this.accident.endTimePage).format(
      'YYYY-MM-DD HH:MM:ss'
    );
    const isValidate =
      this.slValidateService.validateFormWithAlert(validateForm);
    if (isValidate) {
      this.onSave.emit(isValidate);
    }
  }
  cancel() {
    this.service.closePopup('AccidentAddComponent');
  }

  /**
   *
   * 全选/反选
   *
   */
  selectAll($event: boolean) {
    if (this.notSelectabled) return;
    this.isSelectAll = $event;
    this.isSomeSelected = false;

    this.accident.accidentShipList!.forEach((ele) => {
      ele.ifCheck = this.isSelectAll;
    });

    this.selectShipList = this.accident.accidentShipList!.filter(
      (ele) => ele.ifCheck
    );
  }

  checkChange($event: boolean, item: TblAccidentship) {
    item.ifCheck = $event;
    this.isSomeSelected = this.accident.accidentShipList!.some(
      (ele) => ele.ifCheck
    );
    this.isSelectAll = this.accident.accidentShipList!.every(
      (ele) => ele.ifCheck
    );
    if (this.isSelectAll) this.isSomeSelected = false;
    this.selectShipList = this.accident.accidentShipList!.filter(
      (ele) => ele.ifCheck
    );
    // 2
    // let count: number = 0;
    // const len = this.shipList.length;
    // this.selectShipList = [];
    // for (let i = 0; i < len; i++) {
    //   const item = this.shipList[i];
    //   if (item.ifCheck) {
    //     this.selectShipList.push(item);
    //     count++;
    //   }
    // }
    // this.isSelectAll = count == len;
    // this.isSomeSelected = count > 0 && !this.isSelectAll;
  }
  // 船只跟踪
  checkChange2($event: boolean, item: TblAccidentship) {
    item.remark = $event;
    this.accident.shipMmsis = '';
    this.service.valueId$.next(true);
    this.accident.accidentShipList?.forEach((item) => {
      if (item.remark) {
        item.ifDubious = '1';
      } else {
        item.ifDubious = '0';
      }
    });
    this.selectTraceList = this.accident.accidentShipList!.filter(
      (ele) => ele.remark
    );
  }
  // 轨迹跟踪
  startTrackLayer() {
    if (this.selectShipList.length == 0) return;
    if (this.accident.accidentShipList?.every((el) => el.trackList)) {
      this.shipTrajectoryService.addTrackLayer(
        this.selectShipList!,
        this.accident.startTime!,
        this.accident.endTime!,
        'accident',
        [{ name: 'ShipTrajectoryLayerComponent' }]
      );
    } else {
      const search = {
        endTime: this.accident.endTime + ':00',
        lat: this.accident.latitude,
        lng: this.accident.longitude,
        rail: this.accident.rail,
        startTime: this.accident.startTime + ':00',
      };
      this.spinning = true;

      this.service
        .getShips(search)
        .then((res) => {
          this.shipTra = res;
          this.spinning = false;
          if (this.shipTra) {
            this.shipTra.forEach((ele) => {
              this.selectShipList.forEach((el) => {
                if (ele.mmsi == el.mmsi) {
                  el.trackList = ele.trackList;
                }
              });
            });
          }
          this.shipTrajectoryService.addTrackLayer(
            this.selectShipList!,
            this.accident.startTime!,
            this.accident.endTime!,
            'accident',
            [{ name: 'ShipTrajectoryLayerComponent' }]
          );
        })
        .catch(() => {
          this.spinning = false;
        });
    }
  }

  /**监听半径变化 */
  radiusChange() {
    if (
      !this.accident.latitude ||
      !this.accident.longitude ||
      !this.accident.rail
    )
      return;

    const circleLayer = this.mapLayerComponentService.addComponent({
      type: 'layer',
      name: 'MapToolCircleLayerComponent',
      data: {
        params: {
          latlngs: [
            L.latLng(this.accident.latitude!, this.accident.longitude!),
          ],
          fitBounds: true,
          radius: this.accident.rail,
          option: {
            color: '#D21C1C',
            fillColor: '#FFBDBD',
            fillOpacity: '0.4',
          },
        },
      },
    });
  }
}
