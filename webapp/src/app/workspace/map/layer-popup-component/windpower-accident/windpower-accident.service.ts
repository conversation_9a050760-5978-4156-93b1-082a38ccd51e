import { Injectable } from '@angular/core';
import { BaseSearch } from 'src/app/shared/models';
import { Page } from 'src/app/shared/models/page.model';
import { BaseCURDService } from 'src/app/shared/services/base-curd.service';
import { BaseHttpService } from 'src/app/shared/services/base-http.service';
import { TblAccident } from 'src/app/workspace/workspace-shared/models/tbl_accident';
import { MapLayerComponentService } from '../../service/map-layer-component.service';
import { SlModalService } from 'src/app/shared/modules/sl-modal/sl-modal.service';
import { Subject } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class WindpowerAccidentService extends BaseCURDService<
  Page<TblAccident>
> {
  valueId$: Subject<boolean> = new Subject();
  // 把显示的活跃状态关闭
  activeClose$: Subject<boolean> = new Subject();

  constructor(
    protected http: BaseHttpService,
    private mapLayerService: MapLayerComponentService,
    private slModalService: SlModalService
  ) {
    super(http, 'api/Accident');
  }

  getList2(data: BaseSearch): Promise<Page<TblAccident>> {
    return this.http.post('api/Accident/getList', data);
  }
  save2 = (data: TblAccident): Promise<string> => {
    return this.http.post(`api/Accident/save`, data);
  };
  getInfo2 = (id: string): Promise<TblAccident> => {
    return this.http.get(`api/Accident/getInfo/${id}`);
  };
  delete2 = (id: string): Promise<TblAccident> => {
    return this.http.get(`api/Accident/delete/${id}`);
  };

  getProtectAre = (data: any): Promise<string> => {
    return this.http.post('/api/Proarea/getAreaByPoint', data);
  };
  // 获取可疑船舶列表
  // 先用any代替
  getShips(data: any): Promise<any> {
    return this.http.post(`/api/Aisposition/getAreaList`, data);
  }

  getAllAccident(): Promise<TblAccident[]> {
    return this.http.get(`/api/Accident/getAllAccident`);
  }
  /**
   * 刷新列表
   */
  refreshListComponent() {
    this.mapLayerService.refreshComponent({
      name: 'AccidentMainComponent',
    });
  }

  // 删除函数
  deleteData(id: string, componentName: string) {
    this.slModalService.openPromptModal({
      type: 'confirm',
      content: '确定删除吗？',
      okCb: () => {
        this.delete2(id)
          .then((res) => {
            this.refreshListComponent();
            this.removePopup(componentName);
            this.mapLayerService.refreshComponent({
              name: 'WindpowerAccidentLayerComponent',
            });
          })
          .catch((err) => {
            this.slModalService.openPromptModal({
              type: 'error',
              content: err && '删除失败',
            });
          });
      },
    });
  }
  /**
   * 关闭popup
   * @param componentName
   * @param hasTip  是否需要确认
   */
  closePopup(componentName: string, confirm: boolean = true) {
    if (confirm) {
      this.slModalService.openPromptModal({
        type: 'confirm',
        content: '确定取消吗？',
        okCb: () => {
          this.removePopup(componentName);
          this.activeClose$.next(true);
        },
      });
    } else {
      this.removePopup(componentName);
    }
  }
  /**
   * 直接关闭popup
   * @param componentName
   */
  removePopup(componentName: string) {
    this.mapLayerService.removeComponent({
      destroy: true,
      name: componentName,
    });
  }
}
