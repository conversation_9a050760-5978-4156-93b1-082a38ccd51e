// 船舶图标路径
const baseShipIconUrl = '/assets/img/map/ship';
// color 用来区分页面船只
export const SHIP_TYPES = [
  {
    typeCode: '1',
    name: '货船',
    icon: 'cargo-ship2',
    colorValue: '#FF0000',
    iconClass: 'sl-ship-solid-green-16'
  },
  {
    typeCode: '2',
    name: '油轮',
    icon: 'oiler2',
    colorValue: '#0000FF',
    iconClass: 'sl-ship-solid-red-16'
  },
  {
    typeCode: '3',
    name: '客船',
    icon: 'passenger-ship2',
    colorValue: '#AC00C7',
    iconClass: 'sl-ship-solid-blue-16'
  },
  {
    typeCode: '5',
    name: '渔船',
    icon: 'fisher2',
    colorValue: '#005E00',
    iconClass: 'sl-ship-solid-orange-16'
  },
  {
    typeCode: '6',
    name: '游艇',
    icon: 'yacht2',
    colorValue: '#15B3FF',
    iconClass: 'sl-ship-solid-violet-16'
  },
  {
    typeCode: '4',
    name: '高速船',
    icon: 'high-speed-ship2',
    colorValue: '#03A8A8',
    iconClass: 'sl-ship-solid-yellow-16'
  },
  {
    typeCode: '7',
    name: '拖船/特种船',
    icon: 'tugboat2',
    colorValue: '#749F00',
    iconClass: 'sl-ship-solid-bluish-16'
  },
  {
    typeCode: '8',
    name: '其他',
    icon: 'other-ship2',
    colorValue: '#959595',
    iconClass: 'sl-ship-solid-gray-16'
  },
];

/**
 * 根据id 获取
 * @param {*} typeCode
 */
export const getShipType = (typeCode: string) => {
  return SHIP_TYPES.find((ele) => ele.typeCode == typeCode);
};

/**
 * 根据id 获取
 * @param {*} typeCode
 */
export const getShipIcon = (typeCode: string) => {
  const item = getShipType(typeCode);
  const iconName = item ? item.icon : 'other-ship1';
  return `${baseShipIconUrl}/${iconName}.png`;
};


/**
 *
 * 获取低放大级别下的船舶图标
 * @export
 * @return {*} 
 */
export function getShipMinIcon() {
  return `${baseShipIconUrl}/map-min-icon.png`;
}
