<div class="hitory-track-timer">
    <div class="timer-title">
        <ng-container *ngIf="extraHandle">
            <a href="javascript:void(0)" (click)="download()" title="下载可疑船舶轨迹">
                <i class="sl-download-24"></i>
            </a>
        </ng-container>
        <!-- <a href="javascript:void(0)" title="返回" (click)="goBack()">
            <i class="sl-back-24"></i>
        </a> -->
        <a href="javascript:void(0)" (click)="close()" title="关闭">
            <i class="sl-cose-24"></i>
        </a>
    </div>
    <div class="timer-content">
        <a href="javascript:void(0)" (click)="toggle()" [title]="isPlaying?'暂停':'播放'">
            <i [ngClass]="isPlaying?'sl-pause-24':'sl-play-24'"></i>
        </a>
        <!--加上录制的52px-->
        <div class="slide-bar-wrapper" #slider>
            <div class="bar"></div>
            <div #activeBar class="bar-active"></div>
            <span #barBtn class="bar-btn">
                <span class="current-time" (click)="slideBtnClick($event)">{{currentTimeStr}}</span>
                <i class="bar-dot" (mousedown)="slideBtnMouseDown($event)" (click)="slideBtnClick($event)"></i>
            </span>
        </div>
        <!-- <a href="javascript:void(0)" (click)="record()">
            <i class="sl-video-white"></i>
            <span>{{isRecoding?'停止':'录制'}}</span>
        </a> -->
        <div class="timer-speed">
            <a href="javascript:void(0)" title="正常速度" (click)="changeSpeed(1)" [class.active]="speed==1">1.0x</a>
            <a href="javascript:void(0)" title="2倍速度" (click)="changeSpeed(2)" [class.active]="speed==2">2.0x</a>
            <a href="javascript:void(0)" title="4倍速度" (click)="changeSpeed(3)" [class.active]="speed==3">4.0x</a>
        </div>
    </div>
    <!-- <div class="timer-speed">
        <a href="javascript:void(0)" (click)="changeSpeed(1)" [class.active]="speed==1">1.0X</a>
        <a href="javascript:void(0)" (click)="changeSpeed(2)" [class.active]="speed==2">2.0X</a>
        <a href="javascript:void(0)" (click)="changeSpeed(3)" [class.active]="speed==3">4.0X</a>
    </div> -->
    <div class="timer-duration-info">
        <span>{{startTime}}</span>
        <span>{{endTime}}</span>
    </div>
</div>
<div class="replaycontent" id="replaycontent"></div>