.hitory-track-timer {
  display: flex;
  background: rgba(255, 255, 255, 80%);
  flex-direction: column;
  padding: 8px 10px;
  border-radius: 4px;
  .timer-title {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    a {
      display: flex;
      align-items: center;
    }
    a + a {
      margin-left: 10px;
    }
  }
  .timer-content {
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 6px 4px;
    .slide-bar-wrapper {
      width: 514px; // 进度条宽度 500 + 滑块的宽度 14
      position: relative;
      height: 16px;
      border-radius: 6px;

      &:hover {
        cursor: pointer;
      }

      .bar,
      .bar-active {
        position: absolute;
        top: 50%;
        height: 12px;
        margin-top: -6px;
      }
      .bar {
        width: 100%;
        background-color: #d9dde5;
        border-top-right-radius: 6px;
        border-bottom-right-radius: 6px;
      }
      .bar-active {
        background-image: linear-gradient(to right, #71a5ff, #2d74ef),
          linear-gradient(to right, #71a5ff, #2d74ef);
        border-top-left-radius: 6px;
        border-bottom-left-radius: 6px;
      }
      .bar-btn {
        display: inline-block;
        position: absolute;
        top: 0;
        left: 0;
        width: 14px;
        height: 24px;
        .current-time {
          position: absolute;
          top: -35px;
          background: #dee8fc;
          width: 116px;
          border-radius: 2px;
          left: 50%;
          margin-left: -60px;
          display: flex;
          justify-content: center;
          font-size: 14px;
          font-weight: 400;
          height: 24px;
          align-items: center;
          &::after {
            content: " ";
            display: inline-block;
            position: absolute;
            width: 0;
            height: 0;
            margin-left: 2px;
            vertical-align: middle;
            border-top: 5px solid #fff;
            border-right: 5px solid transparent;
            border-left: 5px solid transparent;
            bottom: -5px;
          }
        }
        i.bar-dot {
          display: block;
          background: url("/assets/img/map/slider.png") no-repeat;
          width: 14px;
          height: 24px;
          background-size: cover;
          // width: 4px;
          // height: 4px;
          // background: #d13c3a;
          // border-radius: 50% 50%;
          position: absolute;
          margin-top: -7px;
        }
      }
    }
    .timer-speed {
      display: flex;
      // position: absolute;
      // top: 24px;
      // right: 58px;
      a {
        color: #666;
        padding: 0 2px;
        &.active {
          color: #333;
        }
      }
    }
  }
  .timer-duration-info {
    display: flex;
    color: #666;
    width: 514px;
    margin-left: 40px;
    justify-content: space-between;
    margin-top: -5px;
  }
}
