import { AfterViewInit, Component, ElementRef, OnDestroy, OnInit, ViewChild } from '@angular/core';
import * as moment from 'moment';
import { DynamicComponent } from 'src/app/shared/models/dynamic-component.model';
import { MapLayerComponentService } from '../../../service/map-layer-component.service';
import { MapStateService } from '../../../service/map-state.service';
import * as XLSX from 'xlsx';
import { stopBubble, stopDefault } from 'src/app/shared/utils';
import * as $ from 'jquery'
@Component({
  selector: 'app-trajectory-time-controller',
  templateUrl: './trajectory-time-controller.component.html',
  styleUrls: ['./trajectory-time-controller.component.less']
})
export class TrajectoryTimeControllerComponent implements OnInit, OnDestroy, AfterViewInit {
  private isDown: boolean = false;
  @ViewChild('slider') slider!: ElementRef<any>;
  @ViewChild('activeBar') activeBar!: ElementRef<any>;
  @ViewChild('barBtn') barBtn!: ElementRef<any>;
  startTime: string = '';
  endTime: string = '';

  playInterval!: any;
  // 状态
  isPlaying: boolean = false;
  // 完成
  playComplete: boolean = false;
  // 滑块位置
  pointerLeft: number = 0;
  // 滑块位置百分比
  percent: number = 0;
  // 轨迹周期 endTime - startTime 的毫秒数
  duration: number = 0;
  markerList: any;
  componentLayerList: Array<DynamicComponent> = [];
  // 录屏数据
  recordEvents: Array<any> = [];
  operation: any;
  // 是否录制中
  isRecoding: boolean = false;
  speed: number = 1;
  // 额外的操作（下载船舶轨迹），有额外操作，代表是事故轨迹，否则是船舶轨迹
  extraHandle: boolean = false;
  // 当前显示轨迹的船舶列表
  shipList: Array<any> = []
  // popup
  popup!: L.Popup; // 船舶详细弹窗
  constructor(
    private mapLayerService: MapLayerComponentService,
  ) { }
  ngOnDestroy(): void {
    this.clearPlayInterval();
  }
  ngAfterViewInit(): void {
    this.hostListner(this.slider.nativeElement, 'click', (e) => {
      this.setBarProgress(e.offsetX - this.barBtnWidth!);
      if (!this.isPlaying) {
        this.start();
      }
    });
  }

  ngOnInit(): void {
    const { params } = this['data'];
    if (params) {
      // console.log('params', params);
      const { popup, startTime, endTime, markerList, componentLayerList, extraHandle, shipList } = params;
      this.componentLayerList = componentLayerList;
      this.extraHandle = extraHandle;
      this.shipList = shipList;
      this.startTime = startTime;
      this.endTime = endTime;
      this.popup = popup;
      if (markerList) {
        this.markerList = markerList.map((ele: any) => {
          const item = ele;
          item.currentIndex = 0;
          item.currentTime = this.currentTime();
          return item;
        });
      }
      this.duration =
        moment(this.endTime).valueOf() - moment(this.startTime).valueOf();
    }
  }

  get maxWidth() {
    return this.sliderWidth! - this.barBtnWidth!;
  }

  get sliderWidth() {
    return $(this.slider.nativeElement).width();
  }
  get barBtnWidth() {
    return $(this.barBtn.nativeElement).outerWidth();
  }
  private currentTime() {
    return moment(this.startTime).valueOf() + this.duration * this.percent;
  }
  get currentTimeStr() {
    return moment(this.currentTime()).format('YYYY/MM/DD HH:mm');
  }

  // 开始/暂停
  toggle() {
    if (this.pointerLeft == this.maxWidth!) {
      this.pointerLeft = 0;
      this.playComplete = false;
    }
    if (!this.isPlaying) {
      this.start();
    } else {
      this.pause();
    }
  }
  /** 暂停 */
  private pause() {
    this.isPlaying = false;
    this.clearPlayInterval();
  }
  /**
   * 开始动画
   * 1 帧移动 1px 总长是500px， 总共花费500帧
   */
  private start() {
    this.isPlaying = true;
    // requestAnimationFrame(this.animate);
    this.animate2();
  }

  /**
   * requestAnimationFrame 动画
   */
  now: number = 0;
  lastTime: number = Date.now();
  animate = () => {
    if (this.isPlaying && !this.playComplete) {
      this.now = Date.now();
      this.pointerLeft++;
      this.setBarProgress(this.pointerLeft);
      if (this.now - this.lastTime > 200) {
        this.lastTime = this.now;
        this.updatePosition();
      }
      requestAnimationFrame(this.animate);
    } else {
      this.pause();
    }
    this.playComplete = this.pointerLeft >= this.maxWidth!;
  };

  animate2() {
    let speedList = [200, 100, 50]; // 1.0x 2.0x 4.0x
    let interval = speedList[this.speed];
    this.clearPlayInterval();
    this.playInterval = setInterval(() => {
      this.playComplete = this.pointerLeft >= this.maxWidth!;
      if (!this.playComplete && this.isPlaying) {
        this.pointerLeft += 1;
        this.updatePosition();
        this.setBarProgress(this.pointerLeft);
      } else {
        this.pause();
      }
    }, interval);
  }

  private updatePosition() {
    this.markerList.forEach((element: any) => {
      element.updatePosition(this.pointerLeft);
    });
  }
  /**速度调节 */
  changeSpeed(speed: number) {
    if (this.speed != speed) {
      if (this.playComplete) {
        this.pointerLeft = 0;
        this.playComplete = false;
      }
      this.speed = speed;
      this.start();
    }
  }
  // 关闭轨迹图层
  close() {
    if (this.componentLayerList && this.componentLayerList.length) {
      // this.mapLayerService.removeComponentByUUIDs(
      //   this.componentLayerList.map((ele) => ele.uuid!)
      // );
      this.mapLayerService.removeComponents(this.componentLayerList)
    }
    this.mapLayerService.removeComponent({
      name: 'TrajectoryTimeControllerComponent',
      destroy: true,
    });
    // if (this.extraHandle) {
    //   // TODO： 关闭事故编辑/或新增页面
    //   this.mapLayerService.removeComponent({ name: 'ShipAccidentHandleComponent' });
    // }
    // else {
    //   this.mapLayerService.removeComponent({ name: 'ShipWindpowerInfoComponent' });
    // }
  }
  // 录制
  record() {
    if (this.isRecoding) {
      this.stopRecord();
    } else {
      this.startRecord();
    }
    // this.mapLayerService.addComponent({
    //   name: '',
    //   type: 'popup',
    // });
  }

  startRecord() {
    this.isRecoding = true;
    let recordEvents = this.recordEvents;
    // this.operation = rrweb.record({
    //   emit(event) {
    //     // 用任意方式存储 event
    //     recordEvents.push(event);
    //   },
    //   recordCanvas: true, //支持录制canvas
    // });
  }

  // play() {
  //   // let events = this.recordEvents;
  //   // new rrwebPlayer({
  //   //   target: document.getElementById('replaycontent')!, // 可以自定义 DOM 元素
  //   //   props: {
  //   //     events,
  //   //     mouseTail: false,
  //   //     UNSAFE_replayCanvas: true, //支持回放 canvas
  //   //   },
  //   // });
  // }

  stopRecord() {
    this.isRecoding = false;
    this.operation && this.operation();
    // this.play();
  }

  download() {
    if (this.extraHandle && this.shipList && this.shipList.length) {
      // console.log(this.shipList)
      const filename = `可疑船舶轨迹记录表（${this.startTime}-${this.endTime}）.xlsx`;
      const data: Array<Array<any>> = [['序号', '船舶名称', 'MMSI', '距离（米）', '经纬度', '时间', '速度（节）']]
      let index = 0;
      this.shipList.forEach((ele, i) => {
        const trackList: Array<any> = ele.trackList;
        let arr: Array<any> = []
        trackList.forEach((track, j: number) => {
          index++;
          arr = [index, ele.shipName, ele.mmsi, ele.distance, `${track.lat}°N，${track.lng}°E`, track.date, track.speed]
          data.push(arr);
        });
      })
      // const data = [[1, 2, 3], [true, false,"sheetjs"]];  //二维数组
      const ws: XLSX.WorkSheet = XLSX.utils.aoa_to_sheet(data);
      /* generate workbook and add the worksheet */
      const wb: XLSX.WorkBook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');

      /* save to file */
      XLSX.writeFile(wb, filename);
    }
  }

  goBack() {
    if (this.extraHandle) {
      this.mapLayerService.showComponent({ type: 'popup', name: 'ShipAccidentHandleComponent' });
    }
    else {
      this.mapLayerService.showComponent({ name: 'ShipWindpowerInfoComponent' });
    }
  }

  slideBtnMouseDown($event: any) {
    this.isDown = true;
    const e = $event || window.event;
    stopBubble(e);
    stopDefault(e);
    const leftVal = e.clientX - this.barBtn.nativeElement.offsetLeft; //滑块最左边距离浏览器左边的距离
    $(document)
      .on('mousemove', (e) => {
        if (!this.isDown) return;
        const barleft = e.clientX - leftVal;
        this.setBarProgress(barleft);
        //防止选择内容--当拖动鼠标过快时候，弹起鼠标，bar也会移动，修复bug
        window.getSelection
          ? window.getSelection()!.removeAllRanges()
          : document.getSelection()!.empty();
      })
      .on('mouseup', () => {
        this.isDown = false;
        $(document).off('mousemove');
        $(document).off('mouseup');
        if (!this.isPlaying) {
          this.start();
        }
      });
  }
  slideBtnClick($event: any) {
    const e = $event || window.event;
    stopBubble(e);
    stopDefault(e);
  }
  private hostListner(
    ele: HTMLElement,
    event: string,
    cb: (data?: any) => any
  ) {
    $(ele).on(event, cb);
  }

  // 设置滑块的位置
  private setBarProgress(barleft: number) {
    if (barleft < 0) {
      barleft = 0;
    } else if (barleft > this.maxWidth) {
      barleft = this.maxWidth;
    }
    this.percent = this.computedPercent(barleft);
    this.pointerLeft = barleft;
    this.activeBar.nativeElement.style.width = barleft + 'px';
    this.barBtn.nativeElement.style.left = barleft + 'px';
    this.playComplete = this.percent == 1;
    if (this.playComplete) this.isPlaying = false;
  }
  // 计算滑块滑动距离所占百分比
  private computedPercent(distance: number) {
    return Math.ceil((distance / this.maxWidth!) * 10000) / 10000;
  }

  private setBarProgressBypercent(percent: number) {
    if (percent == undefined || percent < 0 || percent > 1) {
      percent = 0;
    }
    const max = this.sliderWidth!;
    const maxLeft = max - this.barBtnWidth!;
    const barleft = percent * maxLeft;
    this.activeBar.nativeElement.style.width = barleft + 'px';
    this.barBtn.nativeElement.style.left = barleft + 'px';
  }
  // 清除滑块定时器
  private clearPlayInterval() {
    if (this.playInterval) clearInterval(this.playInterval);
  }

}
