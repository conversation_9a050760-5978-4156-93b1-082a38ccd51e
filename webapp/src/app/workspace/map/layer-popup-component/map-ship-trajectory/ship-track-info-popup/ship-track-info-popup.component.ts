import { Component, Input, OnInit } from '@angular/core';
import { DynamicComponentData } from 'src/app/shared/models';

@Component({
  selector: 'app-ship-track-info-popup',
  templateUrl: './ship-track-info-popup.component.html',
  styleUrls: ['./ship-track-info-popup.component.less']
})
export class ShipTrackInfoPopupComponent implements OnInit {
  @Input() data: DynamicComponentData = new DynamicComponentData()
  model: any = {}

  constructor() { }

  ngOnInit(): void {
    const { params } = this.data
    if (params) {
      this.model = params.model
    }
  }

}
