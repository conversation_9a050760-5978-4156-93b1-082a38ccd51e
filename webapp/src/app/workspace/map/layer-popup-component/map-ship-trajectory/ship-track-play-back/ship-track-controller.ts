import * as L from 'leaflet'
import { ShipTrack } from './ship-track'
import { ShipTrackDraw } from './ship-track-draw'
export class ShipTrackController extends L.Class {
    private _tracks: Array<ShipTrack>
    private _minTime: number = 0
    private _maxTime: number = 0
    private _draw: ShipTrackDraw
    constructor(tracks: Array<ShipTrack>, draw: ShipTrackDraw) {
        super()
        this._draw = draw
        this._tracks = []
        this.addTrack(tracks)
        console.log('ShipTrackController', this._tracks)
        this._updateTime()
    }
    get minTime() {
        return this._minTime
    }

    get maxTime() {
        return this._maxTime
    }

    addTrack(track: Array<ShipTrack> | ShipTrack) {
        if (Array.isArray(track)) {
            for (let i = 0, len = track.length; i < len; i++) {
                this.addTrack(track[i])
            }
        } else {
            this._tracks.push(track)
            this._updateTime()
            this._draw.drawTrack(track.trackList)
        }
    }

    drawTracksByTime(time: number) {
        this._draw.clear()
        for (let i = 0, len = this._tracks.length; i < len; i++) {
            let track = this._tracks[i]
            let tps = track.getTrackPointsBeforeTime(time)
            if (tps && tps.length) this._draw.drawTrack(tps)
        }
    }

    private _updateTime() {
        this._minTime = this._tracks[0].getStartTrackPoint().time
        this._maxTime = this._tracks[0].getEndTrackPoint().time
        for (let i = 0, len = this._tracks.length; i < len; i++) {
            let stime = this._tracks[i].getStartTrackPoint().time
            let etime = this._tracks[i].getEndTrackPoint().time
            if (stime < this._minTime) {
                this._minTime = stime
            }
            if (etime > this._maxTime) {
                this._maxTime = etime
            }
        }
    }
}
