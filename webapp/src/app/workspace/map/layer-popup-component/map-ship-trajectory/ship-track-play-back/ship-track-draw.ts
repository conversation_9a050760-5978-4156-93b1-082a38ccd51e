import * as L from 'leaflet'
import { ShipTrackLayer } from './ship-track-layer'
import { ShipTrackPoint } from './ship-track-point.model'
export class ShipTrackDraw extends L.Class {
    private _map: L.Map
    private _showTrackPoint: boolean = true // 是否显示轨迹点
    private _showTrackLine: boolean = true // 是否显示轨迹线
    private _shipTrackLayer: ShipTrackLayer
    private _canvas: HTMLCanvasElement
    private _ctx: CanvasRenderingContext2D

    private _bufferTracks: Array<ShipTrackPoint[]> = []

    private _trackPointFeatureGroup!: L.FeatureGroup


    private _targetImg: HTMLImageElement | undefined


    // 轨迹点配置
    private trackPointOptions = {
        isDraw: true,
        useCanvas: true,
        stroke: true,
        color: '#AB47BC',// stroke color
        fill: true,
        fillColor: '#AB47BC',
        opacity: 1,
        radius: 3
    }

    // 轨迹线配置
    private trackLineOptions = {
        isDraw: true,
        stroke: true,
        color: '#AB47BC', // stroke color
        weight: 1,
        fill: false,
        fillColor: '#000',
        opacity: 1
    }

    // 目标物（船）
    private targetOptions = {
        useImg: true, // 使用图标绘制船舶
        imgUrl: '/assets/img/map/ship/cargo-ship2.png',
        showText: false,
        width: 20,
        height: 20,
        color: '#00f', // stroke color
        fillColor: '#9FD12D'
    }

    // 轨迹点hover 显示toolTip 的配置
    private toolTipOptions = {
        offset: [0, 0],
        direction: 'top',
        permanent: false
    }

    constructor(map: L.Map) {
        super()
        this._map = map
        this._map.on('mousemove', this._onmousemoveEvt, this)
        this._shipTrackLayer = new ShipTrackLayer().addTo(this._map)
        this._shipTrackLayer.on('update', this._trackLayerUpdate, this)
        this._canvas = this._shipTrackLayer.getContainer()
        this._ctx = this._canvas.getContext('2d')!

        this._bufferTracks = []

        if (!this.trackPointOptions.useCanvas) {
            this._trackPointFeatureGroup = L.featureGroup([]).addTo(map)
        }

        // 目标如果使用图片，先加载图片
        if (this.targetOptions.useImg) {
            const img = new Image()
            img.onload = () => {
                this._targetImg = img
            }
            img.onerror = () => {
                throw new Error('img load error!')
            }
            img.src = this.targetOptions.imgUrl
        }
        console.log('this.onload', this._targetImg)
    }

    update() {
        this._trackLayerUpdate()
    }

    drawTrack(trackPoints: ShipTrackPoint[]) {
        this._bufferTracks.push(trackPoints)
        this._drawTrack(trackPoints)
    }

    remove() {
        this._bufferTracks = []
        this._shipTrackLayer.off('update', this._trackLayerUpdate, this)
        this._map.off('mousemove', this._onmousemoveEvt, this)
        if (this._map.hasLayer(this._shipTrackLayer)) {
            this._map.removeLayer(this._shipTrackLayer)
        }
        if (this._map.hasLayer(this._trackPointFeatureGroup)) {
            this._map.removeLayer(this._trackPointFeatureGroup)
        }
    }

    clear() {
        this._clearLayer()
        this._bufferTracks = []
    }

    private _clearLayer() {
        let bounds = this._shipTrackLayer.getBounds()
        if (bounds) {
            let size = bounds.getSize()
            this._ctx.clearRect(bounds.min.x, bounds.min.y, size.x, size.y)
        } else {
            this._ctx.clearRect(0, 0, this._canvas.width, this._canvas.height)
        }
        if (this._map.hasLayer(this._trackPointFeatureGroup)) {
            this._trackPointFeatureGroup.clearLayers()
        }
    }




    /**
     * 绘制轨迹
     * @param trackPoints 
     */
    private _drawTrack(trackPoints: Array<ShipTrackPoint>) {
        // 画轨迹线
        if (this._showTrackLine) {
            this._drawTrackLine(trackPoints)
        }
        // console.log('_drawTrack', trackPoints);
        // 画船
        let targetPoint = trackPoints[trackPoints.length - 1]

        if (this.targetOptions.useImg && this._targetImg) {
            this._drawShipImage(targetPoint)
        }

        // 画标注信息(画tool tip)
        // if (this.targetOptions.showText) {
        // this._drawtxt(`航向：${parseInt(targetPoint.dir)}度`, targetPoint)
        // }

        // 画经过的轨迹点
        if (this._showTrackPoint) {
            if (this.trackPointOptions.useCanvas) {
                this._drawTrackPointsCanvas(trackPoints)
            } else {
                this._drawTrackPointsSvg(trackPoints)
            }
        }
    }

    /**
     * 画轨迹线
     * @param trackPoint ShipTrackPoint
     */
    private _drawTrackLine(trackpoints: ShipTrackPoint[]) {
        // console.log('drawTrackLine', trackpoints);
        let options = this.trackLineOptions
        let tp0 = this._getLayerPoint(trackpoints[0])
        this._ctx.save()
        this._ctx.beginPath()
        // 画轨迹线
        this._ctx.moveTo(tp0.x, tp0.y)
        for (let i = 1, len = trackpoints.length; i < len; i++) {
            const tpi = this._getLayerPoint(trackpoints[i])
            this._ctx.lineTo(tpi.x, tpi.y)
        }
        this._ctx.globalAlpha = options.opacity
        if (options.stroke) {
            this._ctx.strokeStyle = options.color
            this._ctx.lineWidth = options.weight
            this._ctx.stroke()
        }
        if (options.fill) {
            this._ctx.fillStyle = options.fillColor
            this._ctx.fill()
        }
        this._ctx.restore()
    }

    /**
     * 根据配置图标画船
     * @param trackPoint 
     */
    private _drawShipImage(trackpoint: ShipTrackPoint) {
        // const point = this._getLayerPoint(trackpoint)
        // const dir = trackpoint.dir || 0
        // const width = this.targetOptions.width
        // const height = this.targetOptions.height
        // const offset = {
        //     x: width / 2,
        //     y: height / 2
        // }
        // const x = point.x - width / 2,
        //     y = point.y - height / 2
        // if (!trackpoint.cavasImg) {
        //     const img = new Image()
        //     img.src = this.targetOptions.imgUrl
        //     img.onerror = () => {
        //         throw new Error('img load error!')
        //     }
        //     trackpoint.cavasImg = img
        //     trackpoint.cavasImg.onload = () => {
        //         this._ctx.save()
        //         this._ctx.translate(point.x, point.y)
        //         this._ctx.rotate((Math.PI / 180) * dir)
        //         this._ctx.drawImage(img, x, y, width, height)
        //         this._ctx.rotate(-(Math.PI / 180) * dir)
        //         this._ctx.translate(point.x, point.y)
        //         this._ctx.restore()
        //     }
        // } else {
        //     this._ctx.save()
        //     this._ctx.translate(point.x, point.y)
        //     this._ctx.rotate((Math.PI / 180) * dir)
        //     this._ctx.drawImage(trackpoint.cavasImg, x, y, width, height)
        //     this._ctx.restore()
        // }
        let point = this._getLayerPoint(trackpoint)
        let dir = trackpoint.course || 0
        let width = this.targetOptions.width
        let height = this.targetOptions.height
        let offset = {
            x: width / 2,
            y: height / 2
        }
        this._ctx.save()
        this._ctx.translate(point.x, point.y)
        this._ctx.rotate((Math.PI / 180) * dir)
        this._ctx.drawImage(this._targetImg!, 0 - offset.x, 0 - offset.y, width, height)
        this._ctx.restore()

    }

    /**
     * 使用canvas 绘制船舶经过的点
     * @param trackPoint 
     */
    private _drawTrackPointsCanvas(trackpoints: ShipTrackPoint[]) {
        let options = this.trackPointOptions
        this._ctx.save()
        for (let i = 0, len = trackpoints.length; i < len; i++) {
            if (trackpoints[i].isOrigin) {
                let latLng = L.latLng(trackpoints[i].lat, trackpoints[i].lng)
                let radius = options.radius
                let point = this._map.latLngToLayerPoint(latLng)
                this._ctx.beginPath()
                this._ctx.arc(point.x, point.y, radius, 0, Math.PI * 2, false)
                this._ctx.globalAlpha = options.opacity
                if (options.stroke) {
                    this._ctx.strokeStyle = options.color
                    this._ctx.stroke()
                }
                if (options.fill) {
                    this._ctx.fillStyle = options.fillColor
                    this._ctx.fill()
                }
            }
        }
        this._ctx.restore()
    }

    /**
     * 使用SVG 绘制船舶经过的点
     * @param trackPoints 
     */
    private _drawTrackPointsSvg(trackpoints: ShipTrackPoint[]) {
        for (let i = 0, len = trackpoints.length; i < len; i++) {
            if (trackpoints[i].isOrigin) {
                let latLng = L.latLng(trackpoints[i].lat, trackpoints[i].lng)
                let cricleMarker = L.circleMarker(latLng, this.trackPointOptions)
                // cricleMarker.bindTooltip(this._getTooltipText(trackpoints[i]), this.toolTipOptions)
                this._trackPointFeatureGroup.addLayer(cricleMarker)
            }
        }
    }

    /**
     * 使用canvas 绘制toolTip
     * @param trackPoint 
     */
    private _drawToolTip(trackPoint: any) {
        // TODO:
    }

    /**
     * TODO：
     * mousemove 打开tool tip
     * @param e 
     * @returns 
     */
    private _onmousemoveEvt(e: any) {
        if (!this._showTrackPoint) {
            return
        }
    }


    private _trackLayerUpdate() {
        if (this._bufferTracks.length) {
            this._clearLayer()
            this._bufferTracks.forEach((element) => {
                this._drawTrack(element)
            })
        }
    }

    /**
     * 根据坐标点获取对应的point 位置
     * latLngToLayerPoint 给定一个地理坐标，返回相对于原始像素的相应像素坐标。  
     * @param trackpoint 
     * @returns 
     */
    private _getLayerPoint(trackpoint: ShipTrackPoint) {
        return this._map.latLngToLayerPoint(L.latLng(trackpoint.lat, trackpoint.lng))
    }
}