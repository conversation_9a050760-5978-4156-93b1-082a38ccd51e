import * as L from 'leaflet'
import { ShipClock } from './ship-clock'
import { ShipTrack } from './ship-track'
import { ShipTrackController } from './ship-track-controller'
import { ShipTrackDraw } from './ship-track-draw'
import { MockShipTrackPoint, ShipTrackPoint } from './ship-track-point.model'
export class ShipTrackPlayBack extends L.Evented {
    private tracks: Array<ShipTrack> = []
    private draw: ShipTrackDraw | null
    private trackController: ShipTrackController | null
    private clock: ShipClock | null
    constructor(data: Array<Array<MockShipTrackPoint>> | Array<MockShipTrackPoint>, map: L.Map, options: any = {}) {
        super()
        this.tracks = this._initTracks(data)
        this.draw = new ShipTrackDraw(map)
        console.log('ShipTrackPlayBack', this.tracks);

        this.trackController = new ShipTrackController(this.tracks, this.draw)
        this.clock = new ShipClock(this.trackController)
        this.clock.on('tick', this._tick, this)
    }

    start() {
        this.clock!.start()
        return this
    }

    dispose() {
        this.clock!.off('tick', this._tick)
        this.draw!.remove()
        this.tracks = []
        this.draw = null
        this.trackController = null
        this.clock = null
    }
    /**
     * 初始化轨迹
     * @param data 
     * @returns 
     */
    private _initTracks(data: Array<Array<MockShipTrackPoint>> | Array<MockShipTrackPoint>): Array<ShipTrack> {
        let tracks = []
        if (Array.isArray(data)) {
            if (Array.isArray(data[0])) {
                // 多条轨迹
                for (let i = 0, len = data.length; i < len; i++) {
                    tracks.push(new ShipTrack(<MockShipTrackPoint[]>data[i]))
                }
            } else {
                // 单条轨迹
                tracks.push(new ShipTrack(<MockShipTrackPoint[]>data))
            }
        }
        return tracks
    }

    _tick(e: any) {
        console.log('ship-track-play-back _tick');
        this.fire('tick', e)
    }
}