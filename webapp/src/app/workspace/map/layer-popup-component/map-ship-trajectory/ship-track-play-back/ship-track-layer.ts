import * as L from 'leaflet'
export class ShipTrackLayer extends <PERSON><PERSON>er {
    options!: <PERSON>.RendererOptions;
    private _container!: HTMLCanvasElement
    private _ctx!: CanvasRenderingContext2D
    constructor(options?: <PERSON>.RendererOptions) {
        super()
        this.options.padding = 0.1
    }

    onAdd(map: L.Map) {
        this._map = map
        this._container = L.DomUtil.create('canvas', 'leaflet-zoom-animated')
        this._ctx = this._container.getContext('2d')!
        const pane = map.getPane(this.options.pane!)
        pane?.appendChild(this._container)
        this._update()
        return this
    }
    onRemove(map: L.Map): this {
        L.DomUtil.remove(this._container)
        return this
    }

    getContainer(): HTMLCanvasElement {
        return this._container
    }

    getBounds() {
        return this['_bounds']
    }

    private _update() {
        if (this._map['_animatingZoom'] && this.getBounds()) {
            return
        }
        L.Renderer.prototype['_update'].call(this)
        const b = this.getBounds()
        const container = this.getContainer()
        const size = b.getSize()
        const m = L.Browser.retina ? 2 : 1

        L.DomUtil.setPosition(container, b.min)

        // set canvas size (also clearing it); use double size on retina
        container.width = m * size.x
        container.height = m * size.y
        container.style.width = size.x + 'px'
        container.style.height = size.y + 'px'

        if (L.Browser.retina) {
            this._ctx.scale(2, 2)
        }
        // translate so we use the same path coordinates after canvas element moves
        this._ctx.translate(-b.min.x, -b.min.y)

        // Tell paths to redraw themselves
        this.fire('update')
    }
}