import * as L from 'leaflet'
import { MockShipTrackPoint, ShipTrackPoint } from './ship-track-point.model'
export class ShipTrack extends L.Class {
    _trackPoints: Array<ShipTrackPoint> = []
    _timeTick: any = {}
    constructor(trackData: Array<MockShipTrackPoint> = []) {
        super()
        this._trackPoints = trackData.map(ele => {
            const trackPoint: ShipTrackPoint = <ShipTrackPoint>ele
            // 添加 isOrigin 字段用来标识是否是原始采样点，与插值点区分开
            trackPoint.isOrigin = true
            trackPoint.time = Math.floor(new Date(ele.date!).getTime() / 1000)
            return trackPoint
        })
        this._timeTick = {}
        this._update()
    }
    get trackLen() {
        return this._trackPoints.length
    }

    private _update() {
        this._sortTrackPointsByTime()
        this._updatetimeTick()
    }

    get trackList() {
        return this._trackPoints
    }


    getStartTrackPoint(): ShipTrackPoint {
        return this._trackPoints[0]
    }

    getEndTrackPoint(): ShipTrackPoint {
        return this._trackPoints[this.trackLen - 1]
    }

    getTrackPointByTime(time: number): ShipTrackPoint {
        return this._trackPoints[this._timeTick[time]]
    }

    /**
     * 获取所有的时间节点
     * @returns 
     */
    getTimes() {
        const times = []
        for (let i = 0; i < this.trackLen; i++) {
            times.push(this._trackPoints[i].time)
        }
        return times
    }

    /**
     * 获取某个时间点走过的轨迹
     * @param time 
     */
    getTrackPointsBeforeTime(time: number) {
        let tpoints = []
        for (let i = 0, len = this._trackPoints.length; i < len; i++) {
            if (this._trackPoints[i].time < time) {
                tpoints.push(this._trackPoints[i])
            }
        }
        // 获取最后一个点，根据时间线性插值而来
        let endPt = this._getCalculateTrackPointByTime(time)
        if (endPt) {
            tpoints.push(endPt)
        }
        return tpoints
    }

    /**
     * 为轨迹点建立时间索引，优化查找性能
     */
    private _updatetimeTick() {
        this._timeTick = {}
        for (let i = 0, len = this._trackPoints.length; i < len; i++) {
            this._timeTick[this._trackPoints[i].time] = i
        }
    }

    /**
     * 轨迹点按时间排序 【冒泡排序】
     */
    private _sortTrackPointsByTime() {
        let len = this._trackPoints.length
        for (let i = 0; i < len; i++) {
            for (let j = 0; j < len - 1 - i; j++) {
                if (this._trackPoints[j].time > this._trackPoints[j + 1].time) {
                    let tmp = this._trackPoints[j + 1]
                    this._trackPoints[j + 1] = this._trackPoints[j]
                    this._trackPoints[j] = tmp
                }
            }
        }
    }

    /**
     * 根据时间计算出轨迹点
     * @param time 
     * @returns 
     */
    private _getCalculateTrackPointByTime(time: number) {
        // 先判断最后一个点是否为原始点
        let endpoint: ShipTrackPoint = this.getTrackPointByTime(time)
        let startPt: ShipTrackPoint = this.getStartTrackPoint()
        let endPt: ShipTrackPoint = this.getEndTrackPoint()
        let times = this.getTimes()
        if (time < startPt.time || time > endPt.time) return
        let left = 0
        let right = times.length - 1
        let n
        // 处理只有一个点情况
        if (left === right) {
            return endpoint
        }
        // 通过【二分查找】法查出当前时间所在的时间区间
        while (right - left !== 1) {
            // n = parseInt((left + right ) / 2)
            n = Math.floor((left + right) / 2)
            if (time > times[n]) left = n
            else right = n
        }

        let t0 = times[left]
        let t1 = times[right]
        let t = time
        let p0 = this.getTrackPointByTime(t0)
        let p1 = this.getTrackPointByTime(t1)
        const _startPt = L.point(p0.lng, p0.lat)
        const _endPt = L.point(p1.lng, p1.lat)
        let s = _startPt.distanceTo(_endPt)
        // 不同时间在同一个点情形
        if (s <= 0) {
            endpoint = p1
            return endpoint
        }
        // 假设目标在两点间做匀速直线运动
        // 求解速度向量，并计算时间 t 目标所在位置
        let v = s / (t1 - t0)
        let sinx = (_endPt.y - _startPt.y) / s
        let cosx = (_endPt.x - _startPt.x) / s
        let step = v * (t - t0)
        let x = _startPt.x + step * cosx
        let y = _startPt.y + step * sinx
        // 求目标的运动方向，0-360度
        let dir = _endPt.x >= _startPt.x ? (Math.PI * 0.5 - Math.asin(sinx)) * 180 / Math.PI : (Math.PI * 1.5 + Math.asin(sinx)) * 180 / Math.PI

        if (endpoint) {
            // if (endpoint.dir === undefined) {
            //     endpoint.dir = dir
            // }
            endpoint.course = dir
        } else {
            endpoint = {
                lng: x,
                lat: y,
                course: dir,
                isOrigin: false,
                time: time
            }
        }
        return endpoint
    }

}