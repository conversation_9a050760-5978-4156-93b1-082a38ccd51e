import * as L from 'leaflet'
import { ShipTrackController } from './ship-track-controller'
export class ShipClock extends L.Evented {

    private _intervalID: any
    private _lastFpsUpdateTime: number = 0
    private _curTime: number = 0
    private _endTime: number = 0
    private _speed: number = 12    // 播放速度 计算方法 fpstime * Math.pow(2, this._speed - 1)
    private _maxSpeed: number = 65 // 最大播放速度
    private _trackController: ShipTrackController
    constructor(trackController: ShipTrackController) {
        super()
        this._trackController = trackController
        this._endTime = this._trackController.maxTime
        this._curTime = this._trackController.minTime
    }
    start() {
        if (this._intervalID) return
        this._intervalID = L.Util.requestAnimFrame(this._tick, this)
    }

    stop() {
        if (!this._intervalID) return
        L.Util.cancelAnimFrame(this._intervalID)
        this._intervalID = null
        this._lastFpsUpdateTime = 0
    }

    rePlaying() {
        this.stop()
        this._curTime = this._trackController.minTime
        this.start()
    }
    private _tick() {
        let now = +new Date() // 毫秒数
        let fpstime = this._caculatefpsTime(now)
        let isPause = false
        let stepTime = fpstime * Math.pow(2, this._speed - 1)
        this._curTime += stepTime
        if (this._curTime >= this._endTime) {
            this._curTime = this._endTime
            isPause = true
        }
        this._trackController.drawTracksByTime(this._curTime)
        this.fire('tick', {
            time: this._curTime
        })
        if (!isPause) this._intervalID = L.Util.requestAnimFrame(this._tick, this)
    }

    private _caculatefpsTime(now: number) {
        let time
        if (this._lastFpsUpdateTime === 0) {
            time = 0
        } else {
            time = now - this._lastFpsUpdateTime
        }
        this._lastFpsUpdateTime = now
        // 将毫秒转换成秒
        time = time / 1000
        return time
    }


}