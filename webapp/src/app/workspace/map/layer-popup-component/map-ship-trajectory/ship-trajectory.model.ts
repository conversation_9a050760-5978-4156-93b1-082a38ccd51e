import { LatLng } from 'leaflet';
export class ShipTrajectory {
  LAT?: number;
  LNG?: number;
  DATE?: number; // 返回秒数 1628399518
  DATESTR?: string; //时间 精确到秒 2021-08-08 13:11:58
  MMSI?: string;
  ACCURACY?: string;
  CODE?: string;
  COURSE?: string;
  course?: number; // 船迹向
  HEADING?: string;
  SPEED?: number;
  speed?: number;
  TURN?: number;
  date?: string;
  time?: number; // 毫秒数
  lat?: number; // 经度
  lng?: number; // 纬度
  period?: number; // 两点间花费时长
  rotateDeg?: number; // 两点间的旋转角度
  latlng?: LatLng;
}
