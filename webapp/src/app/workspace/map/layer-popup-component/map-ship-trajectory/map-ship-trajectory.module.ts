import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { TrajectoryTimeControllerComponent } from './trajectory-time-controller/trajectory-time-controller.component';
import { ShipTrajectoryLayerComponent } from './ship-trajectory-layer/ship-trajectory-layer.component';
import { ShipTrackInfoPopupComponent } from './ship-track-info-popup/ship-track-info-popup.component';
import { ShipTrajectoryLayer2Component } from './ship-trajectory-layer/ship-trajectory-layer2.component';


@NgModule({
  declarations: [
    TrajectoryTimeControllerComponent,
    ShipTrajectoryLayer2Component,
    ShipTrajectoryLayerComponent,
    ShipTrackInfoPopupComponent
  ],
  imports: [
    CommonModule,
  ]
})
export class MapShipTrajectoryModule { }
