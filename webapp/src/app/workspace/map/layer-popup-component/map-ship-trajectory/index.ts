import { DynamicComponent } from "src/app/shared/models/dynamic-component.model";
import { ShipTrackInfoPopupComponent } from "./ship-track-info-popup/ship-track-info-popup.component";
import { ShipTrajectoryLayerComponent } from "./ship-trajectory-layer/ship-trajectory-layer.component";
import { ShipTrajectoryLayer2Component } from "./ship-trajectory-layer/ship-trajectory-layer2.component";
import { TrajectoryTimeControllerComponent } from "./trajectory-time-controller/trajectory-time-controller.component";

// 船舶轨迹
export const DYNAMIC_MAP_TRACK_COMPONENTS: Array<DynamicComponent> = [
    //时间控制器
    { name: 'TrajectoryTimeControllerComponent', component: TrajectoryTimeControllerComponent },
    // 轨迹图层
    { name: 'ShipTrajectoryLayerComponent', component: ShipTrajectoryLayerComponent },
    // 时间popup
    { name: 'ShipTrackInfoPopupComponent', component: ShipTrackInfoPopupComponent },
    // 轨迹图层2
    { name: 'ShipTrajectoryLayer2Component', component: ShipTrajectoryLayer2Component }


];