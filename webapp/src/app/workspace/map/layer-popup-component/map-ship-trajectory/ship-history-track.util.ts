import * as L from 'leaflet';
import * as moment from 'moment';
import * as _ from 'lodash';
import { ShipTrajectory } from './ship-trajectory.model';
const LINE_COLORS: Array<string> = [
  '#AB47BC',
  '#7E58C2',
  '#8C9EFF',
  '#FF5252',
  '#26C6DA',
  '#66BB6A',
  '#FF6F43',
  '#FFC400',
  '#795548',
  '#546E7A',
  '#1B8258',
  '#9E9D25',
  '#AED581',
  '#3F51B5',
  '#5D35A3',
  '#D32F2F',
  '#3FC4FF',
  '#2963FF',
  '#AA6D79',
  '#F06292',
];
// 历史轨迹终点图标
export const EndPointImg = '/assets/img/map/end-point.png';

/**
 * 根据序号得出历史轨迹线路的颜色
 * @param index
 * @returns
 */
export const getColorByIndex = (index: number) => {
  const len = LINE_COLORS.length;
  return LINE_COLORS[index % len];
};

/**
 *
 * 生成轨迹移动的点 1px 对应 个 点 ，100 ms 移动一1px  100ms 对应1个点
 * @export
 * @param {Array<ShipTrajectory>} trackList
 * @param {number} interval endTime - startTime / 500(时间轴 500px) ，每一px对应的时间
 */
export function genTimerTrackList2(
  trackList: Array<ShipTrajectory>,
  startTime: string,
  endTime: string
) {
  let startAt = moment(startTime).valueOf();
  // 当前查询时间段总耗时（毫秒）
  const duration = moment(endTime).valueOf() - startAt;
  let interval = duration / 500; // 时间轴总长度 500px ,需要500次轮回
  let timerTrackList: Array<any> = [];
  let i = 0,
    len = trackList.length;
  for (; i < len; i++) {
    const track = trackList[i];
    const nextTrack = trackList[i + 1];
    if (i == len - 1) {
      timerTrackList.push({
        lat: track.lat,
        lng: track.lng,
        rotateDeg: track.rotateDeg,
        index: i,
        startAt: track.time,
      });
    } else {
      let { trackList: _timerTrackList, startAt: _startAt } = computedTrack(
        track,
        nextTrack,
        interval,
        i,
        startAt
      );
      startAt = _startAt;
      timerTrackList.push(..._timerTrackList);
    }
  }
  return timerTrackList;
}

function computedTrack(
  startTrack: ShipTrajectory,
  endTrack: ShipTrajectory,
  interval: number,
  index: number,
  startAt: number
) {
  const start = startTrack.time!;
  const end = endTrack.time!;
  const period = end - start;
  let trackList: Array<any> = [];
  while (startAt <= end) {
    if (startAt <= start) {
      trackList.push({
        lat: startTrack.lat!,
        lng: startTrack.lng!,
        rotateDeg: startTrack.rotateDeg,
        index,
        startAt,
      });
    } else {
      let percent = (startAt - start) / period;
      let lat = startTrack.lat! + (endTrack.lat! - startTrack.lat!) * percent;
      let lng = startTrack.lng! + (endTrack.lng! - startTrack.lng!) * percent;
      trackList.push({
        lat,
        lng,
        rotateDeg: startTrack.rotateDeg,
        index,
        startAt,
      });
    }
    startAt += interval;
  }
  return { startAt, trackList };
}

/**
 * 根据当前轨迹计算出通过时间轴控件播放需要生成的轨迹点
 * 时间轴控件500px，每循环一次移动 1px
 */
export const genTimerTrackList = (
  trackList: Array<ShipTrajectory>,
  startTime: string,
  endTime: string
) => {
  // 时间轴对应的轨迹
  const timerTrackList = []; // 长度500
  // 开始时间
  let startAt = moment(startTime).valueOf();
  // 当前查询时间段总耗时（毫秒）
  const duration = moment(endTime).valueOf() - startAt;
  // 时间轴移动1px 对应花费的时长（毫秒）
  // 1px 100ms 基准 1 * 500 500 500点
  // 1px 200ms 更慢 2 * 500 1000  1000 点
  // 1px 400ms 更慢 4 * 500 2000 2000 点

  let perPeriod = duration / 1000; // 500

  let currentIndex = 0;
  for (let i = 0; i <= 1000; i++) {
    const track = trackList[currentIndex];
    const nextTrack = trackList[currentIndex + 1];
    const rotateDeg = track.rotateDeg;
    if (!nextTrack) {
    } else if (startAt >= track.time! && startAt <= nextTrack.time!) {
      let t = startAt - track.time!;
      let percent = t / (nextTrack.time! - track.time!);
      let lat = track.lat! + (nextTrack.lat! - track.lat!) * percent;
      let lng = track.lng! + (nextTrack.lng! - track.lng!) * percent;
      timerTrackList.push({
        lat,
        lng,
        rotateDeg,
        index: currentIndex,
        time: startAt,
      });
    } else {
      if (startAt > nextTrack.time!) {
        currentIndex++;
      }
      timerTrackList.push({
        lat: track.lat,
        lng: track.lng,
        rotateDeg,
        index: currentIndex,
        time: startAt,
      });
    }
    startAt += perPeriod;
  }
  return timerTrackList;
};

/**
 * 计算后台返回的历史轨迹
 * 格式如下，
 * ACCURACY: "1"
 * CODE: "0"
 * COURSE: 194.8
 * DATESTR: "2021-07-21 09:59"
 * DATE:1628399518（秒） * 1000 （毫秒）
 * HEADING: 194
 * lat: 37.692783
 * lng: 123.29637
 * MMSI: "413226260"
 * SPEED: 9.4
 * TURN: 0
 */
export const genComputedTrackList = (_trackList: Array<ShipTrajectory>) => {
  const trackList = _.cloneDeep(_trackList);
  for (let i = 0; i < trackList.length; i++) {
    const track = trackList[i];
    // const date = parseFloat(trackList[i].date!)
    // 注意：这里的date 后台返回的是日期格式的字符串 （YYYY-MM-DD HH:mm:ss）
    track.time = moment(trackList[i].date!).valueOf();
    track.lat = trackList[i].lat;
    track.lng = trackList[i].lng;
    track.latlng = L.latLng(track.lat!, track.lng!);
    if (i === trackList.length - 1) {
      track.period = trackList[i - 1].period;
      track.rotateDeg = trackList[i - 1].rotateDeg;
    } else {
      let nextTrack = trackList[i + 1];
      const date = parseFloat(nextTrack.date!)
      track.period = moment(date * 1000).valueOf() - track.time;
      trackList[i].rotateDeg = getRotation(
        L.latLng(track.lat!, track.lng!),
        L.latLng(nextTrack.lat!, nextTrack.lng!)
      );
    }
  }
  return trackList;
};

/**
 * 生成终点图标
 * @param latlng
 * @returns
 */
export const genEndPointMarker = (latlng: L.LatLng) => {
  const icon = L.icon({
    iconUrl: EndPointImg,
    iconSize: [32, 32],
    iconAnchor: [16, 16],
  });
  return L.marker(latlng, {
    icon,
  });
};
/**
 * 计算两点间的角度
 * @param start
 * @param end
 * @returns
 */
const getRotation = (start: L.LatLng, end: L.LatLng) => {
  const dx = end.lng - start.lng;
  const dy = end.lat - start.lat;
  const radian = Math.atan2(dy, dx); //弧度值
  let rotation = (180 * radian) / Math.PI; //转换为角度值
  if (rotation > -180 && rotation < 0) {
    rotation = 360 + rotation;
  }
  return rotation;
};
