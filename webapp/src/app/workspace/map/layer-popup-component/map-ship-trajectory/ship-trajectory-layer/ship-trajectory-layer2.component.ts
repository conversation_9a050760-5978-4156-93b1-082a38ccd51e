import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { MapStateService } from '../../../service/map-state.service';
import * as L from 'leaflet';
import * as _ from 'lodash';
import { MockShipTrackPoint } from '../ship-track-play-back/ship-track-point.model';
import { ShipTrackPlayBack } from '../ship-track-play-back/ship-track-play-back';
import { MOCK_DATA } from '../ship-track-play-back/mock.data';
@Component({
  selector: 'app-ship-trajectory-layer2',
  template: ``
})
export class ShipTrajectoryLayer2Component implements OnInit, OnDestroy {
  private map: L.Map;
  trackplayBack!: ShipTrackPlayBack
  // 模拟的数据
  private mockData: Array<Array<MockShipTrackPoint>> = []

  constructor(private mapState: MapStateService) {
    this.map = this.mapState.getMapInstance();
    this.mockData = _.cloneDeep(MOCK_DATA)
  }

  ngOnDestroy(): void {
    this.trackplayBack.dispose()
    this.trackplayBack.off('tick', this._tickCallback, this)
  }

  ngOnInit(): void {
    this.map.setView(L.latLng(38.700283, 123.85422))
    this.trackplayBack = new ShipTrackPlayBack(this.mockData, this.map)
    console.log(this.trackplayBack);
    this.trackplayBack!.on('tick', this._tickCallback, this)
    // const trackplaybackControl = new ShipTrackPlayBackControl(trackplayBack);
    // trackplaybackControl.addTo(this.map)
    setTimeout(() => {
      // this.trackplayBack.start()
    }, 1000);
  }
  _tickCallback = (e: any) => {
    console.log('_tickCallback', e);
  }
}
