import { stopBubble } from 'src/app/shared/utils';
import { Component, OnInit, OnDestroy } from '@angular/core';
import * as L from 'leaflet';
import { MapLayerComponentService } from '../../../service/map-layer-component.service';
import { MapStateService } from '../../../service/map-state.service';
import { getShipType } from '../ship-type';
import { canvasLayerFactory, animatedMarkerFactory } from 'src/app/utils';
import { Subscription } from 'rxjs/internal/Subscription';
import { DynamicComponent } from 'src/app/shared/models';
import { filter } from 'rxjs/operators';
import { MapOption } from '../../../models/MapOption.model';
import { genComputedTrackList, genEndPointMarker, genTimerTrackList2, getColorByIndex } from '../ship-history-track.util';
import { ShipTrajectory } from '../ship-trajectory.model';
import * as _ from 'lodash';
@Component({
  selector: 'app-ship-trajectory-layer',
  template: ``
})
export class ShipTrajectoryLayerComponent implements OnInit, OnDestroy {
  private map: L.Map;
  private trackGroupLayer: L.FeatureGroup; // marker line
  private trackTimePopupLayer: L.FeatureGroup; // 轨迹上的时间popup
  private timePopList: Array<L.Popup> = [];
  private animateMarkerList: Array<any> = []; // 动态轨迹marker
  private animatedMarkerLayers: L.FeatureGroup; // 动态轨迹图层 (marker)
  private isAdded: boolean = false;
  private trackType: 'ship' | 'accident' = 'ship'; // 轨迹类型 ship: 船舶详情中单条船轨迹  accident: 多条或单条船轨迹
  private mapstateSubscription: Subscription;
  private historyTimerLayer!: DynamicComponent | undefined;
  private canvasEndPointMarker: any;
  private singleAnimateMarker: any;
  constructor(
    private mapState: MapStateService,
    private mapLayerService: MapLayerComponentService,
  ) {
    this.map = this.mapState.getMapInstance();
    this.trackGroupLayer = L.featureGroup().addTo(this.map);
    this.trackTimePopupLayer = L.featureGroup().addTo(this.map);
    this.animatedMarkerLayers = L.featureGroup().addTo(this.map);
    this.canvasEndPointMarker = canvasLayerFactory(L)({}).addTo(this.map);
    this.mapstateSubscription = this.mapState.mapOptChange
      .pipe(filter((res: MapOption) => res && res.zoomLevel != undefined))
      .subscribe(({ zoomLevel }) => {
        if (zoomLevel! > 10) {
          if (!this.isAdded) {
            this.timePopList.forEach((ele) => {
              this.trackTimePopupLayer.addLayer(ele);
            });
            this.isAdded = true;
          }
        } else {
          this.isAdded = false;
          this.trackTimePopupLayer.clearLayers();
        }
      });
  }

  ngOnDestroy(): void {
    if (this.singleAnimateMarker) this.singleAnimateMarker.stop();
    this.removeLayer();
    this.mapstateSubscription.unsubscribe();
    this.mapState.removeMapContainerClass('history-track-layer');
    this.removeTimerLayer();
  }

  ngOnInit(): void {
    const { params } = this['data'];
    if (params) {
      this.mapState.addMapContainerClass('history-track-layer');
      const { type, list, startTime, endTime, componentLayerList, timer } =
        params;
      this.clearLayer();
      this.removeTimerLayer();
      this.removeCanvasMarkers();
      // 多条船的历史轨迹
      this.trackType = type;
      this.genMultiLine(
        _.cloneDeep(list),
        startTime,
        endTime,
        componentLayerList,
        timer
      );
    }
  }

  /** 移除时间轴 */
  private removeTimerLayer() {
    if (this.historyTimerLayer) {
      this.mapLayerService.removeComponent(this.historyTimerLayer);
      this.historyTimerLayer = undefined;
    }
  }

  /**
   * 多条船的历史轨迹（事故轨迹回放）
   * @param dataList
   */
  private genMultiLine(
    dataList: Array<any>,
    startTime: string,
    endTime: string,
    componentLayerList: Array<DynamicComponent>,
    timer: boolean = false
  ) {
    this.removeCanvasMarkers();
    const endPointMarkers: Array<L.Marker> = [];
    dataList.forEach((ele: any, index: number) => {
      let trackList = genComputedTrackList(ele.trackList);
      if (!trackList || !trackList.length) return
      let timerTrackList = genTimerTrackList2(trackList, startTime, endTime);
      let lastTrack = trackList[trackList.length - 1];
      endPointMarkers.push(genEndPointMarker(lastTrack.latlng!));
      if (trackList.length) {
        const marker = this.genAnimateMarker(
          trackList,
          ele.shipTypeCode,
          getColorByIndex(index),
          timerTrackList,
          false
        );
        this.animatedMarkerLayers.addLayer(marker);
        this.animateMarkerList.push(marker);
      }
    });
    this.canvasEndPointMarker.addLayers(endPointMarkers);
    this.canvasEndPointMarker._reset();
    if (timer) {
      this.historyTimerLayer = this.mapLayerService.addComponent({
        name: 'TrajectoryTimeControllerComponent',
        type: 'popup',
        contentDraggable: true,
        horizontalCentered: true,
        data: {
          params: {
            shipList: dataList,
            startTime,
            endTime,
            markerList: this.animateMarkerList,
            componentLayerList,
            extraHandle: this.trackType == 'accident'
          },
          size: { width: 680 },
          position: { bottom: 40, left: 500 },
        },
      });
    }
  }

  private removeCanvasMarkers() {
    this.canvasEndPointMarker && this.canvasEndPointMarker.clearLayers();
  }

  /**
   * 生成轨迹线
   * @param trackList
   * @param shipTypeCode
   * @param lineColor
   * @param trackList2 500
   * @param autoStart
   * @returns
   */
  private genAnimateMarker(
    trackList: Array<ShipTrajectory>,
    shipTypeCode: string,
    lineColor: string,
    timerTrackList: Array<ShipTrajectory>,
    autoStart: boolean = false
  ) {
    const latlngs: Array<L.LatLng> = [];
    // trackList.map((ele) => ele.latlng!);
    const shipType = this.getShipTypeByCode(shipTypeCode)!; // 船舶种类
    // const weight = this.trackType == 'ship' ? 2 : 1; // 轨迹线粗细
    // const circleMarkerSize = this.trackType == 'ship' ? 4 : 2; // 圆点大小
    const weight = 1;
    const circleMarkerSize = 3;
    // 轨迹圆点
    trackList.forEach(ele => {
      latlngs.push(ele.latlng!)
      // 轨迹圆点
      const circleMarker = this.genCircleMarker(ele.latlng!, circleMarkerSize, lineColor);

      circleMarker.on('click', (e) => {
        stopBubble(e)
        this.mapLayerService.addComponent({
          name: 'ShipTrackInfoPopupComponent',
          type: 'popup',
          data: {
            position: {
              latlng: ele.latlng
            },
            params: {
              model: {
                mmsi: ele.MMSI,
                date: ele.date,
                speed: ele.speed,
                course: ele.course,
                latstr: `${ele.lat}°N`,
                lngstr: `${ele.lng}°E`,
              }
            }
          }
        })
      }).on('mouseover', (e) => {
        this.mapLayerService.addComponent({
          name: 'ShipTrackInfoPopupComponent',
          type: 'popup',
          data: {
            position: {
              latlng: ele.latlng
            },
            params: {
              model: {
                mmsi: ele.MMSI,
                date: ele.date,
                speed: ele.speed,
                course: ele.course,
                latstr: `${ele.lat}°N`,
                lngstr: `${ele.lng}°E`,
              }
            }
          }
        })
      }).on('mouseout', () => {
        this.mapLayerService.removeComponent({ name: 'ShipTrackInfoPopupComponent' })
      })
      circleMarker.addTo(this.trackGroupLayer);
    })
    const routeLine = this.genRouteLine(latlngs, lineColor, weight); // 轨迹线
    // 轨迹线坐标
    const routeLineLatlngs = routeLine.getLatLngs();
    const icon = this.genShipIcon(shipType.icon);
    if (this.trackType == 'ship') {//船舶轨迹 缩放至合适的几倍
      this.map.fitBounds(routeLine.getBounds());
    }

    const animatedMarker = animatedMarkerFactory(L)(routeLineLatlngs, {
      icon,
      distance: 1000,
      interval: 100,
      autoStart,
      data: trackList,
      data2: timerTrackList,
      // playCall: (obj: any) => {
      //   if (this.trackType == 'ship') {
      //     // 单条船轨迹
      //     const { index, lat, lng, realTimeLatlng } = obj;
      //     // const latlng = L.latLng(lat, lng);
      //     // this.genCircleMarker(latlng, 4, shipType.colorValue);
      //     if (realTimeLatlng) this.map.setView(realTimeLatlng);
      //   }
      // },
    });
    if (this.trackType == 'ship') this.singleAnimateMarker = animatedMarker;
    return animatedMarker;
  }
  /**
   * 生成轨迹线上的圆点
   * @param latlng
   */
  private genCircleMarker(latlng: L.LatLng, radius: number, color: string) {
    return L.circleMarker(latlng, {
      radius,
      color,
      fillColor: color,
      opacity: 1,
    });
  }

  /** 时间popup */
  private genTimePopup(latlng: L.LatLng, time: string) {
    const options: L.PopupOptions = {
      closeButton: false,
      autoClose: false,
      autoPan: false,
      closeOnEscapeKey: false,
      className: 'history-time-popup',
      minWidth: 100,
      offset: L.point(0, -10),
    };
    return L.popup(options).setLatLng(latlng).setContent(time);
  }

  /**
   * 生成船舶的轨迹线
   * @param latlngs
   */
  private genRouteLine(
    latlngs: Array<L.LatLng>,
    color: string,
    weight: number
  ) {
    const routeLine = L.polyline(latlngs, {
      weight,
      color,
    }).addTo(this.trackGroupLayer);
    return routeLine;
  }

  /**
   * 生成船的图标
   * @param iconName
   * @param iconSize
   * @param iconAnchor
   * @returns
   */
  private genShipIcon(
    iconName: string,
    iconSize = [20, 20] as any,
    iconAnchor = [10, 10] as any
  ) {
    const option: L.IconOptions = {
      iconUrl: `assets/img/map/ship/${iconName}.png`,
      iconSize,
      iconAnchor,
    };
    return L.icon(option);
  }

  /**
   * 获取船舶的类型
   * 如果未获取到，默认返回其他类型
   */
  private getShipTypeByCode(code: string) {
    const item = getShipType(code);
    return item || getShipType('8');
  }

  /**
   * 清除图层上的popup line marker
   */
  private clearLayer() {
    if (this.trackGroupLayer) this.trackGroupLayer.clearLayers();
    if (this.trackTimePopupLayer) this.trackTimePopupLayer.clearLayers();
    if (this.animatedMarkerLayers) this.animatedMarkerLayers.clearLayers();
    this.removeCanvasMarkers();
  }

  /**
   * 移除图层 ngOnDestroy
   */
  private removeLayer() {
    if (this.trackGroupLayer) this.trackGroupLayer.remove();
    if (this.trackTimePopupLayer) this.trackTimePopupLayer.remove();
    if (this.animatedMarkerLayers) this.animatedMarkerLayers.remove();
    if (this.canvasEndPointMarker) this.canvasEndPointMarker.remove();
  }
}
