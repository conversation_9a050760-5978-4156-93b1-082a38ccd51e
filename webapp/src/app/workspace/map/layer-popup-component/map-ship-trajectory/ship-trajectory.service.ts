import { Injectable } from '@angular/core';
import * as moment from 'moment';
import { DynamicComponent } from 'src/app/shared/models';
import { MapLayerComponentService } from '../../service/map-layer-component.service';

@Injectable({
  providedIn: 'root'
})
export class ShipTrajectoryService {

  private currentLayers: DynamicComponent[] = []

  private trackLayerName: string = 'ShipTrajectoryLayerComponent'

  constructor(private mapLayerService: MapLayerComponentService) { }

  /**
   *
   * 加载船舶轨迹
   * @param {Array<any>} shipList 船舶的历史轨迹 {mmsi:stsring,id:string,trackList:Array<LAT:string,LNG:string...>}
   * @param {string} startValue
   * @param {string} endValue
   * @param {('ship' | 'accident')} type
   * @param componentLayerList 点击时间控制器上的关闭按钮需要关闭的其他组件
   * @memberof ShipTrajectoryService
   */
  addTrackLayer(shipList: Array<any>, startValue: string, endValue: string, type: 'ship' | 'accident', componentLayerList: Array<DynamicComponent> = []) {
    this.removeTrackLayer()
    const trackLayerC = this.mapLayerService.addComponent({
      name: this.trackLayerName,
      type: 'layer',
      data: {
        params: {
          timer: true,
          type,
          list: shipList,
          componentLayerList,
          startTime: moment(startValue).format('YYYY-MM-DD HH:mm'),
          endTime: moment(endValue).format('YYYY-MM-DD HH:mm'),
        }
      }
    })
    this.currentLayers.push(trackLayerC)
  }
  removeTrackLayer() {
    if (this.currentLayers && this.currentLayers.length) {
      this.currentLayers.forEach(ele => this.mapLayerService.removeComponent(ele))
    }
  }

}
