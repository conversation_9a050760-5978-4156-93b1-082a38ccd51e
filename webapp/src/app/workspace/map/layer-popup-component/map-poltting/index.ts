import { PolttingListComponent } from './poltting-list/poltting-list.component';
import { DynamicComponent } from "src/app/shared/models";
import { PolttingHandleComponent } from './poltting-handle/poltting-handle.component';
import { PolttingInfoComponent } from './poltting-info/poltting-info.component';
import { PolttingLayerComponent } from './poltting-layer/poltting-layer.component';

// 标绘
export const DYNAMIC_MAP_POLTTING_COMPONENTS: Array<DynamicComponent> = [
    // 标绘图层
    { name: 'PolttingLayerComponent', component: PolttingLayerComponent },
    // 标绘列表
    { name: 'PolttingListComponent', component: PolttingListComponent, hasChild: true },
    // 标绘详细
    { name: 'PolttingInfoComponent', component: PolttingInfoComponent, parentName: 'PolttingListComponent' },
    // 标绘新增
    { name: 'PolttingAddComponent', component: PolttingHandleComponent, parentName: 'PolttingListComponent', aliasName: 'PolttingHandleComponent' },
    // 标绘编辑
    { name: 'PolttingEditComponent', component: PolttingHandleComponent, parentName: 'PolttingListComponent', aliasName: 'PolttingHandleComponent' },
];