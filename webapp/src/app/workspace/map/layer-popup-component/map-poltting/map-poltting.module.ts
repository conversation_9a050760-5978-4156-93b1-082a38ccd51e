import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzEmptyModule } from 'ng-zorro-antd/empty';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PolttingHandleComponent } from './poltting-handle/poltting-handle.component';
import { PolttingLayerComponent } from './poltting-layer/poltting-layer.component';
import { PolttingListComponent } from './poltting-list/poltting-list.component';
import { PolttingInfoComponent } from './poltting-info/poltting-info.component';
import { FormsModule } from '@angular/forms';
import { SlButtonModule } from 'src/app/shared/modules/sl-button/sl-button.module';
import { MapSharedModule } from '../../map-shared/map-shared.module';
import { ColorPickerModule } from 'ngx-color-picker';

import {
  PolttingCoordinateTableComponent,
} from './poltting-handle'
import { SharedModule } from 'src/app/shared/shared.module';

const POLTTING_HANDLE_COMPONENTS = [
  PolttingCoordinateTableComponent,
]

@NgModule({
  declarations: [
    PolttingHandleComponent,
    PolttingLayerComponent,
    PolttingListComponent,
    PolttingInfoComponent,
    ...POLTTING_HANDLE_COMPONENTS,

  ],
  imports: [
    CommonModule,
    FormsModule,
    NzInputModule,
    SlButtonModule,
    MapSharedModule,
    ColorPickerModule,
    NzInputNumberModule,
    SharedModule,
    NzEmptyModule,
    NzSpinModule
  ]
})
export class MapPolttingModule { }
