import { MapLayerComponentService } from './../../../service/map-layer-component.service';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { MapPolttingService } from '../map-poltting.service';
import { TblDraw, TblDrawSearch } from 'src/app/workspace/workspace-shared/models/tbl_draw';
import { Page } from 'src/app/shared/models';
import { SlModalService } from 'src/app/shared/modules/sl-modal/sl-modal.service';
import * as _ from 'lodash';

@Component({
  selector: 'app-poltting-list',
  templateUrl: './poltting-list.component.html',
  styleUrls: ['./poltting-list.component.less']
})
export class PolttingListComponent implements OnInit, OnDestroy {
  search: TblDrawSearch = new TblDrawSearch()
  list: Array<TblDraw> = []
  page: Page<TblDraw> = new Page()
  spinning: boolean = false
  currentEditItem: TblDraw | undefined

  constructor(
    private mapLayerService: MapLayerComponentService,
    private service: MapPolttingService,
    private slModalService: SlModalService
  ) { }
  ngOnDestroy(): void {
    this.mapLayerService.removeComponent({ name: 'PolttingLayerComponent', destroy: true })
  }

  ngOnInit(): void {
    this.getList()
    // 打开标绘图层
    this.mapLayerService.addComponent({ name: 'PolttingLayerComponent', type: 'layer' })
  }
  searchKeyword() {
    this.search.currentPage = 1
    this.getList()
  }
  delDraw(item: TblDraw) {
    this.slModalService.openPromptModal({
      type: 'confirm', content: '确定删除吗？', okCb: () => {
        this.service.delete(item.id!).then(res => {
          // 刷新页面
          this.search.currentPage = 1
          this.getList()
          // 刷新图层
          this.service.resetLayer()
          this.slModalService.openPromptModal({
            type: 'success',
            content: '删除成功'
          })
        })
      }
    })
  }

  /**
   * 定位
   * @param item 
   */
  location(item: TblDraw) {
    this.service.locationLayer(item.id!)
  }

  /**
   * 显示或隐藏
   * @param item 
   */
  toggle(item: TblDraw) {
    item.active = !item.active
    if (item.active) {
      this.service.showLayerById(item.id!)
    } else {
      this.service.hideLayerById(item.id!)
    }
  }
  addPoltting() {
    this.mapLayerService.addComponent(
      {
        type: 'popup',
        title: '标绘（新增）',
        name: 'PolttingAddComponent',
        data: {
          position: {
            right: 410,
            top: 116
          }
        }
      }
    )
  }

  /**
   * todo : 编辑的时候不隐藏原始图标
   * @param item 
   */
  editDraw(item: TblDraw) {
    item.editActive = true
    this.currentEditItem = item
    // let tmp = false
    // if (item.active) {
    //   tmp = true
    //   // 暂时在图层上隐藏
    //   this.service.hideLayerById(item.id!)
    // }
    this.mapLayerService.addComponent(
      {
        type: 'popup',
        title: '标绘（编辑）',
        name: 'PolttingEditComponent',
        data: {
          position: {
            right: 410,
            top: 116
          },
          params: {
            tblDraw: _.cloneDeep(item),
            closeCb: () => {
              item.editActive = false
              this.currentEditItem = undefined
            }
          }
        },
        closeCb: () => {
          item.editActive = false
          this.currentEditItem = undefined
        }
      }
    )
  }

  /**
   *
   * 分页事件
   * @param {{ currentPage: number }} event
   * @memberof PolttingListComponent
   */
  pageChanged(event: { currentPage: number }) {
    this.search.currentPage = event.currentPage
    this.getList()
  }

  getList() {
    this.spinning = true
    const hiddenLayerIds = this.service.hiddenLayerIds
    this.service.getList(this.search).then(res => {
      this.spinning = false
      this.page = res
      this.list = res.result || []
      this.list.forEach(ele => {
        if (hiddenLayerIds.includes(ele.id!)) {
          ele.active = false
        } else {
          ele.active = true
        }
      })
    }).catch(err => {
      this.spinning = false
    })
  }
}
