<div class="poltting-list-container">
    <div class="search">
        <nz-input-group [nzPrefix]="nzPrefixIconSearch" [nzSuffix]="inputClearTpl"
            style="width: 246px;background:#EEF3Fc;">
            <input type="text" name="drawName" [(ngModel)]="search.name" nz-input placeholder="请输入标绘名称"
                style="background:#EEF3Fc;" />
        </nz-input-group>
        <ng-template #nzPrefixIconSearch>
            <a href="javascript:void(0)" style="display: flex;" (click)="searchKeyword()">
                <i class="sl-input-search-16"></i>
            </a>
        </ng-template>
        <ng-template #inputClearTpl>
            <a href=" javascript:void(0)" style="display: flex;" *ngIf="search.name"
                (click)="search.name='';searchKeyword()">
                <i class="sl-close-circle-white"></i>
            </a>
        </ng-template>
        <button sl-button slType="primary" (click)="addPoltting()">
            <i class="sl-plus-16"></i>
            新增
        </button>
    </div>
    <div class="content">
        <div class="outer">
            <div class="inner">
                <nz-spin [nzSpinning]="spinning" [nzTip]="'数据加载中...'">
                    <table>
                        <tr *ngFor="let item of list;index as i">
                            <td class="title">
                                <a style="display: flex;align-items: center;" href="javascript:void(0)"
                                    (click)="location(item)">
                                    <i
                                        class="sl-{{item.shapeType}}-{{item.shapeType=='point'?item.picName:'active'}}-16"></i>
                                    <span>{{item.name}}</span>
                                </a>
                            </td>
                            <td class="handle">
                                <div>
                                    <a href="javascript:void(0)" (click)="toggle(item)">
                                        <i class="sl-eye-{{item.active===false?'closed':'opened'}}-16"></i>
                                    </a>
                                    <a href="javascript:void(0)" (click)="editDraw(item)">
                                        <i
                                            [ngClass]="currentEditItem&&currentEditItem.id==item.id&&item.editActive?'sl-edit-active-16':'sl-edit-16'">
                                        </i>
                                    </a>
                                    <a href="javascript:void(0)" (click)="delDraw(item)">
                                        <i class="sl-close-red-16"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <tr *ngIf="list&&list.length==0">
                            <td colspan="2">
                                <nz-empty></nz-empty>
                            </td>
                        </tr>
                    </table>
                </nz-spin>
            </div>
        </div>
    </div>
    <app-map-list-pagination [currentPage]="page.currentPage!" [pageRecord]="10" [totalNum]="page.recordCount!"
        [totalPageNum]="page.pageCount!" (pageChange)="pageChanged($event)">
    </app-map-list-pagination>
</div>