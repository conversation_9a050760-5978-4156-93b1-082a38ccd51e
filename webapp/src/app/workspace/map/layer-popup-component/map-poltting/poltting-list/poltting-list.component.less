@import "/src/styles/mixins";
.poltting-list-container {
  width: 356px;
  background: transparent;
  .search {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 12px;
  }
  .content {
    padding-bottom: 18px;
    .outer {
      height: 320px; // 10条数据高度
      overflow: hidden;
      .scrollbars(5px, #C8C9CC, rgba(0, 0, 0, 0));
      .inner {
        padding: 0 12px;
        overflow-y: auto;
        height: 100%;
      }
    }
    table {
      tr {
        &:nth-child(odd) {
          background-image: linear-gradient(
            -87deg,
            rgba(238, 243, 252, 0) 0%,
            #f4f8ff 51%,
            rgba(238, 243, 252, 0) 99%
          );
        }
        td {
          height: 32px;
          &.title {
            & > a {
              color: #333;
            }
            i + span {
              margin-left: 4px;
            }
          }
          &.handle {
            width: 68px;
            text-align: center;
            & > div {
              display: flex;
              align-items: center;
              justify-content: space-around;
            }
          }
        }
      }
    }
  }
}
