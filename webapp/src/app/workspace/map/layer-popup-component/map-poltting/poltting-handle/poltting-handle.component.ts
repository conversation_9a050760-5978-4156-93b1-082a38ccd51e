import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { DynamicComponentData } from 'src/app/shared/models';
import { TblDraw } from 'src/app/workspace/workspace-shared/models/tbl_draw';
import { GRAPH_TYPE_LIST, GrapModel } from '../poltting-type';



@Component({
  selector: 'app-poltting-handle',
  templateUrl: './poltting-handle.component.html',
  styleUrls: ['./poltting-handle.component.less']
})
export class PolttingHandleComponent implements OnInit, OnDestroy {
  isEdit: boolean = false
  @Input() data: DynamicComponentData = new DynamicComponentData()
  tblDraw!: TblDraw
  closeCb!: Function
  // 当前tab 角标
  currentGrap: GrapModel
  // 图形选择 列表
  graphList: Array<GrapModel> = []

  constructor() {
    this.graphList = GRAPH_TYPE_LIST
    this.currentGrap = this.graphList[0]
  }
  ngOnDestroy(): void {
    this.onCancel()
  }

  ngOnInit(): void {
    const { params } = this.data
    if (params && params.tblDraw) {
      // 编辑标绘
      this.isEdit = true
      this.tblDraw = params.tblDraw
      this.currentGrap = this.graphList.find(ele => ele.type == this.tblDraw.shapeType) || new GrapModel()

    } else {
      this.tblDraw = new TblDraw('#ff0000', 'red')
    }
    if (params && params.closeCb) {
      this.closeCb = params.closeCb
    }
  }

  // 切换图形
  switchGraph(item: GrapModel) {
    if (this.currentGrap.type != item.type) {
      this.currentGrap = item
      const tblDraw = new TblDraw('#ff0000', 'red')
      if (this.isEdit) {
        tblDraw.id = this.tblDraw.id
      }
      this.tblDraw = tblDraw
    }
  }

  onCancel() {
    if (this.closeCb && typeof this.closeCb === 'function') {
      this.closeCb()
    }
  }
}
