<form #validateForm="ngForm">
    <table class="handle-table">
        <tr>
            <td class="label required"><span>标绘名称：</span></td>
            <td>
                <input name="polttingName" [slValidate]="{required:true,label:'标绘名称'}" [(ngModel)]="draw.name"
                    type="text" nz-input style="width:290px;height: 28px;" />
                <ng-container [ngTemplateOutlet]="isPoint?pointPicker:colorPicker"></ng-container>
            </td>
        </tr>
        <tr *ngIf="isPoint">
            <td class="label"></td>
            <td>
                <div class="point-picker-container">
                    <div class="row" *ngFor="let children of pointIconList;index as i">
                        <a [title]="item.title" *ngFor="let item of children"
                            [class.selected]="item.icon == draw.picName" href="javascript:void(0)"
                            (click)="switchPointIcon(item)">
                            <i class="sl-point-{{item.icon}}-20"></i>
                        </a>
                    </div>
                </div>
            </td>
        </tr>
        <tr>
            <td class="label">区域坐标：</td>
            <td class="handle">
                <a href="javascript:void(0)" (click)="addCoordinate()" *ngIf="canadd">
                    <i class="sl-plus-circle-16"></i>
                    <span>新增点</span>
                </a>
            </td>
        </tr>

    </table>
    <div class="coordinate-container-outer" [style.height]="maxLen?'180px':'auto'">
        <div class="coordinate-container-inner">
            <div class="coordinate-item" *ngFor="let item of coordinateList;index as i">
                <span class="serial">{{i+1}}</span>
                <input type="number" [slValidate]="{required:true,label:'第'+(i+1)+'行纬度（度）',type:'la_degree'}"
                    name="latD-{{item.uuid}}" (keyup)="posChange(item)" [(ngModel)]="item.latD" nz-input
                    style="width: 42px;" />
                <sup>°</sup>
                <input type="number" [slValidate]="{required:true,label:'第'+(i+1)+'行纬度（分）',type:'la_minute'}"
                    name="latM-{{item.uuid}}" (keyup)="posChange(item)" [(ngModel)]="item.latM" nz-input
                    style="width: 43px;" />
                <sup>′</sup>
                <input type="number" [slValidate]="{required:true,label:'第'+(i+1)+'行纬度（秒）',type:'la_second'}"
                    name="latS-{{item.uuid}}" (keyup)="posChange(item)" [(ngModel)]="item.latS" nz-input
                    style="width: 60px;" />
                <sup>″</sup>
                <span class="unit" style="margin-left: 4px;margin-right: 10px;">
                    {{item.lat?.type ||'N' }}
                </span>
                <input type="number" [slValidate]="{required:true,label:'第'+(i+1)+'行经度（度）',type:'lo_degree'}"
                    name="lngD-{{item.uuid}}" (keyup)="posChange(item)" [(ngModel)]="item.lngD" nz-input
                    style="width: 42px;" />
                <sup>°</sup>
                <input type="number" [slValidate]="{required:true,label:'第'+(i+1)+'行纬度（分）',type:'lo_minute'}"
                    name="lngM-{{item.uuid}}" (keyup)="posChange(item)" [(ngModel)]="item.lngM" nz-input
                    style="width: 43px;" />
                <sup>′</sup>
                <input type="number" [slValidate]="{required:true,label:'第'+(i+1)+'行纬度（秒）',type:'lo_second'}"
                    name="lngS-{{item.uuid}}" (keyup)="posChange(item)" [(ngModel)]="item.lngS" nz-input
                    style="width: 60px;" />
                <sup>″</sup>
                <span class="unit" style="margin-left: 4px;">{{item.lng?.type||'E'}}</span>
                <a class="handle" *ngIf="removable" href="javascript:void(0)" (click)="removeCoordinate(item,i)">
                    <i class="sl-close-red-16"></i>
                </a>
            </div>
        </div>
    </div>
    <table class="handle-table" *ngIf="isCircle">
        <tr>
            <td class="label required">半径：</td>
            <td>
                <input name="rail" (keyup)="radiusChange($event)" [slValidate]="{required:true,label:'半径'}"
                    [(ngModel)]="draw.rail" type="number" nz-input style="width:290px;height: 28px;" />
                <span class="unit" style="margin-left: 4px;">米</span>
            </td>
        </tr>
    </table>
    <div class="footer-container">
        <button sl-button type="button" (click)="cancel()">取消</button>
        <button sl-button type="button" slType="primary" (click)="save(validateForm)">保存</button>
    </div>
</form>
<ng-template #pointPicker>
    <a href="javascript:void(0)" class="point-picker-link">
        <i class="sl-point-{{draw.picName}}-20"></i>
    </a>
</ng-template>
<ng-template #colorPicker>
    <a class="color-picker-link" [cpDialogDisplay]="'popup'" [style.background]="draw.color" [cpPosition]="'bottom'"
        [(colorPicker)]="draw.color" href="javascript:void(0)" [cpOutputFormat]="'hex'"
        (colorPickerChange)="colorPickerChange($event)" title="点击更换背景">
    </a>
    <!-- (colorPickerSelect)="currentBgcolor=$event" [cpOKButton]="true" [cpOKButtonText]="'确定'" [cpOKButtonClass]="'sl-primary-btn'" [cpSaveClickOutside]="false"-->
</ng-template>