@import "/src/styles/mixins";
table.handle-table {
  tr > td {
    position: relative;
    padding: 4px 6px;

    &.label {
      width: 98px;
      text-align: right;
      &.required > span {
        position: relative;
        &::before {
          content: "*";
          color: red;
          width: 8px;
          height: 8px;
          position: absolute;
          left: -9px;
        }
      }
    }
    &.handle {
      text-align: right;
      a {
        display: inline-flex;
        align-items: center;
        i + span {
          margin-left: 4px;
        }
      }
    }
    ul.grap-ul > li {
      display: inline-block;
      & + li {
        margin-left: 16px;
      }
    }
  }
}
.coordinate-container-outer {
  overflow: hidden;
  max-height: 180px; // 5条数据的高度
  .scrollbars(5px, #C8C9CC, rgba(0, 0, 0, 0));
  padding: 0 5px;
  .coordinate-container-inner {
    overflow-y: auto;
    height: 100%;
    .coordinate-item {
      display: flex;
      align-items: center;
      height: 36px;
      padding-left: 8px;
      .serial {
        width: 35px;
        text-align: center;
        display: inline-block;
      }
      .handle {
        width: 35px;
        text-align: center;
        display: inline-flex;
        align-items: center;
        justify-content: center;
      }
      input[type="number"] {
        height: 26px;
        padding: 4px 5px;
        & + sup {
          margin-left: 2px;
          margin-right: 2px;
        }
      }
    }
  }
}

.footer-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 66px;
  [sl-button] + [sl-button] {
    margin-left: 24px;
  }
}

.color-picker-link,
.point-picker-link {
  display: inline-flex;
  width: 30px;
  height: 30px;
  cursor: pointer;
  position: absolute;
  right: 14px;
  top: 2px;
  align-items: center;
}
.point-picker-container {
  display: flex;
  width: 300px;
  flex-wrap: wrap;
  .row {
    display: flex;
    & + .row {
      margin-top: 10px;
    }
  }

  a {
    display: flex;
    width: 24px;
    height: 24px;
    align-items: center;
    justify-content: center;
    & + a {
      margin-left: 31px;
    }
    &:hover:not(.selected) {
      background: #e9f1fe;
    }
    &.selected {
      background: url("/assets/img/map/poltting-selected.png") no-repeat;
    }
  }
}
