<div class="poltting-handle-container">
    <table>
        <tr>
            <td class="label">
                区域形状：
            </td>
            <td>
                <ul class="grap-ul">
                    <li *ngFor="let item of graphList;index as i" [class.active]="currentGrap.type===item.type">
                        <a href="javascript:void(0)" (click)="switchGraph(item)">
                            <i
                                [ngClass]="currentGrap.type===item.type?'sl-'+item.type+'-active-20':'sl-'+item.type+'-20'">
                            </i>
                        </a>
                    </li>
                </ul>
            </td>
        </tr>
    </table>
    <app-poltting-coordinate-table (onCancel)="onCancel()" [isEdit]="isEdit" [draw]="tblDraw" [type]="currentGrap.type"
        [dynamic]="currentGrap.dynamic">
    </app-poltting-coordinate-table>
</div>