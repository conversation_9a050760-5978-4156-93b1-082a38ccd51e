@import "/src/styles/mixins";

.poltting-handle-container {
  background: transparent;
  width: 455px;
  & > table {
    tr > td {
      position: relative;
      padding: 4px 6px;

      &.label {
        width: 98px;
        text-align: right;
      }
      &.handle {
        text-align: right;
        a {
          display: inline-flex;
          align-items: center;
          i + span {
            margin-left: 4px;
          }
        }
      }
      ul.grap-ul > li {
        display: inline-flex;
        justify-content: center;
        align-items: center;
        width: 20px;
        height: 20px;

        & + li {
          margin-left: 16px;
        }
        &.active {
          background: #e5eeff;
          height: 28px;
          width: 28px;
        }
      }
    }
  }
}
