import { TblDraw } from './../../../../../workspace-shared/models/tbl_draw';
import { Subject, Subscription } from 'rxjs';
import { SlValidateService } from './../../../../../../shared/modules/sl-validate/sl-validate.service';
import { grapType, PointModel, POINT_ICON_LIST } from '../../poltting-type';
import { Component, EventEmitter, Input, OnDestroy, OnInit, Output, AfterViewInit, OnChanges, SimpleChanges, ViewChildren, QueryList } from '@angular/core';
import { Coordinate, CoordinatePackage, DynamicComponent, latType, lngType } from 'src/app/shared/models';
import { SlModalService } from 'src/app/shared/modules/sl-modal/sl-modal.service';
import { MapLayerComponentService } from 'src/app/workspace/map/service/map-layer-component.service';
import { NgForm, NgModel } from '@angular/forms';
import { MapDrawToolService } from '../../../map-draw-tool/map-draw-tool.service';
import { DMSToLatlng, latlngToDMS } from 'src/app/workspace/map/utils';
import { MapToolState } from '../../../map-draw-tool/map-tool';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { isEmpty, uuid } from 'src/app/shared/utils';
import { SlValidateDirective } from 'src/app/shared/modules/sl-validate';
import * as L from 'leaflet';
import { MapPolttingService } from '../../map-poltting.service';
import { MapStateService } from 'src/app/workspace/map/service/map-state.service';
@Component({
  selector: 'app-poltting-coordinate-table',
  templateUrl: './poltting-coordinate-table.component.html',
  styleUrls: ['./poltting-coordinate-table.component.less']
})
export class PolttingCoordinateTableComponent implements OnInit, OnDestroy, AfterViewInit, OnChanges {
  @Input() type: grapType = 'polygon'
  @Input() dynamic?: DynamicComponent
  @Input() isEdit: boolean = false
  coordinateList: Array<CoordinatePackage> = [];
  @Input() draw: TblDraw = new TblDraw('#ff0000', 'red')

  @Output() onCancel: EventEmitter<any> = new EventEmitter()

  pointIconList: Array<Array<PointModel>> = []// 点 图标选择列表
  mapToolSub: Subscription
  cachedLayerComponent?: DynamicComponent
  // 坐标点发生改变
  private posTextChange$: Subject<CoordinatePackage> = new Subject()
  private radiusChange$: Subject<number> = new Subject()
  private posTextChangeSub: Subscription
  private radiusChangeSub: Subscription
  // controls
  @ViewChildren(NgModel) validates!: QueryList<SlValidateDirective>

  get currentComponentName() {
    return this.isEdit ? 'PolttingEditComponent' : 'PolttingAddComponent'
  }
  // 可新增坐标点
  get canadd() {
    return this.type == 'polygon' || this.type == 'line'
  }
  // 可移除坐标点
  get removable() {
    return (this.type == 'polygon' && this.len > 3) || (this.type == 'line' && this.len > 2)
  }

  // 当列表数据大于5条时出现滚动条
  get maxLen() {
    return this.coordinateList.length > 4
  }

  get isPoint() {
    return this.type === 'point'
  }

  get isCircle() {
    return this.type == 'circle'
  }

  constructor(
    private slModalService: SlModalService,
    private mapLayerService: MapLayerComponentService,
    private slValidateService: SlValidateService,
    private drawToolService: MapDrawToolService,
    private service: MapPolttingService,
    private mapState: MapStateService
  ) {
    this.radiusChangeSub = this.radiusChange$.pipe(
      debounceTime(2000),
      distinctUntilChanged(),
    ).subscribe(radius => {
      this.redraw()
    })
    this.posTextChangeSub = this.posTextChange$.pipe(
      debounceTime(2000)
    ).subscribe(item => {
      // 用户手动改变北纬和东经数值，重绘
      // 手动输入的方式需要将数组emit 到父组件（coordinateList 与父组件没有关联）
      // 绘制的方式 coordinateList 是从父组件传递过来（引用传递）
      if (isEmpty(item.latD!) || isEmpty(item.latM!) || isEmpty(item.latS!)) return;
      if (isEmpty(item.lngD!) || isEmpty(item.lngM!) || isEmpty(item.lngS!)) return;
      let errors: string[] = []
      // 坐标点校验
      const results = this.validates['_results']
      results.forEach((ele: any) => {
        if (ele.control?.errors) {
          const error: string = ele.control.errors.errMsg?.value
          if (error && error.includes('标绘名称')) return // 不校验标绘名称
          errors.push(error)
        }
      })

      if (!errors.length) {
        const latV = DMSToLatlng(item.latD!, item.latM!, item.latS!)
        const lngV = DMSToLatlng(item.lngD!, item.lngM!, item.lngS!)
        const latType = latV >= 0 ? 'N' : 'S';
        const lngType = lngV >= 0 ? 'E' : 'W';
        item.lat = new Coordinate(item.latD, item.latM, item.latS, latV, latType)
        item.lng = new Coordinate(item.lngD, item.lngM, item.lngS, lngV, lngType)
        item.latlng = L.latLng(latV, lngV)
        this.redraw()
      }
    })
    this.pointIconList = POINT_ICON_LIST
    this.mapToolSub = this.drawToolService.change$.subscribe((res: MapToolState) => {
      // 转换坐标 成 CoordiangePackage
      const { latlng, radius } = res
      if (latlng) {
        const cp = this.latlngToCoordinatePackage(res.latlng)
        this.coordinateList.push(cp)
      }
      if (radius) this.draw.rail = Math.round(radius * 100) / 100
    })
  }
  ngOnChanges(changes: SimpleChanges): void {
    const typeValue = changes['type']
    if (typeValue && typeValue.currentValue != typeValue.previousValue) {
      this.draw.shapeType = typeValue.currentValue
      if (this.draw.shapeType != 'point') {
        this.draw.picName = ''
      }
      this.resetCoordinate()
      this.removeDrawToolLayer()
      // 计算coordinateList
      if (this.isEdit) {
        this.strToCoordinatePkg()
        this.redraw(true)
        // 定位到具体位置
        this.mapState.getMapInstance().setView(this.coordinateList[0].latlng!)
      } else {
        this.openDrawToolLayer()
      }
    }
  }
  ngAfterViewInit(): void { }
  ngOnDestroy(): void {
    this.posTextChangeSub.unsubscribe()
    this.radiusChangeSub.unsubscribe()
    this.mapToolSub.unsubscribe()
    this.removeDrawToolLayer()
  }
  ngOnInit(): void {

  }

  get len() {
    return this.coordinateList.length
  }
  radiusChange(event: any) {
    this.radiusChange$.next(event.target.value)
  }
  // add坐标点
  addCoordinate() {
    const item = new CoordinatePackage()
    item.uuid = uuid()
    this.coordinateList.push(item)
  }

  // 移除坐标点
  removeCoordinate(item: CoordinatePackage, idx: number) {
    this.coordinateList.splice(idx, 1)
    // 移除坐标点后重新绘制
    this.redraw()
  }

  // 切换点的图标
  switchPointIcon(item: PointModel) {
    if (this.draw.picName != item.icon) {
      this.draw.picName = item.icon
      this.redraw()
    }
  }

  // 坐标点发生改变
  posChange(item: CoordinatePackage) {
    this.posTextChange$.next(item)
  }

  // 取色器面板change事件
  colorPickerChange($event: string) {
    this.draw.color = $event;
    this.redraw()
  }


  /**
   * 重新绘制
   * @param fitBounds 缩放到合适的级别
   */
  private redraw(fitBounds?: boolean) {
    const latlngs: Array<L.LatLng> = this.coordinateList.map(
      (ele) => ele.latlng!
    );
    this.openDrawToolLayer(latlngs, fitBounds)
  }

  cancel() {
    this.slModalService.openPromptModal({
      type: 'confirm',
      content: '确定取消吗？',
      okCb: () => {
        this.mapLayerService.removeComponent({ name: this.currentComponentName })
        this.onCancel.emit()
      }
    })
  }

  /**
   * 将坐标包装类转为字符串
   */
  private coordinatePkgToStr() {
    this.draw.drawCoordinate = this.coordinateList.map(ele => {
      const { lat, lng } = ele
      return `${lat.d},${lat.m},${lat.s}#${lat.type}-${lng.d},${lng.m},${lng.s}#${lng.type}`
    }).join(';')
  }

  /**
   * 将字符串转为坐标包装类
   */
  private strToCoordinatePkg() {
    const coordinateStr = this.draw.drawCoordinate
    if (coordinateStr) {
      this.coordinateList = coordinateStr.split(';').map(ele => {
        return ele.split('-')
      }).map(ele => {
        const [latStr, lngStr] = ele
        const [latVStr, latT] = latStr.split('#')
        const [lngVStr, lngT] = lngStr.split('#')
        const [latD, latM, latS] = latVStr.split(',')
        const [lngD, lngM, lngS] = lngVStr.split(',')
        const latSymbol = latT == 'N' ? 1 : -1 // N 为正数 S 为负数
        const lngSymbol = lngT == 'E' ? 1 : -1 // E 为正数 W 为负数
        const latV = DMSToLatlng(parseFloat(latD), parseFloat(latM), parseFloat(latS), 8) * latSymbol
        const lngV = DMSToLatlng(parseFloat(lngD), parseFloat(lngM), parseFloat(lngS), 8) * lngSymbol
        const lat: Coordinate = new Coordinate(parseFloat(latD), parseFloat(latM), parseFloat(latS), latV, <latType>latT)
        const lng: Coordinate = new Coordinate(parseFloat(lngD), parseFloat(lngM), parseFloat(lngS), lngV, <lngType>lngT)
        const cp = new CoordinatePackage(lat, lng)
        cp.latD = parseFloat(latD)
        cp.latM = parseFloat(latM)
        cp.latS = parseFloat(latS)
        cp.lngD = parseFloat(lngD)
        cp.lngM = parseFloat(lngM)
        cp.lngS = parseFloat(lngS)
        cp.latlng = L.latLng(latV, lngV)
        cp.uuid = uuid()
        return cp
      })
    }
  }

  save(validateForm: NgForm) {
    if (!this.isFormValidate(validateForm)) return
    console.log('坐标点', this.coordinateList)
    // 将坐标点拼接为字符串
    this.coordinatePkgToStr()
    console.log('标绘数据', this.draw)
    this.slModalService.openPromptModal({
      type: 'confirm',
      content: '确定保存吗？',
      okCb: () => {
        this.service.save(this.draw).then(res => {
          this.mapLayerService.removeComponent({ name: this.currentComponentName })
          this.slModalService.openPromptModal({
            type: 'success',
            content: '保存成功',
            okCb: () => {
              // 刷新列表与图层
              this.mapLayerService.refreshComponent({ name: 'PolttingListComponent' })
            }
          })
        })
      }
    })
  }

  /**
   * 校验表单
   * @param validateForm 
   * @returns 
   */
  private isFormValidate(validateForm: NgForm) {
    const errors: string[] = []
    if (this.len == 0) {
      errors.push('坐标点不能为空')
    } else {
      if (this.type == 'polygon' && this.len < 3) {
        errors.push('最少需要3个坐标点')
      } else if ((this.type === 'rect' || this.type === 'line') && this.len < 2) {
        errors.push('最少需要2个坐标点')
      }
    }
    errors.push(...this.slValidateService.getFormErrors(validateForm))
    if (errors.length) {
      this.slModalService.openPromptModal({
        type: 'error',
        content: errors
      })
      return false
    }
    return true
  }


  private getMinLen() {
    let len = 1
    switch (this.type) {
      case 'polygon':
        len = 3
        break;
      case 'line':
      case 'rect':
        len = 2
        break;
      case 'circle':
      case 'point':
        break;
    }
    return len
  }

  /**
   * 重置坐标点
   */
  private resetCoordinate() {
    this.coordinateList = []
    // const minLen = this.getMinLen()
    // this.coordinateList.splice(0, this.len)
    // Array(minLen).join(',').split(',').forEach(() => {
    //   this.coordinateList.push(new CoordinatePackage())
    // })
    // console.log('reset', this.coordinateList);
  }

  private openDrawToolLayer(latlngs?: Array<L.LatLng>, fitBounds?: boolean) {
    // console.log('open draw tool');
    if (!this.dynamic) return
    this.dynamic.data = {
      params: {
        color: this.draw.color,
        iconUrl: `/assets/img/map/point/${this.draw.picName}.png`,
        latlngs,
        fitBounds,
        iconSize: [20, 20],
        radius: this.draw.rail
      }
    }
    this.cachedLayerComponent = this.mapLayerService.addComponent(this.dynamic)
  }

  private removeDrawToolLayer() {
    if (this.cachedLayerComponent) {
      // console.log('remove draw tool');
      this.mapLayerService.removeComponent(this.cachedLayerComponent)
    }
  }

  /**
   * 将坐标数转换为CoordinatePackage
   */
  private latlngToCoordinatePackage(latlng: L.LatLng) {
    const item: CoordinatePackage = new CoordinatePackage();
    item.lat = latlngToDMS(latlng.lat);
    item.lng = latlngToDMS(latlng.lng);
    if (item.lat) {
      item.lat.value = latlng.lat;
      item.lat.type = latlng.lat >= 0 ? 'N' : 'S';
      item.latD = item.lat.d
      item.latM = item.lat.m
      item.latS = item.lat.s
    }
    if (item.lng) {
      item.lng.value = latlng.lng;
      item.lng.type = latlng.lng >= 0 ? 'E' : 'W';
      item.lngD = item.lng.d
      item.lngM = item.lng.m
      item.lngS = item.lng.s
    }
    item.latlng = latlng
    item.uuid = uuid()
    return item;
  }
}
