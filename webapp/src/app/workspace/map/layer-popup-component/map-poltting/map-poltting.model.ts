import { grapType } from "./poltting-type";

export class MarkerPoint {
    id: string;
    latlng: L.LatLng;
    imgUrl: string;
    imgSize: [number, number];
    cavasImg?: HTMLImageElement;
    toolTipText?: string;
    constructor(id: string, latlng: <PERSON>.LatLng, imgUrl: string, imgSize: [number, number]) {
        this.id = id
        this.latlng = latlng
        this.imgUrl = imgUrl
        this.imgSize = imgSize
    }
}
export class BasePolttingDraw {
    id: string
    name: string
    type: grapType
    constructor(id: string, name: string, type: grapType) {
        this.id = id
        this.name = name
        this.type = type
    }
}

export class PolttingPolygon extends BasePolttingDraw {
    latlngs: L.LatLng[]
    color: string
    constructor(base: BasePolttingDraw, color: string, latlngs: L.LatLng[]) {
        super(base.id, base.name, base.type)
        this.latlngs = latlngs
        this.color = color
    }
}

export class PolttingLine extends BasePolttingDraw {
    latlngs: L.LatLng[]
    color: string
    constructor(base: BasePolttingDraw, color: string, latlngs: L.LatLng[]) {
        super(base.id, base.name, base.type)
        this.latlngs = latlngs
        this.color = color
    }
}
export class PolttingRect extends BasePolttingDraw {
    latlngs: L.LatLng[]
    color: string
    constructor(base: BasePolttingDraw, color: string, latlngs: L.LatLng[]) {
        super(base.id, base.name, base.type)
        this.latlngs = latlngs
        this.color = color
    }
}
export class PolttingCircle extends BasePolttingDraw {
    radius: number
    latlng: L.LatLng
    color: string
    constructor(base: BasePolttingDraw, color: string, radius: number, latlng: L.LatLng) {
        super(base.id, base.name, base.type)
        this.radius = radius
        this.latlng = latlng
        this.color = color
    }
}

export class PolttingPoint extends BasePolttingDraw {
    imgUrl: string
    latlng: L.LatLng
    iconSize: [number, number]
    cavasImg?: HTMLImageElement;
    constructor(base: BasePolttingDraw, imgUrl: string, latlng: L.LatLng) {
        super(base.id, base.name, base.type)
        this.imgUrl = imgUrl
        this.latlng = latlng
        this.iconSize = [20, 20]
    }
}