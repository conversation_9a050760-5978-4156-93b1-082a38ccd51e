import { debounceTime } from 'rxjs/operators';
import { Subscription } from 'rxjs';
import { PolttingDrawCanvasLayer } from './poltting-draw-canvas-layer';
import { TblDraw } from 'src/app/workspace/workspace-shared/models/tbl_draw';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { MapPolttingService } from '../map-poltting.service';
import { MapStateService } from '../../../service/map-state.service';

@Component({
  selector: 'app-poltting-layer',
  template: ``
})
export class PolttingLayerComponent implements OnInit, OnDestroy {
  list: TblDraw[] = []
  map: L.Map
  polttingCanvasLayer: PolttingDrawCanvasLayer
  private layerChangeSub: Subscription
  constructor(
    private service: MapPolttingService,
    private mapState: MapStateService) {
    this.map = this.mapState.getMapInstance()
    this.polttingCanvasLayer = new PolttingDrawCanvasLayer(service).addTo(this.map)
    this.layerChangeSub = this.service.change$.pipe(
      debounceTime(500)
    ).subscribe(({ id, type }) => {
      if (type === 'toggle') {
        this.polttingCanvasLayer.reset()
      } else if (type === 'location') {
        this.polttingCanvasLayer.locationLayer(id!)
      } else if (type == 'reset') {
        this.getAll()
      }
    })
  }
  ngOnDestroy(): void {
    this.layerChangeSub.unsubscribe()
    this.polttingCanvasLayer.remove()
  }

  ngOnInit(): void {
    this.getAll()
  }

  getAll() {
    this.service.getAll().then(res => {
      this.list = res
      this.polttingCanvasLayer.addPolttingLayer(this.list)
    })
  }

}
