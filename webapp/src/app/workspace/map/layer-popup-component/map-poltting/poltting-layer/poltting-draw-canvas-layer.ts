import { PolttingCircle, PolttingLine, PolttingPoint, PolttingPolygon, PolttingRect } from './../map-poltting.model';
import * as L from "leaflet"
import * as turf from '@turf/turf'
import * as _ from "lodash"
import rbush from 'rbush'
import { TblDraw } from "src/app/workspace/workspace-shared/models/tbl_draw"
import { BasePolttingDraw } from "../map-poltting.model"
import { DMSToLatlng } from '../../../utils';
import { iif } from 'rxjs';
import { MapPolttingService } from '../map-poltting.service';
export class PolttingDrawCanvasLayer extends L.Layer {
    private basePointImgUrl: string = `/assets/img/map/point` // 点的图标
    private pointSize: [number, number] = [20, 20]
    private _canvas!: HTMLCanvasElement
    private _context!: CanvasRenderingContext2D
    options: any
    animationLoop: number | undefined
    // 标绘数据
    cachedPolttingList: Array<TblDraw> = []

    /** 标绘图层 */
    cachedPolttingLayerList: Array<BasePolttingDraw> = []

    private marks = new rbush()

    constructor(private service: MapPolttingService) {
        super()
    }

    /**addTo时会自动调用 */
    onAdd(map: L.Map) {
        this._map = map
        if (!this._canvas) this.initCanvas()
        if (this.options.pane) this.getPane()?.appendChild(this._canvas)
        else map.getPanes().overlayPane.appendChild(this._canvas)
        map.on('moveend', this.reset, this)
        map.on('resize', this.reset, this)
        // map.on('click', this._executeListeners, this)
        // map.on('mousemove', this._executeListeners, this)
        if (map.options.zoomAnimation && L.Browser.any3d) {
            /**缩放动画 */
            map.on('zoomanim', this._animateZoom, this)
        }
        this.reset()
        return this
    }

    /**移除时会自动调用 */
    onRemove(map: any) {
        if (this.options.pane) {
            this.getPane()?.removeChild(this._canvas)
        } else {
            map.getPanes().overlayPane.removeChild(this._canvas)
        }
        map.off('moveend', this.reset, this)
        map.off('resize', this.reset, this)
        if (map.options.zoomAnimation) {
            map.off('zoomanim', this._animateZoom, this)
        }
        if (this.animationLoop) cancelAnimationFrame(this.animationLoop)
        return this
    }

    private initCanvas() {
        this._canvas = L.DomUtil.create(
            'canvas',
            'leaflet-canvas-map leaflet-layer'
        )
        var originProp =
            '' +
            L.DomUtil.testProp([
                'transformOrigin',
                'WebkitTransformOrigin',
                'msTransformOrigin',
            ])
        this._canvas.style[originProp] = '50% 50%'
        this._canvas.style['z-index'] = 50
        var size = this._map.getSize()
        this._canvas.width = size.x
        this._canvas.height = size.y
        this._context = this._canvas.getContext('2d')!
        var animated = this._map.options.zoomAnimation && L.Browser.any3d
        L.DomUtil.addClass(
            this._canvas,
            'leaflet-zoom-' + (animated ? 'animated' : 'hide')
        )
    }

    reset() {
        const topLeft = this._map.containerPointToLayerPoint([0, 0])
        L.DomUtil.setPosition(this._canvas, topLeft)
        var size = this._map.getSize()
        this._canvas.width = size.x
        this._canvas.height = size.y
        this._redraw()
    }

    locationLayer(id: string) {
        const i = this.cachedPolttingLayerList.findIndex(ele => ele.id == id)
        if (i > -1) {
            const item = this.cachedPolttingLayerList[i]
            if (item.type === 'rect' || item.type == 'polygon') {
                const polygon = <PolttingPolygon>item
                this._map.fitBounds(L.polygon(polygon.latlngs).getBounds())
            } else if (item.type === 'line') {
                const line = <PolttingLine>item
                this._map.fitBounds(L.polyline([...line.latlngs]).getBounds())
            } else {
                this._map.setView((<PolttingPoint>item).latlng)
            }
        }
    }

    addPolttingLayer(list: TblDraw[]) {
        this.cachedPolttingList = _.cloneDeep(list)
        this.cachedPolttingLayerList = []
        let tmp: any[] = []
        list.forEach(ele => {
            tmp = this._addProtectLayer(ele)
        })
        this.marks.load(tmp)
        this._redraw()
    }

    private _redraw() {
        this._clearContext()
        const hiddenLayerIds = this.service.hiddenLayerIds
        const activatedList = this.cachedPolttingLayerList.filter(ele => !hiddenLayerIds.includes(ele.id))
        activatedList.forEach(ele => {
            this._redrawPolttingLayer(ele)
        })
    }

    /**
     * 重绘保护区
     * @param ele 
     */
    private _redrawPolttingLayer(ele: BasePolttingDraw) {
        switch (ele.type) {
            case 'polygon':
            case 'rect':
                this._drawPloygon(<PolttingPolygon>ele)
                break
            case 'line':
                this._drawLine(<PolttingLine>ele)
                break
            case 'circle':
                this._drawCircle(<PolttingCircle>ele)
                break
            case 'point':
                this._drawPoint(<PolttingPoint>ele)
                break;
        }
    }

    /**
     * 绘制保护区
     * @param ele 
     */
    private _addProtectLayer(tblDraw: TblDraw) {
        let tmp: any[] = []
        const latlngs: L.LatLng[] = this.strToCoordinatePkg(tblDraw)
        const latlng = latlngs[0]
        const shapeType = tblDraw.shapeType!
        const id = tblDraw.id!
        const name = tblDraw.name!
        const color = tblDraw.color!
        const base = new BasePolttingDraw(id, name, shapeType)
        let item: any
        switch (shapeType) {
            case 'polygon':
                item = new PolttingPolygon(base, color, latlngs)
                this._drawPloygon(item)
                break
            case 'line':
                item = new PolttingLine(base, color, latlngs)
                this._drawLine(item)
                break
            case 'rect':
                const [latlng1, latlng2] = latlngs
                const latlngList = [latlng1, L.latLng(latlng1.lat, latlng2.lng), latlng2, L.latLng(latlng2.lat, latlng1.lng)]
                item = new PolttingRect(base, color, latlngList)
                this._drawPloygon(item)
                break;
            case 'circle':
                if (latlng) {
                    item = new PolttingCircle(base, color, tblDraw.rail!, latlng)
                    this._drawCircle(item)
                }
                break;
            case 'point':
                if (latlng) {
                    item = new PolttingPoint(base, `${this.basePointImgUrl}/${tblDraw.picName}.png`, latlng)
                    item.iconSize = this.pointSize
                    const ret = this._drawPoint(item)
                    tmp.push(ret[0])
                }
                break;
        }
        if (item) {
            this.cachedPolttingLayerList.push(item)
        }
        return tmp
    }

    /**
     * 
     * 绘制point
     * @private
     * @param {PolttingPoint} point
     * @return {*} 
     * @memberof PolttingDrawCanvasLayer
     */
    private _drawPoint(pointMarker: PolttingPoint) {
        pointMarker.cavasImg = this._drawImage(pointMarker.latlng, pointMarker.iconSize, pointMarker.imgUrl, pointMarker.cavasImg)
        const point = this._map.latLngToContainerPoint(pointMarker.latlng)
        this._setAreaName(point, pointMarker.name)
        if (!this.marks) this.marks = new rbush()
        const iconSize = pointMarker.iconSize
        const adj_x = iconSize[0] / 2
        const adj_y = iconSize[1] / 2
        const ret = [
            {
                minX: point.x - adj_x,
                minY: point.y - adj_y,
                maxX: point.x + adj_x,
                maxY: point.y + adj_y,
                data: pointMarker,
            },
            {
                minX: pointMarker.latlng.lng,
                minY: pointMarker.latlng.lat,
                maxX: pointMarker.latlng.lng,
                maxY: pointMarker.latlng.lat,
                data: pointMarker,
            },
        ]
        return ret
    }

    /**
     * 绘制圆形
     * @param {L.LatLng} latlng
     * @param {number} radius
     * @param {string} color
     * @param {string} name
     * @memberof PolttingDrawCanvasLayer
     */
    _drawCircle(circle: PolttingCircle) {
        const point = this._map.latLngToContainerPoint(circle.latlng)
        const _radius = this._getLatRadius(circle.radius, circle.latlng)
        const ctx = this._context
        ctx.strokeStyle = circle.color
        ctx.fillStyle = circle.color
        ctx.globalAlpha = 1
        ctx.lineDashOffset = 0
        ctx.setLineDash([0, 0])
        ctx.beginPath()
        ctx.arc(point.x, point.y, _radius, 0, 2 * Math.PI)
        ctx.closePath()
        ctx.globalAlpha = 0.2
        ctx.fill()
        ctx.stroke()

        this._setAreaName(point, circle.name)
    }

    /**
     *
     * 绘制线
     * @param {PolttingLine} line
     * @memberof PolttingDrawCanvasLayer
     */
    _drawLine(line: PolttingLine) {
        const points = this.transformLatlngsToPoints(line.latlngs);
        const ctx = this._context
        ctx.strokeStyle = line.color
        ctx.fillStyle = line.color
        ctx.globalAlpha = 1
        ctx.lineDashOffset = 0
        ctx.setLineDash([0, 0])
        ctx.lineWidth = 3
        const len = points.length
        for (let i = 0; i < len; i++) {
            const { x, y } = points[i]
            if (i == 0) {
                ctx.beginPath()
                ctx.moveTo(x, y)
            } else if (i == len - 1) {
                ctx.lineTo(x, y);
                ctx.stroke()
            }
            else {
                ctx.lineTo(x, y)
            }
        }
        let centerIndex = len % 2 == 0 ? len / 2 : (len - 1) / 2
        const center = points[centerIndex]
        this._setAreaName(center, line.name)
    }

    /**
     *
     * 绘制多边形
     * @private
     * @param {PolttingPolygon} polygon
     * @memberof PolttingDrawCanvasLayer
     */
    private _drawPloygon(polygon: PolttingPolygon) {
        const points = this.transformLatlngsToPoints(polygon.latlngs)
        const ctx = this._context
        ctx.strokeStyle = polygon.color
        ctx.fillStyle = polygon.color
        ctx.globalAlpha = 1
        ctx.lineDashOffset = 0
        ctx.setLineDash([0, 0])
        const len = points.length
        ctx.beginPath()
        for (let i = 0; i < len; i++) {
            const { x, y } = points[i]
            if (i == 0) {
                ctx.moveTo(x, y)
            } else if (i == len - 1) {
                ctx.lineTo(x, y);
            } else {
                ctx.lineTo(x, y)
            }
        }
        ctx.closePath()
        ctx.globalAlpha = 0.2
        ctx.fill()
        ctx.stroke()

        // 设置名称
        const center = this._getGeoJSONCenter(polygon.latlngs)
        this._setAreaName(center, polygon.name)
    }

    /**
     *
     * 绘制图标
     * @private
     * @param {PolttingPoint} pointMarker
     * @memberof PolttingDrawCanvasLayer
     */
    private _drawImage(latlng: L.LatLng, size: [number, number], imgUrl: string, img?: HTMLImageElement): HTMLImageElement | undefined {
        const ctx = this._context
        ctx.globalAlpha = 1
        const point = this._map.latLngToContainerPoint(latlng)
        const x = point.x - size[0] / 2,
            y = point.y - size[1] / 2
        if (!img && imgUrl) {
            img = new Image()
            img.src = imgUrl
            img.onload = () => {
                ctx.translate(point.x, point.y)
                ctx.rotate(0)
                ctx.drawImage(img!, x, y, size[0], size[1])
                ctx.rotate(-0)
                ctx.translate(point.x, point.y)
            }
        }
        ctx.drawImage(img!, x, y, size[0], size[1])
        return img!
    }

    /**
    *
    * 设置画笔样式
    * @private
    * @param {number} type 0 内环 1 外环
    * @memberof PolttingDrawCanvasLayer
    */
    private _setCtxStyle(type: number) {
        let ctx = this._context
        let color, fillColor
        if (type === 0) { // 内环 
            color = 'rgba(255,0,0,0.40)'//#4D9CDD
            fillColor = 'rgba(255,0,0,0.20)'//#3696E0
            ctx.setLineDash([12, 6])
        } else if (type == 1) { // 外环
            color = 'rgba(255,102,0,0.4)' //9B4FD3
            fillColor = 'rgba(255,102,0,0.15)'//9B4FD3
            ctx.setLineDash([12, 6])
        } else {
            color = '#FD6666 '
            fillColor = '#2DB4FC'
            ctx.setLineDash([0, 0])
        }
        ctx.globalAlpha = 1
        ctx.lineWidth = 2
        ctx.strokeStyle = color
        ctx.fillStyle = fillColor
    }

    /**
     *
     * 通过坐标点数组获取绝对中心点
     * @private
     * @param {Array<L.LatLng>} latlngs
     * @return {*}  {L.Point}
     * @memberof PolttingDrawCanvasLayer
     */
    private _getGeoJSONCenter(latlngs: Array<L.LatLng>): L.Point {
        const features = turf.featureCollection(
            latlngs.map((ele) => turf.point([ele.lat, ele.lng]))
        )
        const center = turf.center(features).geometry.coordinates
        return this._map.latLngToContainerPoint(L.latLng(center[0], center[1]))
    }

    /**
     *
     * 将字符串转为坐标包装类
     * @private
     * @param {TblDraw} draw
     * @memberof PolttingDrawCanvasLayer
     */
    private strToCoordinatePkg(draw: TblDraw): Array<L.LatLng> {
        const coordinateStr = draw.drawCoordinate!
        const coordinateLatlngStrList = coordinateStr.split(';').map(ele => {
            return ele.split('-')
        })
        return coordinateLatlngStrList.map(ele => {
            const [latStr, lngStr] = ele
            const [latVStr, latT] = latStr.split('#')
            const [lngVStr, lngT] = lngStr.split('#')
            const [latD, latM, latS] = latVStr.split(',')
            const [lngD, lngM, lngS] = lngVStr.split(',')
            const latSymbol = latT == 'N' ? 1 : -1 // N 为正数 S 为负数
            const lngSymbol = lngT == 'E' ? 1 : -1 // E 为正数 W 为负数
            const latV = DMSToLatlng(parseFloat(latD), parseFloat(latM), parseFloat(latS), 8) * latSymbol
            const lngV = DMSToLatlng(parseFloat(lngD), parseFloat(lngM), parseFloat(lngS), 8) * lngSymbol
            return L.latLng(latV, lngV)
        })
    }

    /**
     *
     * 设置保护区名称
     * @private
     * @param {L.Point} point
     * @param {string} areaName
     * @return {*} 
     * @memberof PolttingDrawCanvasLayer
     */
    private _setAreaName(point: L.Point, areaName: string) {
        const zoom = this._map.getZoom()
        if (!areaName || zoom < 10) return
        const ctx = this._context
        this._setCtxStyle(0)
        ctx.beginPath()
        ctx.font = '14px "微软雅黑"'
        ctx.textAlign = 'center'
        ctx.fillStyle = '#000'
        ctx.textBaseline = 'middle'
        ctx.globalAlpha = 1
        ctx.fillText(areaName, point.x, point.y + 16)
        ctx.stroke()
    }


    /**
     *
     * 清空画布
     * @private
     * @return {*}  {boolean}
     * @memberof PolttingDrawCanvasLayer
     */
    private _clearContext(): boolean {
        let map = this._map
        if (L.Browser.canvas && map) {
            let ctx = this._context,
                ww = this._canvas.width,
                hh = this._canvas.height
            ctx.clearRect(0, 0, ww, hh) // 清空画布
            return true
        }
        return false
    }

    private _animateZoom(e: any) {
        let map: any = this._map
        var scale = map.getZoomScale(e.zoom),
            offset = map
                ._getCenterOffset(e.center)
                ._multiplyBy(-scale)
                .subtract(map._getMapPanePos())
        L.DomUtil.setTransform(this._canvas, offset, scale)
    }

    /**
    *
    * 将坐标点数组转为point数组
    * @private
    * @param {string} coordinateStr
    * @return {*}  {Array<L.Point>}
    * @memberof PolttingDrawCanvasLayer
    */
    private transformLatlngsToPoints(latlngs: Array<L.LatLng>): Array<L.Point> {
        return latlngs.map((ele) =>
            this._map.latLngToContainerPoint(ele)
        )
    }

    /**
    *
    * 根据坐标点计算 canvas 上的对应的point 点
    * @private
    * @param {number} radius
    * @param {L.LatLng} latlng
    * @return {*} 
    * @memberof PolttingDrawCanvasLayer
    */
    private _getLatRadius(radius: number, latlng: L.LatLng) {
        const lng = latlng.lng,
            lat = latlng.lat,
            map = this._map,
            crs = map.options.crs!,
            Earth: any = L.CRS.Earth
        let _radius = 0
        if (crs.distance === Earth.distance) {
            var d = Math.PI / 180,
                latR = radius / Earth.R / d,
                top = map.project([lat + latR, lng]),
                bottom = map.project([lat - latR, lng]),
                p = top.add(bottom).divideBy(2),
                lat2 = map.unproject(p).lat,
                lngR =
                    Math.acos(
                        (Math.cos(latR * d) - Math.sin(lat * d) * Math.sin(lat2 * d)) /
                        (Math.cos(lat * d) * Math.cos(lat2 * d))
                    ) / d

            if (isNaN(lngR) || lngR === 0) {
                lngR = latR / Math.cos((Math.PI / 180) * lat) // Fallback for edge case, #2425
            }
            const _point = p.subtract(map.getPixelOrigin())
            _radius = isNaN(lngR) ? 0 : p.x - map.project([lat2, lng - lngR]).x
            const _radiusY = p.y - top.y
        } else {
            const latlng2 = crs.unproject(crs.project(latlng).subtract([radius, 0]))
            const _point = this._map.latLngToLayerPoint(latlng)
            _radius = _point.x - this._map.latLngToLayerPoint(latlng2).x
            // console.log('_radius', _radius)
        }
        return _radius
    }
}