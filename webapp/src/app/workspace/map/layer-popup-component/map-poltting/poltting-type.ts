
import { DynamicComponent } from "src/app/shared/models";

export type grapType = 'polygon' | 'rect' | 'circle' | 'line' | 'point'

export class GrapModel {
    type: grapType = 'polygon'
    dynamic: DynamicComponent = {}
}

export class PointModel {
    icon: string = '';
    title: string = '';
}
// 图形形状
export const GRAPH_TYPE_LIST: Array<GrapModel> = [
    { type: 'polygon', dynamic: { name: 'MapToolPolygonLayerComponent', type: 'layer' } },// 多边形
    { type: 'rect', dynamic: { name: 'MapToolRectLayerComponent', type: 'layer' } }, // 矩形
    { type: 'circle', dynamic: { name: 'MapToolCircleLayerComponent', type: 'layer' } },// 圆
    { type: 'line', dynamic: { name: 'MapToolLineLayerComponent', type: 'layer' } },// 线
    { type: 'point', dynamic: { name: 'MapToolPointComponent', type: 'layer' } },
]
// 点 图标选择
export const POINT_ICON_LIST: Array<Array<PointModel>> = [
    [
        { icon: 'red', title: '标注点1' },
        { icon: 'green', title: '标注点2' },
        { icon: 'blue', title: '标注点3' },
        { icon: 'purple', title: '标注点4' },
        { icon: 'ship', title: '船厂' },
        { icon: 'tree', title: '岛屿' },
    ],
    [
        { icon: 'navmark', title: '电厂' },
        { icon: 'block', title: '风力发电' },
        { icon: 'lighthouse', title: '浮标' },
        { icon: 'park', title: '港口' },
        { icon: 'mansion', title: '炼厂' },
        { icon: 'hammer', title: '码头' },
    ],
    [
        { icon: 'anchor', title: '锚地' },
        { icon: 'signB', title: '泊位' },
        { icon: 'truck', title: '油库' },
        { icon: 'piano', title: '钻井点' },
        { icon: 'bridge', title: '钻井平台' },
    ]

]