import { Subject } from 'rxjs';
import { TblDraw } from './../../../workspace-shared/models/tbl_draw';
import { BaseCURDService } from 'src/app/shared/services/base-curd.service';
import { Injectable } from '@angular/core';
import { BaseHttpService } from 'src/app/shared/services/base-http.service';
import * as _ from 'lodash';

@Injectable({
  providedIn: 'root'
})
export class MapPolttingService extends BaseCURDService<TblDraw> {
  // 控制图形显示与隐藏/定位/重绘
  private layerChange$: Subject<{ id?: string, type: string }> = new Subject()

  // 隐藏的标绘id
  private hideLayerIds: Array<string> = []

  // 临时隐藏的标绘：编辑时 隐藏 canvas 上的标绘
  private tmpHideLayerIds: Array<string> = []


  constructor(protected http: BaseHttpService) {
    super(http, '/api/Draw')
  }

  getAll() {
    return this.http.get<TblDraw[]>(`/api/Draw/getAll`);
  }

  get hiddenLayerIds(): Array<string> {
    return _.cloneDeep(this.hideLayerIds)
  }

  get change$() {
    return this.layerChange$.asObservable()
  }

  resetLayer() {
    this.layerChange$.next({ type: 'reset' })
  }

  locationLayer(id: string) {
    this.layerChange$.next({ id, type: 'location' })
  }


  private toggleLayerById(id: string) {
    this.layerChange$.next({ id, type: 'toggle' })
  }

  hideLayerById(id: string) {
    const i = this.hideLayerIds.findIndex(ele => ele == id)
    if (i == -1) {
      this.hideLayerIds.push(id)
    }
    this.toggleLayerById(id)
  }

  showLayerById(id: string) {
    const i = this.hideLayerIds.findIndex(ele => ele == id)
    if (i >= -1) {
      this.hideLayerIds.splice(i, 1)
    }
    this.toggleLayerById(id)
  }
}
