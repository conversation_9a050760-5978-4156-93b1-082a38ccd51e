import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ShipListComponent } from './ship-list/ship-list.component';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { FormsModule } from '@angular/forms';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzInputModule } from 'ng-zorro-antd/input';
import { ShipWindpowerInfoComponent } from './ship-windpower-info/ship-windpower-info.component';
import { SlButtonModule } from 'src/app/shared/modules/sl-button/sl-button.module';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { MapSharedModule } from '../../map-shared/map-shared.module';
import { SharedModule } from 'src/app/shared/shared.module';
import { RouterModule } from '@angular/router';
import { WindpowerShipLayerComponent } from './windpower-ship-layer/windpower-ship-layer.component';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzEmptyModule } from 'ng-zorro-antd/empty';
import { NzMessageModule } from 'ng-zorro-antd/message';

@NgModule({
  declarations: [ShipListComponent, ShipWindpowerInfoComponent, WindpowerShipLayerComponent],
  imports: [
    CommonModule,
    FormsModule,
    NzSelectModule,
    NzButtonModule,
    NzInputModule,
    SlButtonModule,
    NzRadioModule,
    NzDatePickerModule,
    MapSharedModule,
    SharedModule,
    RouterModule,
    NzSpinModule,
    NzEmptyModule,
    NzMessageModule
  ],
})
export class WindpowerShipModule { }
