// 船舶图标路径
const baseShipIconUrl = '/assets/img/map/ship';
// 货物/船舶类型
interface SHIPMODEL {
  typeCode?: string; // 类型标识
  name?: string; // 类型名称
  icon1?: string; // canvas layer 图层显示的icon
  icon?: string; // 页面显示的icon
  colorValue?: string; // 颜色代码
  iconClass?: string;
}
export const SHIP_TYCODES: Array<SHIPMODEL> = [
  {
    typeCode: '1',
    name: '货船',
    icon1: 'cargo-ship2',
    icon: 'cargo',
    colorValue: '#FF0000',
    iconClass: 'sl-ship-cargo-14'
  },
  {
    typeCode: '2',
    name: '油轮',
    icon1: 'oiler2',
    icon: 'oil',
    colorValue: '#0000FF',
    iconClass: 'sl-ship-oiler-14'
  },
  {
    typeCode: '3',
    name: '客船',
    icon1: 'passenger-ship2',
    icon: 'passenger',
    colorValue: '#AC00C7',
    iconClass: 'sl-ship-passenger-14'
  },
  {
    typeCode: '4',
    name: '高速船',
    icon1: 'high-speed-ship2',
    icon: 'high-speed',
    colorValue: '#03A8A8',
    iconClass: 'sl-ship-high-speed-14'
  },
  {
    typeCode: '5',
    name: '渔船',
    icon1: 'fisher2',
    icon: 'fisher',
    colorValue: '#005E00',
    iconClass: 'sl-ship-fisher-14'
  },
  {
    typeCode: '6',
    name: '游艇',
    icon1: 'yacht2',
    icon: 'yacht',
    colorValue: '#15B3FF',
    iconClass: 'sl-ship-yacht-14'
  },
  {
    typeCode: '7',
    name: '拖船/特种船',
    icon1: 'tugboat2',
    icon: 'special',
    colorValue: '#749F00',
    iconClass: 'sl-ship-tugboat-14'
  },
  {
    typeCode: '8',
    name: '其他',
    icon1: 'other-ship2',
    icon: 'other',
    colorValue: '#959595',
    iconClass: 'sl-ship-other-14'
  },
]

// 1报警  2 白名单图标
export const SHIP_MARKTYPES = [
  {
    typeCode: '1',
    name: '报警',
    icon: 'alarm',
  },
  {
    typeCode: '2',
    name: '白名单',
    icon: 'white-list',
  },
]

/**
 * 通过typeCode获取该项
 * @param typeCode 
 * @param {{boolean}} type true 查船舶类型  false 查报警白名单类型
 * @returns 
 */
export const getWindopowerShipType = (typeCode: string, type?: boolean, scrennCode?: string, isAllSearch?: boolean) => {
  if (type) {
    const list: Array<any> = SHIP_TYCODES
    return list.find((ele) => ele.typeCode == typeCode);
  } else {
    // 查报警白名单类型，但不考虑typeCode有多个情况
    if (isAllSearch) {
      const list: Array<any> = SHIP_TYCODES
      return list.find((ele) => ele.typeCode == typeCode);
    }
    // 查报警白名单类型，但考虑typeCode有多个情况  如 typeCode:'1,2';  typeCode:'1,3'
    const list: Array<any> = SHIP_MARKTYPES
    const typeList = typeCode.split(',')
    if (!scrennCode) return;
    const findNum = typeList.findIndex(ele => ele == scrennCode)
    if (findNum !== -1) {
      return list.find((ele) => ele.typeCode == scrennCode);
    }
  }
};

/**
 * 通过typeCode获取该项后再获取icon
 * @param typeCode 
 * @param type true 查船舶类型  false 查报警白名单类型
 * @returns 
 */
export const getWindopowerShipIconClass = (typeCode: string, type: boolean, scrennCode?: string) => {
  const item = getWindopowerShipType(typeCode, type, scrennCode);
  return item ? item.icon : '';
};

export const getWindopowerShipIcon = (typeCode: string) => {
  const item = getWindopowerShipType(typeCode, true);
  const iconName = item ? item.icon1 : 'other-ship1';
  return `${baseShipIconUrl}/${iconName}.png`;
};

