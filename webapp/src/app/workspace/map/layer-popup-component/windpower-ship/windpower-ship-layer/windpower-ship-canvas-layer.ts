import * as L from "leaflet"
import rbush from 'rbush'

export class WindpowerShipCanvasLayer extends L<PERSON>Layer {
    private _onClickListeners: Array<(event: L.LeafletMouseEvent, ret: any) => void> = [];
    private _onHoverListeners: Array<(event: L.LeafletMouseEvent, ret: any) => void> = [];
    private _canvas!: HTMLCanvasElement
    private _context!: CanvasRenderingContext2D
    private _options!: L.LayerOptions
    private _latlngMarkers = new rbush(); // rbush
    private _markers = new rbush(); // rbush
    private _imageLookup: any; // 通过图片获取
    private _openToolTip: any;
    constructor() {
        super()
    }


    // 地图产生 map.on 打开相应事件
    onAdd(map: L.Map) {
        this._map = map
        if (!this._canvas) this.initCanvas() // 初始化画布
        if (this._options?.pane) {
            this.getPane()?.appendChild(this._canvas)
        } else {
            map.getPane('overlayPane')?.appendChild(this._canvas)
        }
        map.on('moveend', this._reset, this)  // 地图中心停止变化时 触发
        map.on('resize', this._reset, this)  // 调整地图大小时 触发
        map.on('click', this._executeListeners, this) // 点击 双击地图时 触发
        map.on('mousemove', this._executeListeners, this) // 鼠标在地图上移动时 触发
        if (map.options.zoomAnimation && L.Browser.any3d) {
            // 缩放动画
            map.on('zoomanim', this._animateZoom)
        }
        return this
    }

    onRemove(map: L.Map) {
        if (this._options?.pane) {
            this.getPane()?.removeChild(this._canvas)
        } else {
            map.getPane('overlayPane')?.removeChild(this._canvas)
        }
        map.off('moveed', this._reset)
        map.off('resize', this._reset)
        map.off('click', this._executeListeners)
        map.off('mousemove', this._executeListeners)
        if (map.options.zoomAnimation) {
            map.off('zoomanim', this._animateZoom);
        }
        return this
    }

    initialize(options: L.LayerOptions) {
        this._options = options
        // setOptions 将给定的第二个参数属性合并到第一个对象
        L.setOptions(this, options)
        this._onClickListeners = []
        this._onHoverListeners = []
    }
    // 改变options
    setOptions(options: L.LayerOptions) {
        L.setOptions(this, options)
        return this.redraw()
    }

    /**
     * 初始化画布
     * @memberof WindpowerShipCanvas
     */
    private initCanvas() {
        // DomUtil.create     HTML元素,添加到HTML元素的类名
        this._canvas = L.DomUtil.create('canvas', 'leaflet-canvas-icon-layer leaflet-layer')
        // DomUtil.testProp 遍历样式名称数组并返回有效存在的第一个名称
        const originProp: string = <string>L.DomUtil.testProp(
            [
                'transformOrigin',
                'WebkitTransformOrigin',
                'msTransformOrigin',
            ]);
        this._canvas.style[originProp] = '50% 50%'
        // 获取当前地图的大小
        const size = this._map.getSize()
        this._canvas.width = size.x
        this._canvas.height = size.y
        this._context = this._canvas.getContext('2d')!
        const animated = this._map.options.zoomAnimation && L.Browser.any3d;
        // DomUtil.addClass（html,name） 添加name 到html元素类属性
        L.DomUtil.addClass(this._canvas, 'leaflet-zoom' + (animated ? 'animated' : 'hide'))
    }

    // 重绘画布
    private _reset = () => {
        // containerPointToLayerPoint: 给定一个相对于地图容器的像素坐标，返回相对于原点像素的对应像素坐标。
        const topLeft = this._map?.containerPointToLayerPoint([0, 0])
        L.DomUtil.setPosition(this._canvas, topLeft);
        const size = this._map?.getSize()
        this._canvas.width = size.x
        this._canvas.height = size.y
        this._redraw()
    }
    /**
     * canvas 画布清空
     */
    redraw() {
        this._redraw(true)
    }

    private _redraw(clear: boolean = false) {
        if (clear) this._context.clearRect(0, 0, this._canvas.width, this._canvas.height);
        if (!this._map || !this._latlngMarkers) return;
        let temp: any = [];
        if (this._latlngMarkers.dirty / this._latlngMarkers.total >= 0.1) {
            this._latlngMarkers.all().forEach((item: any) => {
                temp.push(item)
            });
            this._latlngMarkers.clear()
            this._latlngMarkers.load(temp);
            this._latlngMarkers.dirty = 0
            temp = []
        }
        const mapBounds = this._map.getBounds()
        const mapBoxCoords = {
            minX: mapBounds.getWest(),
            minY: mapBounds.getSouth(),
            maxX: mapBounds.getEast(),
            maxY: mapBounds.getNorth(),
        };
        this._latlngMarkers.search(mapBoxCoords).forEach((item: any) => {
            // latLngToContainerPoint 给定地理坐标 返回相对于地理容器的相应像素坐标
            const pointPos = this._map.latLngToContainerPoint(item.data.getLatLng())
            const iconSize = item.data.options.icon.options.iconSize;
            const adj_x = iconSize[0] / 2
            const adj_y = iconSize[1] / 2
            const newCoords = {
                minX: pointPos.x - adj_x,
                minY: pointPos.y - adj_y,
                maxX: pointPos.x + adj_x,
                maxY: pointPos.y + adj_y,
                data: item.data
            };
            temp.push(newCoords)
            this._drawMarker(item.data, pointPos)
        })

        // rbush
        this._markers.clear()
        this._markers.load(temp)
    }

    private _drawMarker(marker: L.Marker, pointPos: L.Point) {
        if (!this._imageLookup) this._imageLookup = {}
        if (!pointPos) {
            pointPos = this._map.latLngToContainerPoint(marker.getLatLng())
        }
        const iconUrl = marker.options.icon!.options.iconUrl!  // 拿到坐标的icon 的 图标url
        if (marker['canvas_img']) {
            this._drawImage(marker, pointPos)
        } else {
            if (this._imageLookup[iconUrl]) {
                marker['canvas_img'] = this._imageLookup[iconUrl][0];
                if (this._imageLookup[iconUrl][1] === false) {
                    this._imageLookup[iconUrl][2].push([marker, pointPos]);
                } else {
                    this._drawImage(marker, pointPos)
                }
            } else {
                const i = new Image()
                i.src = iconUrl!
                marker['canvas_img'] = i  // 生成marker 数组里保存iconurl 
                this._imageLookup[iconUrl] = [i, false, [[marker, pointPos]]];
                i.onload = () => {
                    this._imageLookup[iconUrl][1] = true;
                    this._imageLookup[iconUrl][2].forEach((e: [L.Marker, L.Point]) => {
                        this._drawImage(e[0], e[1])
                    })
                }
            }
        }
    }

    // 绘制icon 图标到画布上
    private _drawImage(marker: L.Marker, pointPos: L.Point) {
        const iconOptions = marker.options.icon!.options;
        let iconRotateDeg = iconOptions['rotateDeg']
        if (typeof iconRotateDeg != 'undefined') {
            // 进行旋转
            this._context.translate(pointPos.x, pointPos.y); // translate 重新映射画布上的位置
            const rotateDeg = (iconRotateDeg / 180) * Math.PI;// 计算角度
            this._context.rotate(rotateDeg); // rotate 旋转方法 角度转换为弧度 公式 xx*Math.Pi/180
            this._context.drawImage(marker['canvas_img'],  // drawImage 绘制图像 规定图像宽度和高度
                -iconOptions.iconSize![0] / 2,
                -iconOptions.iconSize![1] / 2,
                iconOptions.iconSize![0],
                iconOptions.iconSize![1]
            );
            this._context.rotate(-rotateDeg);
            this._context.translate(-pointPos.x, -pointPos.y);
        } else {
            this._context.drawImage(
                marker['canvas_img'],
                pointPos.x - iconOptions.iconSize![0] / 2,
                pointPos.y - iconOptions.iconSize![1],
                iconOptions.iconSize![0],
                iconOptions.iconSize![1]
            );
        }
    }


    // 动画
    private _animateZoom = (e: any) => {
        let map: any = this._map
        var scale = map.getZoomScale(e.zoom),
            offset = map
                ._getCenterOffset(e.center)
                ._multiplyBy(-scale)
                .subtract(map._getMapPanePos())
        L.DomUtil.setTransform(this._canvas, offset, scale)
    }

    addLayer(layer: L.Layer) {
        if (layer['options'].pane === 'markerPane' && (layer['options'] as L.MarkerOptions).icon) {
            this.addMarker(<L.Marker>layer)
        }
        else console.error('layer is not a marker');
    }

    clearLayers() {
        this._latlngMarkers = null
        this._markers = null;
        this.redraw()
    }
    addOnClickListener(listener: (event: L.LeafletMouseEvent, ret: any) => void) {
        this._onClickListeners.push(listener);
    }
    addRestClickListener(listener: (event: L.LeafletMouseEvent, ret: any) => void) {
        this._onClickListeners = [listener];
    }

    addMarker(marker: L.Marker) {
        const latlng = marker.getLatLng();
        // 判断该地图的可见地理范围 是否存在改点 latlng  contains 判断是否包含
        const isDisplaying: boolean = this._map.getBounds().contains(latlng);
        const dat = this._addMarker(marker, latlng, isDisplaying);
        if (isDisplaying) this._markers.insert(dat[0])  // rbush insert() 方法
        this._latlngMarkers.insert(dat[1]) //rbush insert() 方法
    }

    addMarkers(markers: Array<L.Marker>) {
        const tempMark: any = []
        const tempLatLng: any = []
        markers.forEach((item: any) => {
            if (!(item.options.pane == 'markerPane' && item.options.icon)) {
                // 不存在icon markerPane
                console.error("layer is not a marker");
                return;
            }
            const latlng = item.getLatLng()  // 返回标记的当前地理位置
            const isDisplaying: boolean = this._map.getBounds().contains(latlng); // 判断可见范围内是否存在该点
            const x = this._addMarker(item, latlng, isDisplaying);
            if (isDisplaying === true) tempMark.push(x[0]);
            tempLatLng.push(x[1])
        });
        if (tempMark.length) this._markers.load(tempMark) //rbush load
        if (tempLatLng.length) this._latlngMarkers.load(tempLatLng) //rbush load
    }


    private _addMarker(marker: L.Marker, latlng: L.LatLng, isDisplaying: boolean) {
        marker['_map'] = this._map;
        if (!this._markers) this._markers = new rbush()
        if (!this._latlngMarkers) {
            // rbush
            this._latlngMarkers = new rbush()
            this._latlngMarkers.dirty = 0
            this._latlngMarkers.total = 0
        }
        L.Util.stamp(marker); // 返回对象的唯一ID
        const pointPos = this._map.latLngToContainerPoint(latlng) // 给定地理坐标 返回相对于地理容器的坐标
        const markOpt = marker.options
        const iconSize = markOpt.icon!.options.iconSize!;

        const adj_x = iconSize[0] / 2;
        const adj_y = iconSize[1] / 2;
        const ret = [
            {
                minX: pointPos.x - adj_x,
                minY: pointPos.y - adj_y,
                maxX: pointPos.x + adj_x,
                maxY: pointPos.y + adj_y,
                data: marker,
            },
            {
                minX: latlng.lng,
                minY: latlng.lat,
                maxX: latlng.lng,
                maxY: latlng.lat,
                data: marker,
            }
        ];
        this._latlngMarkers.dirty++;
        this._latlngMarkers.total++;
        if (isDisplaying === true) {
            this._drawMarker(marker, pointPos);
        }
        return ret;
    }

    private _executeListeners = (event: L.LeafletMouseEvent) => {
        if (!this._markers) return;
        const x = event.containerPoint.x;
        const y = event.containerPoint.y;

        if (this._openToolTip) {
            this._openToolTip.closeTooltip();
            delete this._openToolTip;
        }

        const ret = this._markers.search({ minX: x, minY: y, maxX: x, maxY: y });

        if (ret && ret.length > 0) {
            // getContainer 获取地图图层的HTML元素 若该为cursor 鼠标样式设置为 pointer
            this._map.getContainer().style.cursor = 'pointer';

            // 地图上的事件为click时 发出事件listener
            if (event.type === 'click') {
                const hasPopup = ret[0].data.getPopup();
                if (hasPopup) ret[0].data.openPopup();

                this._onClickListeners.forEach((listener: (event: L.LeafletMouseEvent, ret: any) => void) => {
                    listener(event, ret);
                });
            }


            if (event.type === 'mousemove') {
                const hasTooltip = ret[0].data.getTooltip();
                if (hasTooltip) {
                    this._openToolTip = ret[0].data;
                    ret[0].data.openTooltip();
                }

                this._onHoverListeners.forEach((listener: (event: L.LeafletMouseEvent, ret: any) => void) => {
                    listener(event, ret);
                });
            }
        }

        else {
            this._map.getContainer().style.cursor = '';
        }
    }

}