import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import * as L from 'leaflet';
import { Subscription } from 'rxjs';
import { MapLayerComponentService } from '../../../service/map-layer-component.service';
import { MapStateService } from '../../../service/map-state.service';
import { ShipOrigin } from '../windpower-ship-model';
import { getWindopowerShipIcon } from '../windpower-ship-type';
import { WindpowerShipService } from '../windpower-ship.service';
import { WindpowerShipCanvasLayer } from './windpower-ship-canvas-layer';
import { debounceTime, filter } from 'rxjs/operators';
import { GlobalState } from 'src/app/global.state';

@Component({
  selector: 'app-windpower-ship-layer',
  template: ``,
})
export class WindpowerShipLayerComponent implements OnInit, OnDestroy {
  @Input() data: any; // 后续需要参数
  map: L.Map
  shipCanvasLayer = new WindpowerShipCanvasLayer() // 声明船舶图层类
  viewBounds: L.LatLngBounds; // 地理范围坐标
  shipTypeCodes = ['1', '2', '3', '4', '5', '6', '7', '8'] // 船舶图层类型 根据数据库表生成 
  shipChangeSub: Subscription
  mapStateSub: Subscription
  constructor(
    private mapState: MapStateService,
    private service: WindpowerShipService,
    private mapLayerService: MapLayerComponentService,
    private globalState: GlobalState
  ) {
    this.map = this.mapState.getMapInstance() // 取得地图实例
    // 给定图层
    this.shipCanvasLayer.addTo(this.map)  // 添加canvas图层到leaflet
    this.viewBounds = this.map.getBounds() // 获取地图可见范围 getBounds
    // 实时监听船舶类型的code 显示隐藏相应code
    this.shipChangeSub = this.service.changes.pipe(debounceTime(500)).subscribe(codes => {
      this.shipTypeCodes = codes;
      this.getShips()
    });
    // 实时监听地图可见地理范围的变化，并取到地理坐标 发请求  debounceTime 防抖间隔
    this.mapStateSub = this.mapState.mapOptChange.pipe(filter((res) => res && res.viewBounds != undefined), debounceTime(500)).subscribe(({ viewBounds }) => {
      this.viewBounds = viewBounds!
      this.getShips();
    });
  }
  get zoomLevel() {
    return this.map.getZoom() // 更新获取当前地图的缩放级别
  }

  ngOnInit(): void {
    this.getShips()
    this.map.on('moveend', () => {
      this.getShips()
    })
  }
  ngOnDestroy(): void {
    if (this.shipCanvasLayer) this.shipCanvasLayer.remove()
    this.shipChangeSub.unsubscribe()
    this.mapStateSub.unsubscribe()
  }

  /**
   * 获取给定区域内的所有船舶
   * @memberof WindpowerShipLayerComponent
   */
  getShips() {
    const bounds = this.viewBounds; // 获取当前地图中心范围
    if (!this.shipTypeCodes || this.shipTypeCodes.length == 0) {
      // 不存在code 则移除canvas
      this.removeCanvasMarkers()
      return;
    }
    this.service.getShips(bounds).then(res => {
      // 传递船舶数量
      this.globalState.notifyDataChanged('bottomLeftShipNumbers', (res && res.length) || 0);
      if (!res.length) return;
      const markers: Array<L.Marker> = this.buildMarkers(res)
      this.addCanvasMarkers(markers)
    })
  }

  /**
   * 构建marker
   * @param shipOrigins 接口返回的原始数据 a b c d e f g
   * @returns 
   * @memberof WindpowerShipLayerComponent
   */
  private buildMarkers(shipOrigins: ShipOrigin[]): Array<L.Marker> {
    const markers: Array<L.Marker> = []
    shipOrigins.forEach(item => {
      const type = item.f || '8' // 没有返回类型则指定为8 其他类型
      const codeBool = this.shipTypeCodes.includes(type) //判断有无type与命名的code相等
      if (codeBool) {
        const latlng = L.latLng(item.c, item.b)  //L.latlng(lat,lng)
        const icon = this.buildMarkerIcon(item.d, type, item.a)
        const marker: L.Marker = L.marker(latlng, {
          icon,
          autoPan: false, // 拖动标记是否平移地图
          zIndexOffset: 300 // 标记放在其他标记的顶部或下方  （正负值）
        });
        // 手动带参到标记数
        marker['mmsi'] = item.a;
        marker['latlng'] = latlng;
        marker['title'] = item.g; // shipName
        markers.push(marker);
      }
    })
    return markers
  }

  /**
   * 给定多个marker 绘制到canvas里
   * @param markers 
   * @memberof WindpowerShipLayerComponent
   */
  private addCanvasMarkers(markers: Array<L.Marker>) {
    this.shipCanvasLayer.clearLayers();
    this.shipCanvasLayer.addMarkers(markers); // 生成多个图标 addMarkers

    // 做个条件判断 没有markers 不做生成发请求操作
    if (!(markers && markers.length)) return;
    // 点击图层的船舶 带数据 生成详情组件
    this.shipCanvasLayer.addRestClickListener((event, ret) => {
      const data = ret[0].data
      const { mmsi, latlng } = data
      this.service.getInfo(mmsi).then(res => {
        let newParams: any;
        newParams = res
        this.mapLayerService.addComponent({
          title: `${newParams.shipName || newParams.mmsi}（详细）`,
          name: 'ShipWindpowerInfoComponent',
          type: 'popup',
          titleType: 'primary',
          data: {
            params: newParams,
            // 使用海图的定位弹出详情
            position: { latlng, offset: [0, 24] }
          }
        })
      })
    })
  }

  /**
   * 根据marker类型生成icon图标
   * @param deg 旋转角度
   * @param shipType 船舶类型 1---8
   * @param mmsi 船舶mmsi
   * @returns 
   */
  private buildMarkerIcon(deg: number, shipType: string, mmsi: string): L.Icon {
    const iconSize = this.getIconSizeByZoom(this.zoomLevel) // 大小
    const iconUrl = getWindopowerShipIcon(shipType) // 图片 url
    // icon 配置项 options
    const iconOpt: L.IconOptions = {
      iconUrl,
      iconSize: [iconSize, iconSize],
      iconAnchor: [iconSize / 2, iconSize / 2]
    }
    iconOpt['mmsi'] = mmsi // 手动给定mmsi参
    if (deg) iconOpt['rotateDeg'] = deg;
    return L.icon(iconOpt)
  }

  /**
 *
 * 根据地图缩放级别计算icon大小
 * @private
 * @param {number} zoomLevel
 * @return {*} 
 * @memberof ShipLayerComponent
 */
  private getIconSizeByZoom(zoomLevel: number) {
    return zoomLevel < 8
      ? 8
      : zoomLevel < 10
        ? 10
        : zoomLevel < 12
          ? 12
          : zoomLevel < 16
            ? 16
            : 20;
  }

  private removeCanvasMarkers() {
    this.shipCanvasLayer && this.shipCanvasLayer.clearLayers()
  }

}
