export class MarkShipSave {
    areaIds?: string;  // 关联保护区  可多选 
    areaNames?: string; // 关联保护区名称
    enableType?: string; // 生效类型  永久生效0  自定义 1 
    endTime?: string; // 自定义生效时间
    startTime?: string;
    id?: string; // 船舶id
    markType?: string; // 船舶类型分别  空普通 1报警2白名单3事故
    mmsi?: string;
    shipTypeCode?: string; // 货物/船舶类型代码  参照 windpower-ship-data.ts
    shipName?: string; // 船舶名称

    remark?: string; // 备注
}