<div class="ship-info">
  <div class="info-container">
    <!-- 主体数据区 -->
    <span class="body_title">AIS信息：</span>
    <div class="body_content">
      <table>
        <tr>
          <td>MMSI：</td>
          <td>{{ship.mmsi||'--'}}</td>
          <td>呼号：</td>
          <td>{{ship.callsign||'--'}}</td>
        </tr>
        <tr>
          <td>IMO：</td>
          <td>{{ship.imo||'--'}}</td>
          <td>类型：</td>
          <td>{{ship.shipTypeName||'--'}}</td>
        </tr>
        <tr>
          <td>船艏向：</td>
          <td>{{ship.heading!==null?ship.heading+'°':'--'}}</td>
          <td>船迹向：</td>
          <td>{{'--'}}</td>
        </tr>
        <tr>
          <td>状态：</td>
          <td colspan="3">{{ship.navigationStatusName||'--'}}</td>
        </tr>
        <tr>
          <td>经度：</td>
          <td>{{ship.longitude||'--'}}</td>
          <td>纬度：</td>
          <td>{{ship.latitude||'--'}}</td>
        </tr>
        <tr>
          <td>船长：</td>
          <td>{{ship.shipLength!==null?ship.shipLength+'m':'--'}}</td>
          <td>船宽：</td>
          <td>{{ship.shipWidth!==null?ship.shipWidth+'m':'--'}}</td>
        </tr>
        <tr>
          <td>转向率：</td>
          <td>{{ship.rateOfTurn!==null?ship.rateOfTurn:'--'}}</td>
          <td>对地航速：</td>
          <td>{{ship.speedOverGround!==null?ship.speedOverGround+'节':'--'}}</td>
        </tr>
        <tr>
          <td>对地航向：</td>
          <td>{{ship.courseOverGround!==null?ship.courseOverGround+'°':'--'}}</td>
          <td>目的地：</td>
          <td>{{ship.destination||'--'}}</td>
        </tr>
        <tr>
          <td>吃水：</td>
          <td>{{ship.draught!==null?ship.draught+'m':'--'}}</td>
          <td>位置准确度：</td>
          <td>{{ship.positionAccuracy&&ship?.positionAccuracy===0?'低':'高'||'--'}}</td>
        </tr>
        <tr>
          <td>预达时间：</td>
          <td>{{ship.eta||'--'}}</td>
          <td>更新时间：</td>
          <td>{{ship.reportTime||'--'}}</td>
        </tr>
      </table>
    </div>
    <!-- 报警记录区 -->
    <!-- 报警记录区 -->
    <!-- 报警记录区 -->
    <ng-container [ngSwitch]="shipSection">
      <ng-container *ngSwitchCase="'0'">
        <div class="body_bottom">
          <!-- <table>
        <tr>
          <td>报警记录：</td>
          <td class="textNum" style="padding-left: 10px;">
            <a href="javascript:void(0)"(click)="toAlarmAccident(ship.mmsi!,'alarm')" > 
              <i class="sl-alarm-16"></i>
              <span class="textNum_data">
                {{ship.alarmRecordCount}}
                次
              </span>
              <span class="ass">
                >>
              </span>
            </a>
          </td>
          <td style="padding-right: 10px;">事故记录：</td>
          <td class="textNum">
            <a href="javascript:void(0)" (click)="toAlarmAccident(ship.mmsi!,'accident')">
              <i class="sl-accident-16"></i>
            <span class="textNum_data">
              {{ship.accidentRecordCount}}
              次
            </span>
            <span class="ass">
              >>
            </span>
          </a>
          </td>
        </tr>
        <tr>
          <td>白名单：</td>
          <td class="white-effect" colspan="3">
            <ng-container [ngSwitch]="ship.markship&&ship.markship.enableType">
              <ng-container *ngSwitchCase="'1'">
            <span *ngIf="effect=='already'">
              生效中（至{{ship.markship!.endTime}}）
            </span>
            <span *ngIf="effect=='not'">
              未生效（{{ship.markship!.startTime}}至{{ship.markship!.endTime}}生效）
            </span>
            <span *ngIf="effect=='invalid'">
              已失效
            </span>
          </ng-container>
            <ng-container *ngSwitchCase="'0'">
            <span *ngIf="ship.markship&&ship.markship.enableType==='0'">
              永久生效
            </span>
          </ng-container>
            <span *ngIf="!ship.markship" style="color: #333;">
                --
            </span>
          </ng-container>
          </td>
        </tr>
      </table> -->
        </div>
      </ng-container>
      <!-- 白名单记录区 -->
      <!-- 白名单记录区 -->
      <!-- 白名单记录区 -->
      <ng-container *ngSwitchCase="'1'">
        <div class="body_bottom-whitelist">
          <form #validateForm="ngForm">
            <table class="table-whitelist">
              <tr>
                <td style="width: 64px">
                  <span style="
                font-size: 16px;
                color: #333;
                font-weight: 500;
              ">
                    白名单：
                  </span>
                </td>
                <!-- 多选框 保护区 -->
                <td class="white-name">
                  <label for="">关联保护区：</label>
                </td>
                <td>
                  <nz-select [nzMaxTagCount]="2" [nzMaxTagPlaceholder]="tagPlaceHolder" nzMode="multiple" nzPlaceHolder="请选择关联保护区"
                    [(ngModel)]="whiteVal" style="width: 322px" name="whiteVal" [nzShowArrow]="true">
                    <!-- 改用第二种校验方式 -->
                    <!-- [slValidate]="{ required: true, label: '关联保护区' }" -->
                    <nz-option *ngFor="let item of whiteProarea" [nzLabel]="item.areaName" [nzValue]="item"></nz-option>
                  </nz-select>
                  <ng-template #tagPlaceHolder let-selectedList>+{{ selectedList.length }}...</ng-template>
                </td>
              </tr>
              <tr>
                <td></td>
                <!-- 时间框  -->
                <td class="white-name" style="padding-top: 13px;">
                  <label for="">生效时间：</label>
                </td>
                <td class="tdflex" rowspan="2">
                  <nz-radio-group [(ngModel)]="dateType" name="whiteName">
                    <div class="tdflex_radio">
                      <label nz-radio nzValue="A" (click)="permanentDate()" nzValue="0">永久生效</label>
                      <div class="tdflex_radio-data">
                        <label nz-radio nzValue="B" (click)="customDate()" nzValue="1">自定义</label>
                        <nz-range-picker style="width: 240px" [nzDisabled]="dateType=='0'" [(ngModel)]="whiteDate"
                          nzFormat="yyyy-MM-dd" name="whiteDate" (ngModelChange)="isWhiteDate=false"></nz-range-picker>
                        <!-- 改用第二种校验方式 -->
                        <!-- [slValidate]="datePickerValidate" -->
                        <!-- [slValidate]=" { required: true, label: '请输入自定义时间' }" -->
                      </div>
                    </div>
                  </nz-radio-group>
                </td>
              </tr>
            </table>
          </form>
        </div>
      </ng-container>
      <!-- 轨迹跟踪区 -->
      <!-- 轨迹跟踪区 -->
      <!-- 轨迹跟踪区 -->
      <ng-container *ngSwitchCase="'2'">
        <div class="body_bottom-track">
          <span class="track_title">轨迹跟踪：</span>
          <div class="track_datepick">
            <nz-date-picker [nzShowTime]="{ nzFormat: 'HH:mm' }" style="width: 168px" class="control" nzFormat="yyyy-MM-dd HH:mm"
              nzPlaceHolder="请选择开始时间" nzShowTime [(ngModel)]="startTimeHistory">
            </nz-date-picker>
            <span class="split">
              ~
            </span>
            <nz-date-picker style="width: 168px" [nzShowTime]="{ nzFormat: 'HH:mm' }" class="control" nzFormat="yyyy-MM-dd HH:mm"
              nzPlaceHolder="请选择结束时间" nzShowTime [(ngModel)]="endTimeHistory">
            </nz-date-picker>

          </div>
        </div>
      </ng-container>
    </ng-container>

    <!-- 下方按钮区 -->
    <div class="body_btn">
      <ng-container [ngSwitch]="shipSection">
        <!-- 初始按钮 -->
        <!-- 初始按钮 -->
        <!-- 初始按钮 -->
        <ng-container *ngSwitchCase="'0'">
          <!-- <div class="btn" >
        <button sl-button slType="success" (click)="toWhiteList(ship)" *slHasAnyAuthority="'1.2.2'" style="width: 92px;">
          白名单
        </button>
        <button sl-button slType="primary" (click)="toTrack()" style="width: 92px;">轨迹跟踪</button>
      </div> -->
        </ng-container>
        <!-- 白名单按钮 -->
        <!-- 白名单按钮 -->
        <!-- 白名单按钮 -->
        <ng-container *ngSwitchCase="'1'">
          <div class="btn">
            <button sl-button slType="danger" *ngIf="ship.markship" (click)="onWhiteDel(ship.mmsi!)">删除</button>
            <button sl-button slType="default" (click)="back()">取消</button>
            <button sl-button slType="primary" (click)="saveByWhite(ship)">保存</button>
          </div>
        </ng-container>
        <!-- 轨迹跟踪按钮 -->
        <!-- 轨迹跟踪按钮 -->
        <!-- 轨迹跟踪按钮 -->
        <ng-container *ngSwitchCase="'2'">
          <div class="btn">
            <button sl-button slType="default" (click)="back()">取消</button>
            <button sl-button slType="primary" (click)="searchTrack(ship)">查询</button>
          </div>
        </ng-container>
      </ng-container>
    </div>
  </div>
</div>