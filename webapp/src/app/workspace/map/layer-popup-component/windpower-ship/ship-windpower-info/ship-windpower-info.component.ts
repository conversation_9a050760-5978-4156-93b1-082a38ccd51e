import { Component, Input, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { NgForm } from '@angular/forms';
import { SlValidateService } from 'src/app/shared/modules/sl-validate/sl-validate.service';
import * as moment from 'moment';
import { WindpowerShipService } from '../windpower-ship.service';
import * as L from 'leaflet';
import { MapStateService } from '../../../service/map-state.service';
import { TblShip } from 'src/app/workspace/workspace-shared/models/tbl_ship';
import { TblMarkHistory } from 'src/app/workspace/workspace-shared/models/tbl_markship';
import { MarkShipSave } from './ship-windpower-info.model';
import { SlModalService } from 'src/app/shared/modules/sl-modal/sl-modal.service';
import { Router } from '@angular/router';
import { ShipTrajectoryService } from '../../map-ship-trajectory/ship-trajectory.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { MapLayerComponentService } from '../../../service/map-layer-component.service';
import { DynamicComponent } from 'src/app/shared/models';
import { transformPosUnit } from '../../../utils';
import { ReuseTabService } from 'src/app/layout/services';
import { SlValidateOption } from 'src/app/shared/modules/sl-validate';
import { isEmpty } from 'lodash';

@Component({
  selector: 'app-ship-windpower-info',
  templateUrl: './ship-windpower-info.component.html',
  styleUrls: ['./ship-windpower-info.component.less'],
})
export class ShipWindpowerInfoComponent implements OnInit, OnDestroy {
  @Input() data: any;
  // 列表传递过来的数据
  // @Input() data!:DynamicComponentData
  @ViewChild('validateForm') validateForm!: NgForm;
  // 白名单点击
  isWhiteList: boolean = false;
  // 轨迹跟踪点击
  isTrack: boolean = false;
  // 初始下方按钮态
  isInitBtn: boolean = false;
  // 白名单条件 关联保护区
  whiteVal!: any;
  // 白名单保护区列表数据
  whiteProarea!: Array<any>
  // 白名单条件 生效时间
  whiteDate: Array<any> = [];
  isWhiteDate: boolean = true;
  // 轨迹跟踪日期选择
  startTimeHistory: Date | null = null;
  endTimeHistory: Date | null = null;
  // 船舶详细类型
  shipWindpower!: any
  // 区转换
  shipSection: string = '0'
  map: L.Map
  // 页面数据
  ship: TblShip = new TblShip()
  // 轨迹查询条件
  historySearch: TblMarkHistory = new TblMarkHistory()
  formatStart = 'YYYY-MM-DD 00:00:00'  // 白名单日期格式化
  formatEnd = 'YYYY-MM-DD 23:59:59'  // 白名单日期格式化
  formatHistory = 'YYYY-MM-DD HH:mm:00'  // 轨迹查询日期格式化 后台传递数据 固定精确到秒为 00
  format3 = 'YYYY-MM-DD'  // 船舶详细区  白名单显示格式
  // 白名单保存
  whiteSave: MarkShipSave = new MarkShipSave()
  dateType: string = '0' //  生效时间类型 0永久生效 1自定义  默认为0
  selectedMarker!: L.Marker; // 船舶选中后生成给定图标
  effect: string = 'already' || 'not' || 'invalid' // 白名单生效显示  已生效 未生效 已失效 
  // 轨迹  是否销毁轨迹回放组件
  historyComponent: boolean = false
  constructor(
    private slValidateService: SlValidateService,
    private service: WindpowerShipService,
    private mapSate: MapStateService,
    private slModalService: SlModalService,
    private router: Router,
    private shipTrajectoryService: ShipTrajectoryService,
    private nzMessage: NzMessageService,
    private mapLayerService: MapLayerComponentService,
    private reuseReuseService: ReuseTabService
  ) {
    this.map = this.mapSate.getMapInstance()
  }


  ngOnInit(): void {
    const { params } = this.data
    // 移除之前的选中的图标 若有
    this.removeSelectedMark()
    this.ship = params
    if (this.ship.latitude && this.ship.longitude) {
      this.computLat(this.ship.latitude, this.ship.longitude)
    }
    if (this.ship.markship) {
      this.computMarkShipDate(this.ship)
    }
  }
  private computMarkShipDate(ship: TblShip) {
    if (ship.markship && ship.markship.enableType == '2') return;
    const newDate = moment().format(this.format3)  // 当前本地时间
    // 白名单时间
    const endTime = ship.markship?.endTime
    const startTime = ship.markship?.startTime
    const newEndTime = moment(endTime).format(this.format3)
    const newStartTime = moment(startTime).format(this.format3)
    ship.markship!.endTime = moment(endTime).format(this.format3)
    ship.markship!.startTime = moment(startTime).format(this.format3)
    const startDiff: number = moment(newStartTime).diff(moment(newDate), 'days')
    const endDiff: number = moment(newEndTime).diff(moment(newDate), 'days')
    if (startDiff > 0) {
      this.effect = 'not'
    } else if (endDiff < 0) {
      this.effect = 'invalid'
    } else {
      this.effect = 'already'
    }
    // diff1 < 0 已失效
    // else 生效中
    // diff2 > 0 未生效
  }
  ngOnDestroy(): void {
    this.removeSelectedMark();  // 销毁组件时同样要移除选中的图标
    this.desHistoryCom()  // 销毁轨迹回放组件
  }
  /**
   * 白名单按钮
   */
  toWhiteList(ship: TblShip) {
    this.shipSection = '1'
    // 获取保护区列表
    this.service.getProareaList().then(res => {
      this.whiteProarea = res
      // 存在白名单数据
      if (ship.markship) {
        const datas = ship.markship
        const datasIds = datas.areaIds?.split(',')
        let whiteArr: any = []
        if (!this.whiteProarea) return;
        this.whiteProarea.forEach(i => {
          datasIds?.forEach(item => {
            if (item === i.id) {
              whiteArr.push(i)
            }
          })
        })
        this.whiteVal = whiteArr
        this.dateType = datas.enableType!
        if (ship.markship.enableType == '0') return;
        if (datas.startTime && datas.endTime) {
          this.whiteDate = [
            new Date(datas.startTime),
            new Date(datas.endTime)
          ]
        }
      } else {
        // 初始化状态
        this.dateType = '0'
        this.whiteDate = []
        this.whiteVal = []
      }
    }).catch(err => {
      console.error(err);
    })
  }
  /**
   * 自定义日期
   */
  customDate() {
    this.isWhiteDate = false;
  }
  /**
 * 永久生效
 */
  permanentDate() {
    this.isWhiteDate = true;
    this.whiteDate = [];
    this.whiteSave.enableType = '0'
  }

  /**
   * 
   * 判断  选的是永久生效、或选了自定义时间，且有输入自定义时间 返回 true ; 否则 选了自定义时间未输入自定义时间 返回false
   * @param item 
   * @returns boolean true 可保存 false 不可
   */

  private customDateVal(item: boolean): boolean {
    // 永久生效
    if (item) {
      this.whiteSave.startTime = undefined
      this.whiteSave.endTime = undefined
      return true;
    } else {
      // 判断有无输入自定义时间 方可保存 
      // this.ifWhiteDate()
      const bool = this.ifWhiteDate()
      if (bool) {
        const [start, end] = this.whiteDate
        this.whiteSave.startTime = moment(start).format(this.formatStart)
        this.whiteSave.endTime = moment(end).format(this.formatEnd)
        return true;
      } else {
        return false;
      }
    }
  }
  // 判断自定义时间有无输入
  private ifWhiteDate(): boolean {
    return this.whiteDate && this.whiteDate.length ? true : false
  }

  // 变更保护区列表选择 带参形式
  private comAreaNameIds(data: Array<any>) {
    if (data && data.length == 0) return;
    const newValId: any = []
    const newValName: any = []
    data?.forEach(ele => {
      newValId.push(ele.id)
      newValName.push(ele.areaName)
    })
    const data1 = newValId.join(',')
    const data2 = newValName.join(',')
    this.whiteSave.areaIds = data1 // 白名单保护区 id
    this.whiteSave.areaNames = data2 // 白名单保护区 name
  }

  /**
   * 取消
   */
  back() {
    this.shipSection = '0'
  }
  /**
   * 轨迹跟踪
   */
  toTrack() {
    // this.isTrack = !this.isTrack;
    this.shipSection = '2'
    // 日期选择器 默认选择前七天至今
    const oldTime = moment().subtract(7, "days").format(this.formatHistory)
    const newTime = moment().format(this.formatHistory)
    // 转日期Date格式
    this.startTimeHistory = moment(oldTime).toDate()
    this.endTimeHistory = moment(newTime).toDate()
  }

  // 保存前进行手动校验
  private beforeSave = (date?: boolean) => {
    const dataWhiteDate = this.whiteDate
    const dataWhiteVal = this.whiteVal
    let errArr: string[] = []
    if (!date && isEmpty(dataWhiteDate!)) {
      errArr.push('自定义日期不能为空！')
    }
    if (isEmpty(dataWhiteVal!)) {
      errArr.push('关联保护区不能为空！')
    }
    if (errArr.length) {
      this.slModalService.openPromptModal({
        type: 'error',
        content: errArr,
      })
      return false
    }
    return true
  }
  /**
   * 白名单保存
   * @param ship 
   */
  saveByWhite(ship: TblShip) {
    const datePickerBool: boolean = this.isWhiteDate
    // 第二种校验方式
    const newValidateService: boolean = this.beforeSave(datePickerBool)
    // const validateService: boolean = this.slValidateService.validateFormWithAlert(this.validateForm)  // 校验
    this.comAreaNameIds(this.whiteVal)  // 传递保护区名称 id
    this.customDateVal(this.isWhiteDate) // 传递自定义时间
    this.whiteSave.id = ship.id
    this.whiteSave.mmsi = ship.mmsi
    this.whiteSave.markType = ship.markType
    this.whiteSave.shipName = ship.shipName || undefined
    this.whiteSave.shipTypeCode = ship.shipTypeCode
    this.whiteSave.enableType = this.dateType // 生效类型
    this.whiteSave.markType = '2'  // 传递船舶白名单类型
    // 下面两种未出现
    this.whiteSave.remark = ship.remark || undefined

    if (newValidateService) {
      this.slModalService.openPromptModal({
        type: 'confirm',
        content: '确定保存吗?',
        okCb: () => {
          this.service.saveWhite(this.whiteSave).then(() => {
            this.slModalService.openPromptModal({
              type: 'success',
              content: '保存成功!',
              okCb: () => {
                this.shipSection = '0';
                this.service.getInfo(ship.mmsi!).then(res => {
                  this.ship = res
                  this.computMarkShipDate(this.ship)
                  if (this.ship.latitude && this.ship.longitude) {
                    this.computLat(this.ship.latitude, this.ship.longitude)
                  }
                })
                // this.refreshCom(ship)
                // 通知列表刷新操作
                this.service.notyfyShipListChanges(true)
              }
            })
          }).catch((err) => {
            this.slModalService.openPromptModal({
              type: 'error',
              content: err || '保存失败!'
            })
          })
        }
      })
    }
  }
  /**
   * 白名单删除
   * @param mmsi 
   */
  onWhiteDel(mmsi: string) {
    this.slModalService.openPromptModal({
      type: 'confirm',
      content: '确定删除吗?',
      okCb: () => {
        this.service.delWhite(mmsi).then(() => {
          this.slModalService.openPromptModal({
            type: 'success',
            content: '删除成功!',
            okCb: () => {
              this.shipSection = '0'
              this.whiteVal = []
              this.dateType = '0'
              this.service.getInfo(mmsi!).then(res => {
                this.ship = res
              })
              // this.refreshCom(mmsi)
              // 通知列表刷新操作
              this.service.notyfyShipListChanges(true)
            }
          })
        }).catch((err) => {
          this.slModalService.openPromptModal({
            type: 'error',
            content: err || '删除失败!'
          })
        })
      }
    })
  }

  // 重新刷新动态组件 
  // private refreshCom(ship: any) {
  // this.mapLayerService.refreshComponent({
  // name: 'ShipListComponent',
  // })
  // 刷新组件后 带参
  // this.service.notyfyShipListChanges({ bool: true })
  // this.mapLayerService.refreshComponent({ name: 'ShipWindpowerInfoComponent' })
  // }

  /**
   * 轨迹查询 
   * @param item 
   */
  searchTrack(item: TblShip) {
    this.historySearch.mmsi = item.mmsi
    this.historySearch.startTime = moment(this.startTimeHistory).format(this.formatHistory)
    this.historySearch.endTime = moment(this.endTimeHistory).format(this.formatHistory)
    const id = this.nzMessage.loading('轨迹查询中,请稍后...', { nzDuration: 0 }).messageId;
    this.service.getMarkHistory(this.historySearch).then(res => {
      const data = res
      this.nzMessage.remove(id)
      if (!data.trackList || data.trackList.length == 0) {
        this.nzMessage.info('该时间段内暂无船舶轨迹！')
      } else {
        this.shipTrajectoryService.addTrackLayer([data], this.historySearch.startTime!, this.historySearch.endTime!, 'ship', [{ name: 'ShipTrajectoryLayerComponent' }])
        this.historyComponent = true
      }
    }).catch(err => {
      this.nzMessage.info(err || '系统异常')
    })
  }

  // 记录轨迹查询后的组件
  private desHistoryCom() {
    if (!this.historyComponent) return;
    const historyDynamic1: DynamicComponent = {
      name: 'ShipTrajectoryLayerComponent',
    }
    const historyDynamic2: DynamicComponent = {
      name: 'TrajectoryTimeControllerComponent'
    }
    this.mapLayerService.removeComponent(historyDynamic1);
    this.mapLayerService.removeComponent(historyDynamic2)
  }

  // 跳转报警记录区  事故记录区
  toAlarmAccident(mmsi: string, type: 'alarm' | 'accident') {
    switch (type) {
      case 'alarm':
        // 清除路由复用缓存
        this.reuseReuseService.close('/admin/alarm')
        // 打开页面
        this.router.navigateByUrl('/admin/alarm', { state: { keyword: mmsi } });
        break;
      case 'accident':
        this.mapLayerService.removeAllByName('ShipListComponent');
        const c: DynamicComponent = {
          name: 'AccidentMainComponent',
          type: 'popup',
          data: {
            position: {
              left: 10,
              top: 48,
            },
            size: {
              width: 345
            },
            params: {
              mmsi
            }
          }
        }
        // setTimeout(() => {
        //   this.service.notyfyAccident(mmsi)
        // }, 1000);
        this.service.notyfyTopLeftChanges(c)
        break;
    }
  }


  // 重新计算经纬度单位
  private computLat(lat: any, lng: any) {
    const latlng: L.LatLng = L.latLng(lat, lng)
    const newLatlng = transformPosUnit(lat, lng)
    this.ship.latitude = newLatlng.latStr
    this.ship.longitude = newLatlng.lngStr
    this.buildSelectedMarkerIcon(latlng)
  }

  /**
   * 构建船舶选中后的产生的图标 L.icon
   * @param latlng 
   */
  private buildSelectedMarkerIcon(latlng: L.LatLng) {
    const iconUrl = '/assets/img/map/ship-selected.png'
    const icon = L.icon({
      iconUrl,
      iconSize: [32, 32], // 文件大小
      iconAnchor: [16, 16],
    });
    // 注意 icon 传递的 { } 类型 否则L.marker optiongs出不来图标
    const sltMarker = this.buildSelectedMarker(latlng, { icon }).addTo(this.map)
    const zoom = this.map.getZoom()
    this.selectedMarker = sltMarker
    this.map.setView(latlng, zoom)
  }
  /**
   * 构建船舶加载后的 maker = ship + icon
   * @param latlng 
   * @param opt 
   * @returns 返回指定的标记 L.Marker
   */
  private buildSelectedMarker(latlng: L.LatLng, opt: object): L.Marker {
    return L.marker(latlng, opt)
  }
  // 移除选中后的图标 阻止多个选中的图标生成
  private removeSelectedMark() {
    if (this.selectedMarker) this.map.removeLayer(this.selectedMarker)
  }
}
