.ship-info {
  width: 534px;
  background-color: #fff;
  border-radius: 4px;
  padding: 8px;

  .info-container {
    height: 100%;
    width: 100%;

    .body_title {
      font-size: 16px;
      color: #333;
      font-weight: 500;
      width: 100%;
      height: 30px;
      padding-left: 15px;
      line-height: 30px;
    }

    // 中间内容区
    .body_content {
      width: 100%;
      background-color: #fff;
      margin-bottom: 5px;

      table {
        tr {
          td {
            padding: 2px 0;
            font-size: 14px;
            font-weight: 400;
            vertical-align: top;

            &:nth-child(odd) {
              color: #666;
              text-align: right;
              width: 20%;
            }

            &:nth-child(even) {
              color: #333;
              padding: 2px 0px 2px 10px;
            }
          }
        }
      }
    }

    // 报警记录区
    .body_bottom {
      background-color: #fff;
      height: 65px;
      border-top: 1px dashed #E3E7EF;
      padding: 5px 0;

      table {
        width: 100%;

        tr {
          height: 15px;

          td {
            padding: 2px 0;
            font-weight: 400;
            font-size: 14px;

            &:nth-child(odd) {
              color: #666;
              text-align: right;
              width: 20%;
              height: 26px;
            }

            &:nth-child(even) {
              // padding: 0px 0px 0px 10px;
              height: 26px;
            }

            &.white-effect {
              color: #2D74EF;
              padding-left: 10px;
            }

            &.textNum {
              width: 30%;

              a {
                display: flex;
                align-items: center;

                .textNum_data {
                  margin: 0 8px;
                  color: #ED1313;
                }

                .ass {
                  color: #333;
                }
              }
            }
          }
        }
      }
    }

    // 白名单列表区
    .body_bottom-whitelist {
      border-top: 1px solid #E3E7EF;
      padding: 8px 0px 8px 15px;
      width: 100%;

      table {
        width: 100%;
        table-layout: fixed;

        tr {
          td {
            vertical-align: top;

            &.white-name {
              text-align: right;
              width: 100px;

              label {
                color: #666;
                position: relative;

                &::after {
                  content: "*";
                  width: 10px;
                  height: 10px;
                  color: red;
                  position: absolute;
                  left: -13px;
                  top: 1px;
                }
              }
            }

            &.tdflex {
              .tdflex_radio {
                display: flex;
                flex-direction: column;
                padding-top: 13px;
              }

              .tdflex_radio-data {
                display: flex;
                flex-direction: row;
                justify-content: space-around;
                margin-top: 6px;
                align-items: center;
              }
            }

            &:nth-child(3) {
              padding-left: 10px;
            }
          }
        }
      }
    }

    // 轨迹追踪区
    .body_bottom-track {
      height: 80px;
      width: 100%;
      background-color: #fff;
      border-top: 1px solid #E3E7EF;

      .track_title {
        font-size: 16px;
        color: #333;
        font-weight: 500;
        width: 100%;
        height: 30px;
        padding-left: 15px;
        line-height: 30px;
      }

      .track_datepick {
        text-align: center;
      }
    }

    // 下方按钮区
    .body_btn {
      width: 100%;
      text-align: center;
      margin: 8px 0;

      .btn {
        display: flex;
        justify-content: center;
        align-items: center;

        &>button {
          margin: 0 12px;
        }
      }
    }
  }
}
