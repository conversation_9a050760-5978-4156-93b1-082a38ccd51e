import { DynamicComponent } from 'src/app/shared/models';
import { ShipListComponent } from './ship-list/ship-list.component';
import { ShipWindpowerInfoComponent } from './ship-windpower-info/ship-windpower-info.component';
import { WindpowerShipLayerComponent } from './windpower-ship-layer';
// 基站
export const DYNAMIC_WINDPOWER_SHIP_COMPONENTS: Array<DynamicComponent> = [
  {
    name: 'ShipListComponent',
    component: ShipListComponent, // 船舶区列表
    hasChild: true,
  },
  {
    name: 'ShipWindpowerInfoComponent',
    component: ShipWindpowerInfoComponent, // 船舶区详细
    parentName: 'ShipListComponent'
  },
  {
    name: 'WindpowerShipLayerComponent',
    component: WindpowerShipLayerComponent, // 船舶图层
  }
];
