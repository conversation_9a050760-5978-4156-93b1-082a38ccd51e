interface dataItem{
    code?:string;
    name?:string
}
export const WINDPOWER_SHIP_TYPECODE:Array<dataItem>=[
    {code:'1',name:'货船'},
    {code:'2',name:'油轮'},
    {code:'3',name:'客船'},
    {code:'4',name:'高速船'},
    {code:'5',name:'渔船'},
    {code:'6',name:'游艇'},
    {code:'7',name:'拖船/特种船'},
    {code:'8',name:'其他'}
]
export const WINDPOWER_SHIP_STATUES:Array<dataItem>=[
    {code:'0',name:'发动机使用中'},
    {code:'1',name:'锚泊'},
    {code:'2',name:'未操纵'},
    {code:'3',name:'有限适航性'},
    {code:'4',name:'受船舶吃水限制'},
    {code:'5',name:'系泊'},
    {code:'6',name:'搁浅'},
    {code:'7',name:'从事捕捞'},
    {code:'8',name:'航行中'},
    {code:'9',name:'留做将来修正导航状态，用于载运危险品（DG）、有害物质（HS）或海洋污染物（MP）的船舶，或载运IMO的C类危险品或污染物、高速船（HSC）'},
    {code:'10',name:'留做将来修正导航状态，用于载运DG、HS或MP，或载运IMO的A类危险品或污染物的船舶，WIG'},
    {code:'11',name:'机动船尾推作业（区域使用）'},
    {code:'12',name:'机动船顶推或侧推作业（区域使用）'},
    {code:'13',name:'留做将来用'},
    {code:'14',name:'AIS-SART（现行的）、MOB-AIS、EPIRB-AIS'},
    {code:'15',name:'未规定=默认值（也用于测试中的AIS-SART、MOB-AIS和EPIRB-AIS）'},
]