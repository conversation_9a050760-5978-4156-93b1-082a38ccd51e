import { Injectable } from '@angular/core';
import { BaseHttpService } from 'src/app/shared/services/base-http.service';
import { BaseCURDService } from 'src/app/shared/services/base-curd.service';
import { TblShip, TblShipSearch } from 'src/app/workspace/workspace-shared/models/tbl_ship';
import { DynamicComponent, Page } from 'src/app/shared/models';
import { TblMarkHistory, TblMarkship } from 'src/app/workspace/workspace-shared/models/tbl_markship';
import { ShipOrigin } from './windpower-ship-model';
import { BehaviorSubject, Subject } from 'rxjs';
import { filter } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class WindpowerShipService extends BaseCURDService<TblShip>{
  //主题：ship layer  船舶图层 筛选类型   behaviorSubject实时拿到最新通知
  private shipChanges$: BehaviorSubject<any> = new BehaviorSubject(null)

  //主题：henalde topLeft 样式变化
  private handleTopLeft$: BehaviorSubject<any> = new BehaviorSubject(null)

  // 主题：通知列表 刷新操作（重发请求）
  private shipInfoToList$: Subject<boolean> = new Subject()

  // 船舶图层变化
  get changes() {
    return this.shipChanges$.asObservable().pipe(filter(ele => ele !== null))
  }

  // top-left标题栏 样式改变
  get handleTopLeftChange() {
    return this.handleTopLeft$.asObservable().pipe(filter(ele => ele !== null))
  }

  // 列表变化 info to list
  get shipListChange() {
    return this.shipInfoToList$.asObservable()
  }

  // 通知 船舶图层改变
  notyfyShipChanges(nodes: string[]) {
    this.shipChanges$.next(nodes)
  }

  // 通知 标题样式
  notyfyTopLeftChanges(c: DynamicComponent) {
    this.handleTopLeft$.next(c)
  }

  // 通知列表刷新操作
  notyfyShipListChanges(bool: boolean) {
    this.shipInfoToList$.next(bool)
  }


  constructor(
    protected http: BaseHttpService
  ) {
    super(http, '/api/Aisposition')
  }

  // 船舶区获取数据 带分页
  getList2 = (data: TblShipSearch): Promise<Page<TblShip>> => {
    return this.http.post(`/api/Aisposition/getList`, data);
  };

  // 白名单保存
  saveWhite = (data: TblMarkship): Promise<string> => {
    return this.http.post(`/api/Markship/save`, data);
  }
  // 白名单删除
  delWhite = (id: string): Promise<string> => {
    return this.http.get(`/api/Markship/delete/${id}`);
  }

  // 轨迹查询
  getMarkHistory = (data: TblMarkHistory): Promise<any> => {
    return this.http.post('/api/Aisposition/getHistoryList', data)
  }
  // 获取保护区列表
  getProareaList = (): Promise<any> => {
    return this.http.get('/api/Proarea/getProareaList')
  }
  /**
 *
 * 获取区域内所有船舶
 * @param {{ lat: number, lat1: number, lng: number, lng1: number }} bounds
 * @return {*} 
 * @memberof ShipLayerService
 */
  getShips(bounds: L.LatLngBounds) {
    const northEast = bounds.getNorthEast() // 东北
    const southWest = bounds.getSouthWest() // 西南
    return this.http.post<Array<ShipOrigin>, { lat: number, lat1: number, lng: number, lng1: number }>('/api/Aisposition/getShips', {
      lat: northEast.lat,
      lng: northEast.lng,
      lat1: southWest.lat,
      lng1: southWest.lng
    })
  }

}
