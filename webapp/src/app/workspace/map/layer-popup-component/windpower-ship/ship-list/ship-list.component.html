<div class="container">
  <!-- 头部选择输入框 -->
  <div class="header">
    <nz-input-group nzCompact class="sl-select-primary-bg">
      <nz-select style="width: 86px" [(ngModel)]="selected" (ngModelChange)="selectedChange($event)">
        <nz-option nzLabel="全部" nzValue=""></nz-option>
        <nz-option nzLabel="白名单" nzValue="2"></nz-option>
        <nz-option nzLabel="报警" nzValue="1"></nz-option>
        <nz-option nzLabel="事故" nzValue="3"></nz-option>
      </nz-select>
      <nz-input-group [nzPrefix]="suffixIconSearch" [nzSuffix]="inputClearTpl" style="width: 196px" class="ipt-group">
        <input type="text" nz-input placeholder="请输入船舶名称/MMSI" [(ngModel)]="searchText" style="width: 83%;"/>
        <!-- <a href="javascript:void(0)" *ngIf="isEmpty()" (click)="clear()" class="clear">
          <i class="sl-close-circle-white"></i>
        </a> -->
      </nz-input-group>
      <!-- 后缀图标 -->
      <ng-template #inputClearTpl>
        <a href="javascript:void(0)" *ngIf="searchText" (click)="searchText='';shipSearchName()" style="display: flex;">
          <i class="sl-close-circle-white"></i>
        </a>
      </ng-template>
      <!-- 前缀图标 -->
      <ng-template #suffixIconSearch>
        <a href="javascript:void(0)" style="display: flex" (click)="shipSearchName()">
          <i class="sl-input-search-16"></i>
        </a>
      </ng-template>
    </nz-input-group>
    <!-- 搜索框 右侧图标 -->
    <a href="javascript:void(0)" class="handle" (click)="toScreen()">
      <i
          *ngIf="!isScreen"
          [class]="isScreenSearch ? 'sl-filter-active-16' : 'sl-filter-16'"
      ></i>
      <i *ngIf="isScreen" [class]="'sl-return-back-16'"></i>

      <span [ngStyle]="{'color':isScreen?'#2D74EF':'#333'}">{{ isScreen ? "返回" : "筛选" }}</span>
    </a>
  </div>
  <!-- 筛选条件框 -->
  <ng-container *ngIf="isScreen">
    <div class="table_screen">
      <table>
        <tr>
          <td>船舶类型:</td>
          <td>
            <nz-select
            [nzMaxTagCount]="2"
            nzMode="multiple"
            nzPlaceHolder="请选择船舶类型"
            [(ngModel)]="search.easyTypeCodeList"
            style="width: 100%;height:35px;"
            [nzAllowClear]="true"
            [nzMaxTagPlaceholder]="tagPlaceHolder"
            [nzShowArrow]="true"
            >
            <nz-option *ngFor="let item of listShipType" [nzLabel]="item.name" [nzValue]="item.code" 
            ></nz-option>
            <ng-template #tagPlaceHolder let-selectedList>+ {{ selectedList.length }}</ng-template>
          </nz-select>
          </td>
        </tr>
        <tr>
          <td>船舶长度:</td>
          <td>
            <input
              nz-input
              placeholder="请输入"
              type="number"
              style="width: 98px"
              width="98px"
              [(ngModel)]="search.shipLengthMin"
              name="ship.shipLengthMin"
            />
            &nbsp;&nbsp;~&nbsp;&nbsp;
            <input
              nz-input
              placeholder="请输入"
              style="width: 98px"
              [(ngModel)]="search.shipLengthMax"
              type="number"
              width="98px"
              name="ship.shipLengthMax"
            />
            &nbsp;&nbsp; 米
          </td>
        </tr>
        <tr>
          <td>船舶宽度:</td>
          <td>
            <input
              nz-input
              placeholder="请输入"
              style="width: 98px"
              width="98px"
              type="number"
              [(ngModel)]="search.shipBreadthMin"
              name="ship.shipWidthdMin"
            />
            &nbsp;&nbsp;~&nbsp;&nbsp;
            <input
              nz-input
              placeholder="请输入"
              type="number"
              style="width: 98px"
              [(ngModel)]="search.shipBreadthMax"
              name="ship.shipWidthMin"
            />
            &nbsp;&nbsp; 米
          </td>
        </tr>
        <tr>
          <td>船舶速度:</td>
          <td>
            <input
              nz-input
              placeholder="请输入"
              type="number"
              style="width: 98px"
              [(ngModel)]="search.shipSpeedMin"
              name="ship.shipWidthdMin"
            />
            &nbsp;&nbsp;~&nbsp;&nbsp;
            <input
              nz-input
              placeholder="请输入"
              style="width: 98px"
              type="number"
              [(ngModel)]="search.shipSpeedMax"
              width="98px"
              name="ship.shipWidthdMin"
            />
            &nbsp;&nbsp; 节
          </td>
        </tr>
        <tr>
          <td>船舶状态:</td>
          <td class="stateSele">
            <nz-select
            [nzMaxTagCount]="2"
            nzMode="multiple"
            nzPlaceHolder="请选择船舶状态"
            [(ngModel)]="search.navigationStatusCodeList"
            style="width: 100%;height:35px;"
            [nzAllowClear]="true"
            [nzMaxTagPlaceholder]="tagPlaceHolder2"
            [nzShowArrow]="true"
            >
            <nz-option *ngFor="let item of listShipStatus" [nzLabel]="item.name" [nzValue]="item.code" 
            ></nz-option>
            <ng-template #tagPlaceHolder2 let-selectedList>+ {{ selectedList.length }}</ng-template>
          </nz-select>
          </td>
        </tr>
      </table>
      <div class="table_screen_button">
        <button sl-button slType="default" (click)="toReset()">重置</button>
        <button sl-button slType="primary" (click)="searchScreen()">
          查询
        </button>
      </div>
    </div>
  </ng-container>
  <!-- 内容列表区 -->
  <!-- <div class="table_list" [style.height]="isScreen ? lenList ? '0px' : '215px' : '420px'"> -->
    <div class="table_list" [style.height]="isScreen ?  '215px' : '420px'">
    <div class="table_list_inner">
    <table >
      <tr class="table_header">
        <td>序号</td>
        <td>船舶名称</td>
        <td>MMSI</td>
      </tr>
      <tr class="table_content" *ngFor="let item of dataList; index as code">
        <td>
          {{ (page.currentPage! - 1 ) * page.pageRecord! + code + 1 }}
        </td>
        <td class="table_content_text" >
          
          <a href="javascript:void(0)"  (click)="toInfo(item)" class="text-alist">
            <div class="text-list-first" [title]="item.shipName?item.shipName:item.mmsi">
              <i class="sl-{{item.shipIcon}}-ship-16"></i>
              <span>
                {{ item.shipName?item.shipName:item.mmsi }}
              </span>
            </div>
            <!-- 若同时存在 白名单在前 -->
            <!-- 白名单图标 -->
            <i *ngIf="item.shipMarkIcon" class="sl-{{item.shipMarkIcon}}-16" title="白名单"></i> 
            <!-- 报警图标 -->
            <i *ngIf="item.shipMarkIconAlarm" class="sl-{{item.shipMarkIconAlarm}}-16" title="报警"></i>
          </a>
        </td>
        <td>{{ item.mmsi }}</td>
    </tr>
      <tr *ngIf="!dataList||dataList.length==0">
        <td colspan="3" rowspan="3">
          <nz-spin [nzSpinning]="spinning"  [nzTip]="'数据加载中...'" style="margin-top: 25px;">
          </nz-spin>
            <nz-empty></nz-empty>
        </td>
      </tr>
    </table>
  </div>
  </div>
<div>
  <app-map-list-pagination (pageChange)="pageChanged($event)" [totalNum]="page.recordCount!" [totalPageNum]="page.pageCount!" [currentPage]="page.currentPage!" [pageRecord]="page.pageRecord!"></app-map-list-pagination>
</div>

</div>