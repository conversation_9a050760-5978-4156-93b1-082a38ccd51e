import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { popup } from 'leaflet';
import { Subject, Subscription } from 'rxjs';
import { debounceTime } from 'rxjs/operators';
import { BaseSearch, DynamicComponent, Page } from 'src/app/shared/models';
import { TblAisship } from 'src/app/workspace/workspace-shared/models/tbl_aisship';
import { TblShip, TblShipSearch } from 'src/app/workspace/workspace-shared/models/tbl_ship';
import { MapLayerComponentService } from '../../../service/map-layer-component.service';
import { MapStateService } from '../../../service/map-state.service';
import { WindpowerProtectService } from '../../windpower-protect/windpower-protect.service';
import { WINDPOWER_SHIP_STATUES, WINDPOWER_SHIP_TYPECODE } from '../windpower-ship-data';
import { getWindopowerShipIconClass } from '../windpower-ship-type';
import { WindpowerShipService } from '../windpower-ship.service';
import * as L from 'leaflet';
import { Principal } from 'src/app/shared/services/principal.service';

@Component({
  selector: 'app-ship-list',
  templateUrl: './ship-list.component.html',
  styleUrls: ['./ship-list.component.less'],
})
export class ShipListComponent implements OnInit, OnDestroy {
  map: L.Map
  // 搜索  船舶类型区分
  selected: string = ''; // 空普通全部 1报警2白名单3事故
  // 搜索框输入内容
  searchText: string = '';
  // 记录总数
  totalRecords: number | string = 12;
  // 筛选图标
  isScreen: boolean = false;   // true 进入筛选页面 false 初始化页面
  isScreenSearch: boolean = false // false 默认筛选图标 true 筛选条件已经选中并查询图标
  screenStatus: number = 0 // 0 筛选 1 返回 2 已经筛选  是否重置后的查询  0    1 重置后的查询  2 
  // 请求参数
  search: TblShipSearch = new TblShipSearch()
  // 页面渲染的数据
  page: Page<TblShip> = new Page<TblShip>()
  dataList!: Array<TblShip>
  // 船舶类型数据
  listShipType!: Array<any>
  listShipStatus!: Array<any>
  // 搜索主题
  // private searchTextSubject: Subject<string> = new Subject()
  // 搜索主体订阅
  // private searchSubscription: Subscription;
  spinning: boolean = false; // 加载提示 false 加载成功
  // 详情区改变白名单 传递通知
  screenSub = new Subscription()
  constructor(
    private mapLayerComponentService: MapLayerComponentService,
    private windpowerShipService: WindpowerShipService,
    private mapState: MapStateService,
    private principal: Principal
  ) {
    this.listShipType = WINDPOWER_SHIP_TYPECODE
    this.listShipStatus = WINDPOWER_SHIP_STATUES
    this.map = this.mapState.getMapInstance()
  }
  get lenList(): boolean {
    return this.dataList.length == 0
  }
  ngOnInit(): void {
    const search = new TblShipSearch()
    this.getList(search)
    // 接收详情传来的通知
    this.screenSub = this.windpowerShipService.shipListChange.subscribe(bool => {
      if (bool) {
        if (!this.isScreen) {
          this.shipSearchName()
        } else {
          this.searchScreen()  // 筛选里的查询
        }
      }
    })
  }
  ngOnDestroy(): void {
    this.screenSub.unsubscribe()
  }
  // 获取页面数据
  private getList(search: TblShipSearch, filter?: boolean) {
    this.spinning = true
    this.windpowerShipService.getList2(search).then(res => {
      this.page = res
      const results = this.page.result!
      this.dataList = results.map(item => {
        return {
          shipName: item.shipName,
          mmsi: item.mmsi,
          id: item.id,
          shipMarkIcon: getWindopowerShipIconClass(item.markType!, false, '2'), // 白名单图标
          shipMarkIconAlarm: getWindopowerShipIconClass(item.markType!, false, '1'), // 报警图标
          shipIcon: getWindopowerShipIconClass(item.shipTypeCode!, true) // 船舶类型图标
        }
      })

      if (filter) {
        if (this.screenStatus == 1) {
          this.isScreenSearch = false
        } else {
          this.screenStatus = 0
          this.isScreenSearch = true; // 筛选图标变更
        }
        this.screenStatus = 0
      }
      this.spinning = false
    }).catch(err => {
      this.isScreenSearch = false; // 错误请求 筛选图标不变更
      this.spinning = false
    })
  }
  // 跳转详情    menuCode '1.2.4'
  toInfo(data: TblShip) {
    const menuBool: boolean = this.principal.hasAuthority('1.2.4')
    if (!menuBool) return;
    // 弹出详情动态组件
    this.windpowerShipService.getInfo(data.id!).then(res => {
      const item = res
      if (!(item.latitude && item.longitude)) {
        console.error('该船舶坐标不存在');
        return;
      }
      if (item.latitude && item.longitude) {
        item.latlng = L.latLng(item.latitude, item.longitude)!
      }
      let infoComponent: DynamicComponent = {
        name: 'ShipWindpowerInfoComponent',
        type: 'popup',
        title: `${data.shipName || data.mmsi}（详细）`,
        titleType: 'primary',
        data: {
          position: {
            latlng: item.latlng,
            offset: [0, 24]
          },
        }
      }
      infoComponent.data!.params = item
      this.map.setView(item.latlng!, 15)
      this.mapLayerComponentService.addComponent(infoComponent)
    })
  }
  // 头部搜索图标
  shipSearchName() {
    this.search.keyword = this.searchText
    this.search.currentPage = 1
    this.getList(this.search)
  }
  /**
   * 头部选择条件框 
   */
  selectedChange(event: string) {
    this.search.markType = event
    const currentPage = this.search.currentPage!
    if (currentPage > 1) {
      this.search.currentPage = 1  // 注意查询前重置当前页
    }
    this.getList(this.search)
  }
  /**
   * 筛选图标
   */
  toScreen() {
    this.isScreen = !this.isScreen;

    this.isScreen ? this.search.pageRecord = 5 : this.search.pageRecord = 10

    this.getList(this.search)
  }
  // 筛选重置
  toReset() {
    this.isScreenSearch = false
    this.screenStatus = 1
    this.search = new TblShipSearch()
    this.search.pageRecord = 5
    this.getList(this.search)
  }
  // 筛选查询  底部查询按钮
  searchScreen() {
    this.search.pageRecord = 5; // 筛选最多显示5条
    this.search.currentPage = 1
    const search = this.search
    this.getList(search, true)
  }
  /**
   * 改变分页
   */
  pageChanged(event: { currentPage: number }) {
    if (this.isScreen) {
      this.search.pageRecord = 5
      this.search.currentPage = event.currentPage
      this.getList(this.search)
    } else {
      this.search.pageRecord = 10
      this.search.currentPage = event.currentPage
      this.getList(this.search)
    }
  }
  /**
   * 清除搜索条件 x 
   * @returns boolean
   */
  isEmpty(): boolean {
    return this.searchText.trim().length > 0
  }
  // clear() {
  //   this.searchText = ''
  //   this.search.keyword = ''
  //   this.search.currentPage = 1 // 回到第一页
  //   this.getList(this.search)
  // }
  // 搜索框当前输入的值
  // currentSearch() {
  //   // this.searchText
  //   this.searchTextSubject.next(this.searchText)
  // }
}
