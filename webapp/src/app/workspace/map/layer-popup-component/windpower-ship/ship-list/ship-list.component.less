@import "/src/styles/mixins";

.container {
  background-color: #fff;
  color: #333;
  height: 515px;
  padding: 10px 10px 2px 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 2px;
    border-radius: 4px;

    &>a.handle {
      width: 38px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      &>span {
        font-size: 12px;
        color: #333;
        font-weight: 400;
        text-align: center;
      }
    }

    // .sl-select-primary-bg {
    //   .ipt-group {
    //     &>a.clear {
    //       position: absolute;
    //       right: 5px;
    //     }
    //   }
    // }
  }

  .table_screen {
    width: 100%;
    height: 213px;
    background-color: #fff;

    table {
      tr {
        td {
          &>::ng-deep .ant-select-multiple .ant-select-selector {
            overflow: hidden;
            flex-wrap: nowrap;
            max-width: 262px;
            // text-overflow: ellipsis;
          }

          padding: 0 0 3px 0;

          &:nth-child(odd) {
            color: #666;
            width: 20%;
            font-weight: 400;
          }

          &:nth-child(even) {
            width: 80%;
          }

          &.stateSele {
            ::ng-deep .ant-select-multiple .ant-select-selection-item {
              max-width: 96px;
            }
          }
        }
      }
    }

    .table_screen_button {
      display: flex;
      justify-content: center;
      align-items: center;

      button {
        margin-left: 10px;
      }
    }
  }

  .table_list {
    width: 100%;
    table-layout: fixed;
    margin-bottom: 7px; // .scrollbars(6px, #CBD0D7, #fff); // 滚动条的样式
    overflow: hidden;

    .table_list_inner {
      height: 100%;
      // overflow: auto;

      table {
        tr {
          td {
            &:nth-child(1) {
              width: 42px;
            }

            &:nth-child(3) {
              width: 90px;
            }

            height: 32px;
          }

          &.table_header {
            td {
              background-color: #F3F7FF;
              text-align: center;
              font-weight: 400;
              color: #333;
              font-size: 14px;
            }
          }

          &.table_content {
            &:nth-child(odd) {
              background-color: #F3F7FF;
            }

            td {
              overflow: hidden;
              white-space: nowrap; //不允许换行
              height: 38px;
              padding: 2px 0;

              &:nth-child(1),
              &:nth-child(3) {
                text-align: center;
              }

              &.table_content_text {

                // a 标签
                .text-alist {
                  display: flex;
                  align-items: center;
                  color: #333;
                  justify-content: space-between;

                  .text-list-first {
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    span {
                      width: 135px;
                      text-overflow: ellipsis;
                      overflow: hidden;
                    }
                  }
                }
              }
            }
          }
        }
      }

    }
  }

  .bottom_page {
    height: 32px;
    width: 100%;
    display: flex;
    line-height: 32px;
    margin-top: 6px;

    .page_left {
      width: 144px;

      span {
        text-align: center;
        height: 14px;
        font-size: 14px;
        line-height: 14px;
        font-weight: 400;
        color: #666;
      }
    }

    .page_right {
      width: 66px;
      margin-left: 130px;
      display: flex;
      justify-content: center;
      align-items: center;

      .total {
        display: block;
        width: 16px;

        a {
          width: 100%;
        }
      }

      &.pages {
        display: block;
        width: 14px;
        text-align: center;
      }
    }
  }
}
