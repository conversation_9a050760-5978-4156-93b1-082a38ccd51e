<div class="test-project-edit" [class.in-view]="ifView">
  <form #validateForm="ngForm">
    <div style="width: 300px">
      <span>项目名称：</span>
      <input
        type="text"
        [slValidate]="{ required: true, label: '项目名称' }"
        nz-input
        [disabled]="ifView"
        [(ngModel)]="project.projectName"
        name="projectName"
      />
    </div>
    <div style="margin-top: 4px">
      <span>备注：</span>
      <input
        type="text"
        nz-input
        [disabled]="ifView"
        [(ngModel)]="project.remark"
        name="remark"
      />
    </div>
    <div style="flex: 1; align-items: unset">
      <ul class="station-list">
        <li class="header">
          <div
            *ngFor="let column of listColumn"
            [style.min-width.px]="column.width"
            [style.max-width.px]="column.maxWidth"
          >
            <span *ngIf="column.columnType !== 'icon'">{{ column.title }}</span>
            <i
              *ngIf="column.columnType == 'icon'"
              [hidden]="column.ifViewHidden && ifView"
              class="{{ column.columnIconClass }}"
              [title]="column.title"
              (click)="column.onClickHead && column.onClickHead()"
            ></i>
          </div>
        </li>
        <li
          *ngFor="
            let item of stationList;
            let index = index;
            trackBy: trackByItems
          "
        >
          <ng-container *ngFor="let column of listColumn">
            <ng-container [ngSwitch]="column.type">
              <div
                *ngSwitchCase="'select'"
                [style.min-width.px]="column.width"
                [style.max-width.px]="column.maxWidth"
              >
                <nz-select
                  style="width: 100%"
                  [(ngModel)]="item[column.property]"
                  [nzShowSearch]="true"
                  [name]="column.property + item.uuid"
                  [slValidate]="{
                    required: column.required,
                    label: column.title
                  }"
                  [disabled]="ifView"
                  (ngModelChange)="onSelectChange($event, item)"
                >
                  <nz-option
                    *ngFor="let optItem of stationCodes"
                    [nzHide]="filterStationCodes(optItem.value)"
                    [nzValue]="optItem.value"
                    [nzLabel]="optItem.label"
                  >
                  </nz-option>
                </nz-select>
              </div>
              <div
                *ngSwitchCase="'latlng'"
                [style.max-width.px]="column.maxWidth"
                [style.min-width.px]="column.width"
              >
                <!-- 经纬度 -->
                <input
                  type="number"
                  nz-input
                  [disabled]="ifView"
                  style="width: 35.29px; padding-left: 4px; padding-right: 0px"
                  [slValidate]="{
                    required: true,
                    label: '纬度（度）',
                    type: 'la_degree'
                  }"
                  [name]="'d' + item.uuid"
                  [(ngModel)]="item.coordinate.lat.d"
                />
                <sup>°</sup>
                <input
                  type="number"
                  nz-input
                  [disabled]="ifView"
                  style="width: 35.29px; padding-left: 4px; padding-right: 0px"
                  [slValidate]="{
                    required: true,
                    label: '纬度（分）',
                    type: 'la_minute'
                  }"
                  [name]="'m' + item.uuid"
                  [(ngModel)]="item.coordinate.lat.m"
                />
                <sup>′</sup>
                <input
                  type="number"
                  nz-input
                  [disabled]="ifView"
                  style="width: 60px; padding-left: 4px; padding-right: 0px"
                  [slValidate]="{
                    required: true,
                    label: '纬度（秒）',
                    type: 'la_second'
                  }"
                  [name]="'s' + item.uuid"
                  [(ngModel)]="item.coordinate.lat.s"
                />
                <sup>″</sup>
                <span class="unit" style="margin-left: 4px; margin-right: 6px"
                  >N</span
                >
                <input
                  type="number"
                  nz-input
                  [disabled]="ifView"
                  style="width: 35.29px; padding-left: 4px; padding-right: 0px"
                  [slValidate]="{
                    required: true,
                    label: '经度（度）',
                    type: 'lo_degree'
                  }"
                  [name]="'d2' + item.uuid"
                  [(ngModel)]="item.coordinate.lng.d"
                />
                <sup>°</sup>
                <input
                  type="number"
                  nz-input
                  [disabled]="ifView"
                  style="width: 35.29px; padding-left: 4px; padding-right: 0px"
                  [slValidate]="{
                    required: true,
                    label: '经度（分）',
                    type: 'lo_minute'
                  }"
                  [name]="'m2' + item.uuid"
                  [(ngModel)]="item.coordinate.lng.m"
                />
                <sup>′</sup>
                <input
                  type="number"
                  nz-input
                  [disabled]="ifView"
                  style="width: 60px; padding-left: 4px; padding-right: 0px"
                  [slValidate]="{
                    required: true,
                    label: '经度（秒）',
                    type: 'lo_second'
                  }"
                  [name]="'s2' + item.uuid"
                  [(ngModel)]="item.coordinate.lng.s"
                />
                <sup>″</sup>
                <span class="unit" style="margin-left: 4px">E</span>
              </div>
              <div
                *ngSwitchCase="'icon'"
                [style.min-width.px]="column.width"
                [style.max-width.px]="column.maxWidth"
              >
                <i
                  [hidden]="column.ifViewHidden && ifView"
                  class="{{ column.iconClass }}"
                  [title]="column.property"
                  (click)="column.onClick && column.onClick(column, item)"
                ></i>
              </div>
              <div
                *ngSwitchDefault
                [style.max-width.px]="column.maxWidth"
                [style.min-width.px]="column.width"
              >
                <input
                  type="text"
                  nz-input
                  [disabled]="ifView"
                  [(ngModel)]="item[column.property]"
                  [name]="column.property + item.uuid"
                  [slValidate]="{
                    required: column.required,
                    label: column.title
                  }"
                />
              </div>
            </ng-container>
          </ng-container>
        </li>
      </ul>
    </div>
    <div *ngIf="!ifView">
      <button sl-button slType="primary" (click)="onSave(validateForm)">
        保存
      </button>
      <button *ngIf="ifEdit" sl-button slType="danger" (click)="onDelete()">
        删除
      </button>
    </div>
  </form>
</div>
