@import "/src/styles/mixins";
.test-project-edit {
    overflow: hidden;
    height: 500px;
    width: 100%;
    min-width: 895px;
    padding: 10px;
    .scrollbars(5px, #C8C9CC, rgba(0, 0, 0, 0));
    form {
        display: flex;
        flex-direction: column;
        height: 100%;
        & > div {
            display: flex;
            min-height: 32px;
            width: 100%;
            align-items: center;
            & > span {
                min-width: 100px;
            }
        }
    }
}
.in-view {
    .ant-input[disabled] {
        color: rgba(0, 0, 0, 0.85) !important;
    }
}
.station-list {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow: auto;
    .header {
        position: sticky;
        top: 0;
        z-index: 1;
        div {
            height: 32px;
            margin: 0;
            border: 1px solid #d7dbe8;
            border-right: unset;
            background-color: #eef4fe;
        }
        &>div:nth-last-child(1) {
            border-right: 1px solid #d7dbe8;
        }
    }
    i {
        cursor: pointer;
    }
    li {
        display: flex;
        height: 32px;
        position: relative;
        margin: 4px 0;
        &:not(.header):after {
            content: '';
            width: 100%;
            height: 1px;
            position: absolute;
            bottom: -4px;
            background-color: #d7dbe852;
        }
        & > div {
            flex: 1;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            padding: 0 4px;
            nz-select {
                width: 100%;
            }
            nz-input {
                width: 100%;
            }
        }
    }
}