import { SlModalService } from './../../../../../shared/modules/sl-modal/sl-modal.service';
import {
  Coordinate,
  CoordinatePackage,
} from './../../../../../shared/models/coordinate-package.model';
import { Component, OnInit } from '@angular/core';
import { DynamicComponentData } from 'src/app/shared/models';
import { TestProjectInfo } from '../station-test.model';
import {
  AisPosition,
  AisVirtualaton,
  Station,
} from '../../station/station.model';
import { NgForm } from '@angular/forms';
import { SlValidateService } from 'src/app/shared/modules/sl-validate/sl-validate.service';
import { StationService } from '../../station/station.service';
import { DMSToLatlng, latlngToDMS } from '../../../utils';
import * as _ from 'lodash';
import { uuid } from 'src/app/shared/utils';
import { StationTestProjectService } from '../station-test-project.service';
import { MapLayerComponentService } from '../../../service/map-layer-component.service';

@Component({
  selector: 'app-test-project-edit',
  templateUrl: './test-project-edit.component.html',
  styleUrls: ['./test-project-edit.component.less'],
})
export class TestProjectEditComponent implements OnInit {
  data?: DynamicComponentData;
  stationList: StationEdit[] = [];
  project: TestProjectInfo = new TestProjectInfo();
  ifEdit: boolean = false;
  // 只读
  ifView: boolean = false;
  stationCodes: SelectOption[] = [];
  listColumn: StationColumn[] = [
    {
      title: '基站ID',
      width: 105,
      type: 'select',
      property: 'stationCode',
      required: true,
    },
    {
      title: '基站名',
      width: 105,
      property: 'stationName',
      required: true,
    },
    {
      title: '经纬度',
      width: 330,
      type: 'latlng',
      property: 'latlng',
    },
    {
      title: '备注',
      property: 'remark',
      width: 200,
    },
    {
      title: '新增',
      width: 32,
      maxWidth: 32,
      columnIconClass: 'sl-plus-circle-16',
      property: '删除',
      columnType: 'icon',
      iconClass: 'sl-close-red-16',
      type: 'icon',
      onClickHead: () => {
        this.onAdd();
      },
      onClick: (column, data) => {
        this.onDeleteStaion(data);
      },
      ifViewHidden: true,
    },
  ];
  constructor(
    private slModal_: SlModalService,
    private slValidateService: SlValidateService,
    private httpStation_: StationService,
    private httpProject_: StationTestProjectService,
    private mapLayerService: MapLayerComponentService
  ) {}

  ngOnInit(): void {
    this.initData();
    this.getStationCode();
  }
  async initData() {
    if (!this.data) return;
    const { params } = this.data,
      { ifEdit, inData, ifView = false } = params;
    this.ifEdit = ifEdit;
    this.ifView = ifView;
    if (ifEdit || ifView) {
      // 编辑
      this.project = await this.httpProject_.getInfo(inData.id);
      this.stationList = this.initStationList(this.project.stationList);
    } else {
      // 新增
    }
  }
  async getStationCode() {
    const res = await this.httpStation_.getListWithoutPage({});
    this.stationCodes = res.map((el) => {
      return {
        id: el.id,
        label: el.stationCode,
        value: el.stationCode,
      };
    });
  }
  onSelectChange($event: string, item: StationEdit) {
    item.id = this.stationCodes.find((el) => el.value == $event)!.id;
  }
  /**重新构造stationlist */
  initStationList(stations: Station[]): StationEdit[] {
    return stations.map((station) => new StationEdit(station));
  }
  onAdd() {
    this.stationList = [...this.stationList, new StationEdit()];
  }
  onDelete() {
    this.slModal_.openPromptModal({
      type: 'confirm',
      content: '确定是否删除?',
      okCb: async () => {
        try {
          await this.httpProject_.delete(this.project.id);
          this.slModal_.openPromptModal({
            type: 'success',
            content: '删除成功！',
          });
          setTimeout(() => {
            this.closeAndRefresh();
          }, 100);
        } catch (e: any) {
          this.slModal_.openPromptModal({
            type: 'error',
            content: e || '系统异常！',
          });
        }
      },
    });
  }
  /**去重 */
  filterStationCodes(value: string) {
    return this.stationList.some((station) => station.stationCode == value);
  }
  trackByItems(index: number, item: any) {
    return item.uuid;
  }
  closeAndRefresh() {
    this.mapLayerService.refreshComponent({
      name: 'TestProjectListComponent',
    });
    this.mapLayerService.removeAllByName('TestProjectEditComponent');
    this.mapLayerService.refreshComponent({
      name: 'StationLayerComponent',
    });
  }
  onSave(validateForm: NgForm) {
    validateForm.form.markAllAsTouched();
    const valid = this.slValidateService.validateFormWithAlert(validateForm, 1);
    if (!valid) return;
    this.project.stationList = this.stationList.map((station) => {
      const { coordinate } = station,
        { lat, lng } = coordinate,
        { d: latD, m: latM, s: latS } = lat,
        { d: lngD, m: lngM, s: lngS } = lng;
      return {
        ...station,
        latitude: DMSToLatlng(latD!, latM!, latS!),
        longitude: DMSToLatlng(lngD!, lngM!, lngS!),
      };
    });
    try {
      this.httpProject_.onSave(this.project);
      this.slModal_.openPromptModal({
        type: 'success',
        content: '保存成功！',
      });
      setTimeout(() => {
        this.closeAndRefresh();
      }, 100);
    } catch (e: any) {
      this.slModal_.openPromptModal({
        type: 'error',
        content: e || '系统异常！',
      });
    }
  }
  onDeleteStaion(data: any) {
    const idx = this.stationList.findIndex((el) => el === data);
    this.stationList.splice(idx, 1);
  }
}
class StationEdit implements Station {
  id: string;
  stationCode: string;
  longitude?: number | undefined;
  latitude?: number | undefined;
  stationName?: string | undefined;
  remark?: string | undefined;
  coordinate: CoordinatePackage;
  uuid: string;
  constructor(station?: Station) {
    const {
      id = '',
      latitude,
      longitude,
      stationName = '',
      remark = '',
      stationCode = '',
    } = station || {};
    this.id = id;
    this.latitude = latitude;
    this.longitude = longitude;
    this.stationName = stationName;
    this.remark = remark;
    this.stationCode = stationCode;
    this.coordinate = new CoordinatePackage(
      latitude ? latlngToDMS(latitude) : new Coordinate(),
      longitude ? latlngToDMS(longitude) : new Coordinate()
    );
    this.uuid = uuid();
  }
}
interface StationColumn {
  title: string;
  width: number;
  maxWidth?: number;
  property: string;
  columnType?: string;
  columnIconClass?: string;
  options?: any[] | Promise<Array<any>> | any;
  iconClass?: string;
  required?: boolean;
  ifViewHidden?: boolean;
  onClickHead?: () => void;
  onClick?: (column: StationColumn, data: any) => void;
  type?: string;
}
interface SelectOption {
  id: string;
  label: string;
  value: string;
}
