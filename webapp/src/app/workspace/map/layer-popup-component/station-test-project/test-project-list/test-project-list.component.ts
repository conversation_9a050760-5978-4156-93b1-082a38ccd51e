import { Component, OnInit } from '@angular/core';
import { TestProjectInfo } from '../station-test.model';
import { MapLayerComponentService } from '../../../service/map-layer-component.service';
import { StationTreeNode } from '../../station/station.model';
import { DynamicComponent } from 'src/app/shared/models';
import { StationTestProjectService } from '../station-test-project.service';
import { MapStateService } from '../../../service/map-state.service';

@Component({
  selector: 'app-test-project-list',
  templateUrl: './test-project-list.component.html',
  styleUrls: ['./test-project-list.component.less']
})
export class TestProjectListComponent implements OnInit {
  projectList: TestProjectInfo[] = []
  inOpenEdit: boolean = false;
  spinning: boolean = false
  private map: L.Map
  constructor(private mapLayerService: MapLayerComponentService, private httpTestProject_: StationTestProjectService, private mapState: MapStateService) { 
    this.map = this.mapState.getMapInstance();
  }

  ngOnInit(): void {
    this.spinning = false;
    this.getProjectList();
  }
  async getProjectList() {
    this.spinning = true;
    const res = await this.httpTestProject_.getListWithoutPage({});
    this.spinning = false;
    this.projectList = res;
  }
  onAdd() {
    this.mapLayerService.addComponent({
      name: 'TestProjectEditComponent',
      title: '项目新增',
      type: 'popup',
      closeCb: () => {
        this.inOpenEdit = false;
      },
      data: {
        position: {
          left: 355,
          top: 48,
        },
        params: {
          ifEdit: false
        }
      }
    });
  }
  eventChange($event: {type: 'edit' | 'info', data: StationTreeNode}, item: TestProjectInfo) {
    const {type: eventType, data} = $event, {type} = data;
    console.log($event)
    const toComponent: DynamicComponent = {
      name: 'TestProjectEditComponent',
      type: 'popup',
      closeCb: () => {
        this.inOpenEdit = false;
      },
      data: {
        position: {
          left: 355,
          top: 48,
        },
        params: {
          inData: data,
          ifEdit: true
        }
      }
    }
    if (eventType == 'edit') {
      // 点击项目编辑
      if (type == 'project') {
        this.inOpenEdit = true;
        toComponent.title = '项目编辑'
        toComponent.data!.params = {
          inData: item,
          ifEdit: true
        }
      }
    } else if (eventType == 'info') {
      if (type == 'project') {
        toComponent.title = '项目详情'
        toComponent.data!.params = {
          inData: item,
          ifEdit: false,
          ifView: true
        }
      }
    }
    type == 'project' && this.mapLayerService.addComponent(toComponent)
  }
}
