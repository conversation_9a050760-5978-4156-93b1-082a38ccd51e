import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzEmptyModule } from 'ng-zorro-antd/empty';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { TestProjectListComponent } from './test-project-list/test-project-list.component';
import { TestProjectEditComponent } from './test-project-edit/test-project-edit.component';
import { StationModule } from '../station/station.module';
import { SlButtonModule } from 'src/app/shared/modules/sl-button/sl-button.module';
import { SlValidateModule } from 'src/app/shared/modules/sl-validate/sl-validate.module';
@NgModule({
  declarations: [TestProjectListComponent, TestProjectEditComponent],
  imports: [
    StationModule,
    CommonModule,
    FormsModule,
    NzInputModule,
    NzEmptyModule,
    NzSelectModule,
    NzSpinModule,
    NzIconModule,
    SlButtonModule,
    SlValidateModule
  ],
})
export class StationTestProjectModule { }
