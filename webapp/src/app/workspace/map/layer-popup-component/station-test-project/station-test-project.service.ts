import { BaseCURDService } from 'src/app/shared/services/base-curd.service';
import { BaseHttpService } from './../../../../shared/services/base-http.service';
import { Injectable } from '@angular/core';
import { TestProjectInfo } from './station-test.model';

@Injectable({
  providedIn: 'root'
})
export class StationTestProjectService extends BaseCURDService<TestProjectInfo>{
  constructor(protected baseHttp_: BaseHttpService) { 
    super(baseHttp_, '/api/stationproject');
  }
  /**保存项目基站信息 */
  onSave(params: TestProjectInfo) {
    this.baseHttp_.post(`${this.baseUrl}/updateProject`, params)
  }
}
