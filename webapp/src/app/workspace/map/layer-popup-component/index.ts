import { DynamicComponent } from 'src/app/shared/models';
// 绘图工具
import { DYNAMIC_MAP_DRAW_TOOL_COMPONENTS } from './map-draw-tool';
// // 保护区
import { DYNAMIC_WINDPOWER_PROTECT_COMPONENTS } from './windpower-protect';
// // 事故
// import { DYNAMIC_ACCIDENT_MAIN_COMPONENTS } from './windpower-accident';
// // 标绘
// import { DYNAMIC_MAP_POLTTING_COMPONENTS } from './map-poltting';
// 其他辅助模块 （测距、图层切换）
import { DYNAMIC_MAP_AUXILIARY_COMPONENTS } from './map-auxiliary';
// // 船舶
import { DYNAMIC_WINDPOWER_SHIP_COMPONENTS } from './windpower-ship';
// // 测试用例
// import { DYNAMIC_CASE_TEST_COMPONENTS } from './case-test';
// // 船舶轨迹
// import { DYNAMIC_MAP_TRACK_COMPONENTS } from './map-ship-trajectory';
import { DYNAMIC_STATION_COMPONENTS } from './station';
import { DYNAMIC_TEST_PROJECT_COMPONENTS } from './station-test-project';
import { DYNAMIC_MAP_BROADCAST_COMPONENTS } from './broadcast/broadcast';
// 动态加载的组件
export const DYNAMIC_LAYER_POPUP_COMPONENTS = [
  ...DYNAMIC_MAP_DRAW_TOOL_COMPONENTS,
  ...DYNAMIC_WINDPOWER_PROTECT_COMPONENTS,
  ...DYNAMIC_MAP_BROADCAST_COMPONENTS,
  // ...DYNAMIC_ACCIDENT_MAIN_COMPONENTS,
  // ...DYNAMIC_MAP_POLTTING_COMPONENTS,
  ...DYNAMIC_MAP_AUXILIARY_COMPONENTS,
  ...DYNAMIC_WINDPOWER_SHIP_COMPONENTS,
  // ...DYNAMIC_CASE_TEST_COMPONENTS,
  // ...DYNAMIC_MAP_TRACK_COMPONENTS,
  ...DYNAMIC_STATION_COMPONENTS,
  ...DYNAMIC_TEST_PROJECT_COMPONENTS,
];

// 初始化需要加载的组件
// 说明：如果menuCode为-1,代表不需要权限
export const DEFAULT_LOAD_COMPONENTS: Array<DynamicComponent> = [
  // // 事故区图层
  // { name: 'WindpowerAccidentLayerComponent', type: 'layer', menuCode: '1.3.4' },
  // // 保护区图层
  // { name: 'WindpowerProtectLayerComponent', type: 'layer', menuCode: '-1' },
  // // 船舶图层
  { name: 'WindpowerShipLayerComponent', type: 'layer', menuCode: '-1' },
  // 虚拟航标
  { name: 'NavigationAidLayerComponent', type: 'layer', menuCode: '-1' },
  // 基站图层
  { name: 'StationLayerComponent', type: 'layer', menuCode: '-1'}
];

// 开发环境下使用
export const DEV_DEFAULT_LOAD_COMPONENTS: Array<DynamicComponent> = [];
