@import "/src/styles/mixins";

.broadcast {
    padding: 10px;
    width: 450px;
    height: 350px;
    display: flex;
    flex-direction: column;
}

.last,
.item {
    display: flex;
    min-height: 36px;
    align-items: center;
    text-align: center;
}


nz-select,
input {
    min-width: 190px;
}

.last {
    margin-top: 50px;
    width: 100%;
    justify-content: center;
}

.name {
    flex-shrink: 0;
    width: 100px;
}

.text {
    flex-grow: 1;
}

.btn {
    width: 120px;
    height: 36px;
    color: #FFF;
    background-color: #2f71f5;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn.dis {
    background-color: #2f71f5b4;
    color: #e1e1e1;
    cursor: default;
}

textarea {
    width: 100%;
    height: 100px;
    resize: none;
}