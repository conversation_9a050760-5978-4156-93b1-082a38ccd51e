import { Component } from "@angular/core";
import { DynamicComponent } from "src/app/shared/models";
import { StationService } from "../station/station.service";
import { BaseHttpService } from "src/app/shared/services/base-http.service";
import { SlModalService } from "src/app/shared/modules/sl-modal/sl-modal.service";

@Component({
    templateUrl: './broadcast.html',
    styleUrls: ['./broadcast.less']
})
export class BroadcastComponent {
    constructor(private service: StationService, private slModal_: SlModalService, private http_: BaseHttpService) { }
    public opts: any[] = []
    public text: string = "";
    public id: string = "";
    public mmsi: string = "";
    public ifEn: boolean = false;

    ngOnInit(): void {
        this.service.getListWithoutPage({ ifPage: false }).then(res => {
            this.opts = res.map(e => { return { id: e.id, time: e.sysUpdated } }).sort((a, b) => {
                return new Date(a.time!).getTime() > new Date(b.time!).getTime() ? -1 : 1
            })
        });
    }
    public onBroadcast() {
        let { id, mmsi, text, ifEn } = this
        if (!id || !mmsi || !text) return;
        this.http_.post('api/Station/broadcast', { id, mmsi, text, ifEn }).then(e => {
            this.slModal_.openPromptModal({
                type: 'success',
                content: '播发成功！',
            });
        }).catch(e => {
            this.slModal_.openPromptModal({
                type: 'error',
                content: e || '系统异常！',
            });
        })
    }
}


// 标绘
export const DYNAMIC_MAP_BROADCAST_COMPONENTS: Array<DynamicComponent> = [
    // 标绘图层
    { name: 'BroadcastComponent', component: BroadcastComponent },
];