<div class="broadcast">
    <div class="item">
        <div class="name">基站ID：</div>
        <div>
            <nz-select nzShowSearch [(ngModel)]="id">
                <nz-option *ngFor="let opt of opts" [nzValue]="opt.id" [nzLabel]="opt.id"></nz-option>
            </nz-select>
        </div>
    </div>
    <div class="item">
        <div class="name">MMSI：</div>
        <div>
            <input nz-input type="text" [(ngModel)]="mmsi" />
        </div>
    </div>
    <div class="item">
        <div class="name">语言：</div>
        <div>
            <nz-radio-group [(ngModel)]="ifEn">
                <label nz-radio [nzValue]="false">中文</label>
                <label nz-radio [nzValue]="true">英文</label>
            </nz-radio-group>
        </div>
    </div>
    <div class="item">
        <div class="name">播发内容：</div>
        <div class="text">
            <textarea [(ngModel)]="text"></textarea>
        </div>
    </div>
    <div class="last">
        <div [class.dis]="!id||!mmsi||!text" class="btn" (click)="onBroadcast()">播发</div>
    </div>
</div>