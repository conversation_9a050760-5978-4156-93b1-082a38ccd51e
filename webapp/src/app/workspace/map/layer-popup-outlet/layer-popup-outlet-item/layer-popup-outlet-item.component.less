@import "/src/styles/mixins";

.item-component-container {
  &.popup {
    position: absolute;
    z-index: 420;
    // border-radius: 2px;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;

    .item-component-title {
      border-top-left-radius: 5px;
      border-top-right-radius: 5px;
      height: 36px;
      display: flex;
      align-items: center;
      position: relative;

      .title {
        font-size: 16px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .send {
        width: 50px;
        height: 28px;
        position: absolute;
        right: 32px;
        top: 50%;
        transform: translateY(-50%);
        background-color: #2f71f5;
        color: #FFF;
        border-radius: 3px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
      }

      .close {
        position: absolute;
        right: 12px;
        display: flex;
        align-items: center;
        top: 50%;
        transform: translateY(-50%);
      }

      &.default {
        .title {
          color: #333;
          max-width: 340px;
        }

        padding: 0 12px;
        background: #fff;
        background-image: linear-gradient(-87deg,
          rgba(238, 243, 252, 0) 0%,
          #f4f8ff 51%,
          #eef3fc 99%);
      }

      &.primary {
        .title {
          color: #fff;
          max-width: 220px;
        }

        background: #2f71f5;
        display: flex;
        justify-content: center;
        border-bottom: 1px solid #eceef4;
        border-right: 1px solid #fff;

        .left-bg,
        .right-bg {
          display: inline-block;
          height: 100%;
          width: 95px;
          position: absolute;
        }

        .left-bg {
          left: 0;
          background: url("/assets/img/modal/popup-left2.png") no-repeat;
        }

        .right-bg {
          right: -1px;
          background: url("/assets/img/modal/popup-right2.png") no-repeat;
        }

        &.large-mode>.left-bg {
          width: 175px;
          background: url("/assets/img/modal/popup-left.png") no-repeat;
        }

        &.large-mode>.right-bg {
          width: 175px;
          background: url("/assets/img/modal/popup-right.png") no-repeat;
        }
      }

      &.draggable {
        cursor: move;
      }

      &:focus-visible,
      &:focus {
        outline: none;
      }
    }

    .item-component-content.popup,
    .item-component-content.popup>.item-component-content__inner {
      border-bottom-left-radius: 4px;
      border-bottom-right-radius: 4px;
    }

    // .item-component-content.popup {
    //   overflow: hidden;
    // }
    .item-component-content.with-title {
      background: #fff;
      box-shadow: 0px 2px 4px 0px rgba(3, 90, 222, 0.3);
    }
  }

  &.draggle {
    box-shadow: none;
    border-radius: 0;
    cursor: move;
  }

  &.leaflet-popup,
  &.layer {
    position: relative;
  }

  &.active {
    display: block;
  }

  &.inactive {
    display: none;
  }
}

/deep/ .dynamic-leaflet-popup-container {
  z-index: 1;
  margin-bottom: 0;

  .leaflet-popup-content-wrapper {
    box-shadow: none;
    background: none;
    border-radius: 0;
    padding: 0;

    .leaflet-popup-content {
      margin: 0;
    }
  }

  .leaflet-popup-tip-container {
    display: none;
  }
}