import { Subscription } from 'rxjs';
import { Component, On<PERSON><PERSON>roy, OnInit, Type } from '@angular/core';
import { DynamicComponent } from 'src/app/shared/models';
import { MapLayerComponentService } from '../service/map-layer-component.service';
import { environment } from 'src/environments/environment';
import { DEFAULT_LOAD_COMPONENTS, DEV_DEFAULT_LOAD_COMPONENTS, DYNAMIC_LAYER_POPUP_COMPONENTS } from '../layer-popup-component';
import { Principal } from 'src/app/shared/services/principal.service';
import { isRelated } from './layer-popup-rule';
import { GlobalState } from 'src/app/global.state';
import * as _ from 'lodash';
@Component({
  selector: 'app-layer-popup-outlet',
  templateUrl: './layer-popup-outlet.component.html'
})
export class LayerPopupOutletComponent implements OnInit, On<PERSON><PERSON>roy {
  //3种动态组件  popup || layer || leaftlet popup
  dynamicComponents: Array<DynamicComponent> = [];

  // 所有注册的动态组件实例
  private _registeredComponents: Array<DynamicComponent> = [];

  private dynamicSubscription: Subscription;

  constructor(
    private layerComponentService: MapLayerComponentService,
    private principal: Principal,
    private state: GlobalState
  ) {
    this._registeredComponents = _.cloneDeep(DYNAMIC_LAYER_POPUP_COMPONENTS);
    this.dynamicSubscription = this.layerComponentService.change.subscribe(
      (res) => {
        console.log('来展示', res);
        if (res && res.component) {
          const { component, type, list } = res;
          this.genComponentList(component, type);
        }
      }
    );
  }

  ngOnInit(): void {
    this.loadDeafultComponnets();
  }

  ngOnDestroy(): void {
    this.dynamicComponents = []
    this.layerComponentService.clear()
    if (this.dynamicSubscription) this.dynamicSubscription.unsubscribe();
  }

  /**
   * 加载默认显示的组件
   */
  private loadDeafultComponnets() {
    // 只在开发模式加载
    if (!environment.production) {
      DEV_DEFAULT_LOAD_COMPONENTS.forEach((ele) => {
        this.layerComponentService.addComponent(ele);
      });
    }
    // 生产模式和开发模式都会加载
    DEFAULT_LOAD_COMPONENTS.forEach((ele) => {
      if (ele.menuCode == '-1' || this.principal.hasAuthority(ele.menuCode!)) {
        this.layerComponentService.addComponent(ele);
      }
    });
  }

  /**
   * 动态组件加载
   * @param c
   * @param type
   */
  private genComponentList(
    c: DynamicComponent,
    type: string,
  ) {
    let index = this.getIndex(c);
    let _index = -1;
    switch (type) {
      case 'add':
        if (!c.component) {
          const d = this.getRegisterComponentByName(c.name!);
          if (d) {
            c.component = d.component
            c.hasChild = d.hasChild
            c.parentName = d.parentName
            c.aliasName = d.aliasName
          }
        }
        if (c.component) {
          c.handleType = 'add';
          // 如果有别名，判断是否已存在相同别名组件（别名组件不能共存）
          if (c.aliasName) {
            const aliasIndex = this.getIndexByAliasName(c.aliasName)
            // 如果存在相同别名组件，先删除相同的组件
            if (aliasIndex > -1) {
              this.layerComponentService.removeComponent(this.dynamicComponents[aliasIndex])
            }
          }
          this.dynamicComponents.push(c);
          this.shouldRemoveAlarmList(c);
        }

        break;
      case 'remove':
        if (index == -1) return;
        if (c.destroy) {
          // 直接销毁组件实例
          c.handleType = 'remove';
          this.dynamicComponents.splice(index, 1);
          // 销毁的组件实例如何含有children 组件，也需要一并销毁
          if (c.hasChild) {
            const childrenCList = this.dynamicComponents.filter(ele => ele.parentName == c.name)
            this.layerComponentService.removeComponents(childrenCList)
          }
        } else {
          // 适用于经常打开 或 关闭的图层
          // 不直接销毁实例， 实现display:none 隐藏组件
          this.dynamicComponents[index].active = false;
          // this.dynamicComponents.splice(index, 1, c);
        }
        break;
      case 'refresh':
        if (index > -1) {
          c.handleType = 'refresh';
          this.dynamicComponents.splice(index, 1, c);
          this.shouldRemoveAlarmList(c)
        }
        break;
      case 'remove_all':
        _index = this.dynamicComponents.findIndex((ele) => ele.name == c.name);
        while (_index > -1) {
          this.dynamicComponents.splice(_index, 1);
          _index = this.dynamicComponents.findIndex(
            (ele) => ele.name == c.name
          );
        }
        break;
      case 'remove_id':
        _index = this.dynamicComponents.findIndex((ele) => ele.uuid == c.uuid);
        if (_index > -1) {
          this.dynamicComponents.splice(_index, 1);
        }
        break;
      case 'show':
        if (index > -1) {
          this.dynamicComponents[index].active = true;
          this.shouldRemoveAlarmList(this.dynamicComponents[index])
        }
        break;
      case 'hide': // 使用remove destroy:false
        if (index > -1) {
          this.dynamicComponents[index].active = false;
        }
        break;
    }
  }

  private shouldRemoveAlarmList(item: DynamicComponent) {
    setTimeout(() => {
      if (isRelated(item)) {
        this.state.notifyDataChanged(
          'ship-alarm-collapsed',
          new Date().getTime()
        );
      }
    });
  }

  trackByItems(index: number, item: DynamicComponent): string {
    return item.uuid!;
  }

  private getRegisterComponentByName(name: string): DynamicComponent | null {
    const item = this._registeredComponents.find((ele) => ele.name == name);
    if (!item) {
      console.error('没有找到' + name + '对应的 component');
      return null
    }
    return item;
  }

  private getIndex(c: DynamicComponent) {
    return this.dynamicComponents.findIndex((ele) => ele.name == c.name);
  }


  /**
   * 根据别名查找
   * @param aliasName 
   * @returns 
   */
  private getIndexByAliasName(aliasName: string) {
    return this.dynamicComponents.findIndex((ele) => ele.aliasName == aliasName);
  }
}
