<div class="map-container-wrapper" [class.station-tree-expanded]="stationTreeExpanded">
  <div #map class="map-container-main" [style.height]="bottomAlarmHeightPx" (contextmenu)="closeMenu($event)"></div>
</div>

<!--topLeft,topRight,bottomRight,bottomLeft 四个边角位置 start-->
<!--通过继承L.Control 的方式-->
<ng-container #vc></ng-container>

<!--二使用直接放置组件的形式，需要通过css 样式进行定位-->
<!-- <app-handle-layer-control></app-handle-layer-control> -->

<!--topLeft,topRight,bottomRight,bottomLeft 四个边角位置 end-->

<!--动态组件出口：加载地图上自定义的layer图层， popup 弹窗-->
<app-layer-popup-outlet [style.height]="bottomAlarmHeightPx"></app-layer-popup-outlet>