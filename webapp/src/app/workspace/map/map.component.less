@import "./styles/index";

.map-container-wrapper {
  height: 100%;
  width: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  .map-container-main {
    position: relative;
    height: 100%;
    width: 100%;
    z-index: 1;
    // 自定义鼠标形状
    .map-tool-line-cursor();
    // // 画图工具基础样式
    .map-tool-base-active();
    // // 画图工具
    .map-tool-active();
    // 历史轨迹
    .history-track-active();
  }
}
app-layer-popup-outlet {
  width: 100%;
  height: 100%;
  display: block;
  position: absolute;
  left: 0;
  top: 0;
}
