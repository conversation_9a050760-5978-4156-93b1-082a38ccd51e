import * as L from "leaflet";

/**
 *
 * 自定义 Control
 * @export
 * @class BaseHandleLayerControl
 * @extends {L.Control}
 */
export class BaseHandleLayerControl extends L.Control {
    private _map!: L.Map
    private _container!: HTMLElement
    private _position: L.ControlPosition
    private _html!: HTMLElement
    constructor(position: L.ControlPosition = 'topleft', html?: HTMLElement) {
        super()
        this._position = position
        if (html) this._html = html
        this.setPosition(this._position)
    }

    onAdd(map: L.Map) {
        this._map = map
        this._container = L.DomUtil.create('div', 'leaflet-control-' + this._position + '-container');
        if (this._html) {
            this._container.appendChild(this._html)
        }
        return this._container
    }
    onRemove() {
        if (this._container)
            L.DomUtil.remove(this._container)
    }
}
