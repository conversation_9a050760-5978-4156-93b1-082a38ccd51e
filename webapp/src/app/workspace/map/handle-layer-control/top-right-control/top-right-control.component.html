<!-- <div class="top-right-control"> -->
<!-- <button class="sl-lg sl-primary-btn" (click)="test()">模拟测试</button> -->
<!-- <ul>
    <li>
      <a href="javascript:void(0)" (click)="drawLine()">
        <i
          [ngClass]="isLineActive ? 'sl-draw-line-active' : 'sl-draw-line'"
        ></i>
      </a>
    </li>
    <li>
      <a href="javascript:void(0)" (click)="drawRect()">
        <i
          [ngClass]="isRectActive ? 'sl-draw-rect-active' : 'sl-draw-rect'"
        ></i>
      </a>
    </li>
    <li>
      <a href="javascript:void(0)" (click)="drawCircle()">
        <i
          [ngClass]="
            isCircleActive ? 'sl-draw-circle-active' : 'sl-draw-circle'
          "
        ></i>
      </a>
    </li>
    <li>
      <a href="javascript:void(0)" (click)="drawPolygon()">
        <i
          [ngClass]="
            isPolygonActive ? 'sl-draw-polygon-active' : 'sl-draw-polygon'
          "
        ></i>
      </a>
    </li>
  </ul> -->
<!-- </div> -->