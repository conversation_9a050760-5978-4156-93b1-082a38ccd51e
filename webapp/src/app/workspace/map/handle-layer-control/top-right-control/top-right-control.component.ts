import { MapLayerComponentService } from './../../service/map-layer-component.service';
import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-top-right-control',
  templateUrl: './top-right-control.component.html',
  styleUrls: ['./top-right-control.component.less']
})
export class TopRightControlComponent implements OnInit {

  isLineActive: boolean = false
  isRectActive: boolean = false
  isCircleActive: boolean = false
  isPolygonActive: boolean = false

  constructor(private mapLayerService: MapLayerComponentService) { }

  ngOnInit(): void {
  }
  test() {
    this.mapLayerService.addComponent(
      {
        name: 'CaseTestComponent',
        type: 'popup',
        title: '测试用例',
        data: {
          size: { width: 500 },
          position: {
            top: 80,
            right: 20,
          },
        },
      },
    )
  }
  drawLine() {
    this.isLineActive = !this.isLineActive
    if (this.isLineActive) {
      this.mapLayerService.addComponent({ type: 'layer', name: 'MapToolLineLayerComponent' })
    } else {
      this.mapLayerService.removeAllByName('MapToolLineLayerComponent')
    }
  }

  drawCircle() {
    this.isCircleActive = !this.isCircleActive
    if (this.isCircleActive) {
      this.mapLayerService.addComponent({ type: 'layer', name: 'MapToolCircleLayerComponent' })
    } else {
      this.mapLayerService.removeAllByName('MapToolCircleLayerComponent')
    }
  }

  drawRect() {
    this.isRectActive = !this.isRectActive
    if (this.isRectActive) {
      this.mapLayerService.addComponent({ type: 'layer', name: 'MapToolRectLayerComponent' })
    } else {
      this.mapLayerService.removeAllByName('MapToolRectLayerComponent')
    }
  }

  drawPolygon() {
    this.isPolygonActive = !this.isPolygonActive
    if (this.isPolygonActive) {
      this.mapLayerService.addComponent({ type: 'layer', name: 'MapToolPolygonLayerComponent' })
    } else {
      this.mapLayerService.removeAllByName('MapToolPolygonLayerComponent')
    }
  }

}
