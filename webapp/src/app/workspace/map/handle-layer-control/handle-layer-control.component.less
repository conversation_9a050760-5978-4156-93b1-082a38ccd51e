@margin-spacing: 10px;
:host {
  display: block;
  position: relative;
  width: 100%;
}
.handle-area-bottom-left,
.handle-area-top-left,
.handle-area-bottom-right,
.handle-area-top-right {
  position: absolute;
  z-index: 410; // left-pane 400
}
.handle-area-top-left {
  left: @margin-spacing;
  top: @margin-spacing;
}
.handle-area-bottom-left {
  bottom: @margin-spacing;
  left: @margin-spacing;
}
.handle-area-top-right {
  top: @margin-spacing;
  right: @margin-spacing;
}

.handle-area-bottom-right {
  bottom: @margin-spacing;
  right: @margin-spacing;
}
