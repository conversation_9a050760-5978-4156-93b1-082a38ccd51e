import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { filter } from 'rxjs/operators';
import { GlobalState } from 'src/app/global.state';
import { DynamicComponent } from 'src/app/shared/models';
import { MapDrawToolService } from '../../layer-popup-component/map-draw-tool/map-draw-tool.service';
import { MapOption } from '../../models/MapOption.model';
import { MapLayerComponentService } from '../../service/map-layer-component.service';
import { MapStateService } from '../../service/map-state.service';
import { transformPosUnit } from '../../utils/map-tools';
@Component({
  selector: 'app-bottom-right-control',
  templateUrl: './bottom-right-control.component.html',
  styleUrls: ['./bottom-right-control.component.less']
})
export class BottomRightControlComponent implements OnInit, OnD<PERSON>roy {
  mapStateChangeSubscription: Subscription;
  mouseHoverPosition: any = {};
  ifFullscreen: boolean = false;
  bouysNum = 0;
  scale: any = {};
  mousePositionStr: string = '';
  mousePositionFomart: '0' | '1' = '0' // 0 显示度 分 秒 1 显示 度 分
  zoom: number = 0;
  map!: L.Map;
  shipNumbers: number = 0;
  cutlineShow: boolean = false;
  currentDynamic: DynamicComponent | undefined
  // 关闭时不需要销毁的动态组件（destroy false）
  private cachedDynamicList: Array<DynamicComponent> = []
  private dynamicList: Array<DynamicComponent> = [
    {
      name: 'PolttingListComponent',
      type: 'popup',
      title: '标绘',
      titleType: 'primary',
      data: {
        position: {
          right: 50,
          bottom: 68
        }
      }, // 点击popup 关闭图标的回调
      closeCb: () => {
        this.currentDynamic = undefined
      }
    },
    {
      name: 'MapCoverageComponent',
      type: 'popup',
      title: '图层',
      titleType: 'primary',
      destroy: false,
      data: {
        position: {
          right: 50,
          bottom: 68
        }
      },
      // 点击popup 关闭图标的回调
      closeCb: () => {
        this.currentDynamic = undefined
      }
    },
    {
      name: 'MapRangingDistanceLayerComponent',
      type: 'layer',
    },
  ]
  constructor(
    private mapState: MapStateService,
    public router: Router,
    private state: GlobalState,
    private mapLayerService: MapLayerComponentService,
    private mapToolService: MapDrawToolService,
  ) {

    this.mapToolService.change$.pipe(
      filter(ele => ele.name == 'MapRangingDistanceLayerComponent')
    ).subscribe(res => {
      const { destroy, name } = res
      if (destroy) {
        this.currentDynamic = undefined
        this.mapLayerService.removeComponent({ name, destroy })
      }
    })
    this.state.subscribe('bottomLeftShipNumbers', (len: number) => {
      this.shipNumbers = len;
    });
    this.mapStateChangeSubscription = this.mapState.mapOptChange
      .pipe(filter((ele: MapOption) => !!ele))
      .subscribe((res) => {
        const { mouseHoverPosition, viewBounds, zoomLevel, shipCount } = res;
        setTimeout(() => {
          if (mouseHoverPosition) {
            const { latlngStr, latlngStr2 } = mouseHoverPosition
            this.mousePositionFomart == '0' ?
              this.mousePositionStr = latlngStr!
              : this.mousePositionStr = latlngStr2!
          }
          if (zoomLevel) {
            this.zoom = zoomLevel;
          }
          if (viewBounds) {
            this.getScale(viewBounds);
          }
          if (shipCount != undefined) {
            this.bouysNum = shipCount;
          }
        });
      });
  }
  ngOnDestroy(): void {
    this.mapStateChangeSubscription.unsubscribe();
  }
  ngOnInit(): void {
    this.map = this.mapState.getMapInstance();
    this.zoom = this.map.getZoom();
    this.getScale(this.map.getBounds());
    this.updateMousePosition()
  }

  // 手动更新鼠标坐标位置显示
  private updateMousePosition() {
    const center = this.map.getCenter();
    const mouseHoverPosition = transformPosUnit(center.lat, center.lng);
    this.mousePositionFomart == '0' ?
      this.mousePositionStr = mouseHoverPosition.latlngStr!
      : this.mousePositionStr = mouseHoverPosition.latlngStr2!
  }

  mapZoomIn() {
    this.map.zoomIn();
  }
  mapZoomOut() {
    this.map.zoomOut();
  }

  toggleFullscreen() {
    if (!this.ifFullscreen) {
      const docElmWithBrowsersFullScreenFunctions =
        document.documentElement as HTMLElement & {
          mozRequestFullScreen(): Promise<void>;
          webkitRequestFullscreen(): Promise<void>;
          msRequestFullscreen(): Promise<void>;
        };

      if (docElmWithBrowsersFullScreenFunctions.requestFullscreen) {
        docElmWithBrowsersFullScreenFunctions.requestFullscreen();
      } else if (docElmWithBrowsersFullScreenFunctions.mozRequestFullScreen) {
        /* Firefox */
        docElmWithBrowsersFullScreenFunctions.mozRequestFullScreen();
      } else if (
        docElmWithBrowsersFullScreenFunctions.webkitRequestFullscreen
      ) {
        /* Chrome, Safari and Opera */
        docElmWithBrowsersFullScreenFunctions.webkitRequestFullscreen();
      } else if (docElmWithBrowsersFullScreenFunctions.msRequestFullscreen) {
        /* IE/Edge */
        docElmWithBrowsersFullScreenFunctions.msRequestFullscreen();
      }
      this.ifFullscreen = true;
    } else {
      const docWithBrowsersExitFunctions = document as Document & {
        mozCancelFullScreen(): Promise<void>;
        webkitExitFullscreen(): Promise<void>;
        msExitFullscreen(): Promise<void>;
      };
      if (docWithBrowsersExitFunctions.exitFullscreen) {
        docWithBrowsersExitFunctions.exitFullscreen();
      } else if (docWithBrowsersExitFunctions.mozCancelFullScreen) {
        /* Firefox */
        docWithBrowsersExitFunctions.mozCancelFullScreen();
      } else if (docWithBrowsersExitFunctions.webkitExitFullscreen) {
        /* Chrome, Safari and Opera */
        docWithBrowsersExitFunctions.webkitExitFullscreen();
      } else if (docWithBrowsersExitFunctions.msExitFullscreen) {
        /* IE/Edge */
        docWithBrowsersExitFunctions.msExitFullscreen();
      }
      this.ifFullscreen = false;
    }
  }
  // 切换坐标显示
  switchLatlngFormat() {
    this.mousePositionFomart == '0' ? this.mousePositionFomart = '1' : this.mousePositionFomart = '0'
    this.updateMousePosition()
  }

  /**
   * 添加图层或者组件
   */
  addComponent(name: string, destroy?: boolean) {
    const dynamic = this.dynamicList.find(ele => ele.name === name)
    const cachedDynamic = this.cachedDynamicList.find(ele => ele.name === name)
    if (!dynamic) return
    if (!this.currentDynamic) {
      this.currentDynamic = dynamic
      if (cachedDynamic) {
        this.mapLayerService.showComponent(cachedDynamic)
      } else {
        this.mapLayerService.addComponent(dynamic)
        if (dynamic.destroy === false) {
          this.cachedDynamicList.push(dynamic)
        }
      }
    } else {
      const currentIsDestroy = this.currentDynamic.destroy !== false
      // 移除当前的currentDynamic
      if (currentIsDestroy) {
        this.mapLayerService.removeComponent(this.currentDynamic)
      } else {
        this.mapLayerService.hideComponent(this.currentDynamic)
      }
      if (this.currentDynamic.name != name) {
        this.currentDynamic = dynamic
        // add or show 
        if (cachedDynamic) {
          this.mapLayerService.showComponent(cachedDynamic)
        } else {
          this.mapLayerService.addComponent(dynamic)
          if (dynamic.destroy === false) {
            this.cachedDynamicList.push(dynamic)
          }
        }
      } else {
        this.currentDynamic = undefined
      }
    }
  }
  private getScale(viewBounds: L.LatLngBounds) {
    let northEast = viewBounds.getNorthEast(),
      southWest = viewBounds.getSouthWest();
    let width = this.map.getSize().x;
    let dissLng = Math.abs(northEast.lng - southWest.lng);
    let averLat = (northEast.lat + southWest.lat) / 2;
    let distance = this.map.distance([averLat, 0], [averLat, dissLng]);
    distance = (distance / width) * 50;
    let text = '';
    if (distance > 2000) {
      distance = distance / 1852;
      text = ' nm';
    } else {
      text = ' m';
    }
    let num = distance;
    let power = 1;
    while (num > 10) {
      power = power * 10;
      num = Math.ceil(num / 10);
    }
    num = Math.ceil(num) * power;
    this.scale.width = (50 * num) / distance + 'px';
    this.scale.info = num + text;
  }


}
