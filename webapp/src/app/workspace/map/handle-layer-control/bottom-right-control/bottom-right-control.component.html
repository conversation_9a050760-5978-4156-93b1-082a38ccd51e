<div class="handle-bottom-right-container">
    <ul style="margin-bottom: 6px;">
        <!-- <li [class.active]="currentDynamic?.name=='MapCoverageComponent'" *slHasAnyAuthority="'1.4.1'">
            <a href="javascript:void(0)" title="图层" (click)="addComponent('MapCoverageComponent')">
                <i class="sl-coverage{{currentDynamic?.name=='MapCoverageComponent'&&'-active'||''}}-16"></i>
            </a>
        </li> -->
        <!-- <li [class.active]="currentDynamic?.name=='PolttingListComponent'" *slHasAnyAuthority="'1.4.2'">
            <a href="javascript:void(0)" title="标绘" (click)="addComponent('PolttingListComponent',false)">
                <i class="sl-poltting{{currentDynamic?.name=='PolttingListComponent'&&'-active'||''}}-16"></i>
            </a>
        </li> -->
    </ul>
    <ul>
        <li [class.active]="currentDynamic?.name=='MapRangingDistanceLayerComponent'"><a href="javascript:void(0)"
                title="测距" (click)="addComponent('MapRangingDistanceLayerComponent')">
                <i class="sl-ranging{{currentDynamic?.name=='MapRangingDistanceLayerComponent'&&'-active'||''}}-16"></i>
            </a>
        </li>
        <li><a href="javascript:void(0)" title="放大" (click)="mapZoomIn()"><i class="sl-zoomin-16"></i></a></li>
        <li><a href="javascript:void(0)" title="缩小" (click)="mapZoomOut()"><i class="sl-zoomout-16"></i></a></li>
        <li><a href="javascript:void(0)" [title]="ifFullscreen?'退出全屏':'全屏'" (click)="toggleFullscreen()">
                <i [ngClass]="ifFullscreen?'sl-exit-fullscreen-16':'sl-fullscreen-16'" class=""></i>
            </a>
        </li>
    </ul>
    <!-- <div class="station-info">
        <span>基站连接正常</span>
    </div> -->
    <div class="scale-container">
        <span [ngStyle]="{ width: scale.width }">{{ scale.info }}</span>
        <span>{{ zoom }}级--{{shipNumbers}}艘</span>
    </div>
    <div class="position-container" [class.short-type]="mousePositionFomart=='1'">
        <a href="javascript:void(0)" (click)="switchLatlngFormat()"></a>
        <span style="padding-left: 4px;">{{ mousePositionStr }}</span>
    </div>
</div>