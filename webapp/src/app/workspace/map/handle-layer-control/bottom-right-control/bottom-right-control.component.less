.handle-bottom-right-container {
  position: relative;
  ul {
    display: flex;
    flex-direction: column;
    position: relative;
    align-items: center;
    justify-content: center;
    li > a {
      width: 36px;
      height: 36px;
      background: #fff;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    li + li {
      &::before {
        content: " ";
        display: block;
        width: 22px;
        height: 1px;
        background: rgba(14, 33, 89, 0.16);
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
      }
    }
    li.active > a {
      background: #2d74ef;
    }
  }
  .station-info,
  .scale-container,
  .position-container {
    position: absolute;
    background-color: #fff;
    position: absolute;
    right: 40px;

    background: #ffffff;
    box-shadow: 0px 2px 4px 1px rgba(36, 104, 245, 0.16);
    border-radius: 2px;
    display: flex;
    align-items: center;
  }

  .position-container {
    width: 194px;
    height: 26px;

    bottom: 0;
    & > a {
      width: 22px;
      height: 100%;
      background: url("/assets/img/map/latlng-switch.png") no-repeat;
    }
    &.short-type {
      width: 165px;
    }
  }
  .station-info {
    width: 165px;
    height: 24px;
    display: flex;
    bottom: 58px;
    padding-left: 20px;
    justify-content: center;
    align-items: center;
    span {
      position: relative;
      padding-left: 6px;
      &::before {
        content: " ";
        width: 8px;
        height: 8px;
        border-radius: 50% 50%;
        background: #1dac52;
        position: absolute;
        top: 50%;
        margin-top: -4px;
        left: -10px;
      }
    }
  }
  .scale-container {
    width: 165px;
    height: 24px;
    display: flex;
    justify-content: space-around;
    bottom: 30px;
    span {
      position: relative;

      &:first-child {
        text-align: center;

        &::after {
          content: " ";
          width: 100%;
          height: 8px;
          position: absolute;
          border-left: 1px solid rgba(51, 51, 51, 1);
          border-bottom: 1px solid rgba(51, 51, 51, 1);
          border-right: 1px solid rgba(51, 51, 51, 1);
          top: 50%;
          margin-top: -1px;
          left: -2px;
          opacity: 0.29;
        }
      }
    }
  }
}
