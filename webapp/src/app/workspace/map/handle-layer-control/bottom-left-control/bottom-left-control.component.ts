import { Component, ComponentRef, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import { GlobalState } from 'src/app/global.state';
import { AlarmMgtService } from 'src/app/workspace/alarm-mgt/alarm-mgt.service';
import { ShipAlarmVoiceService } from 'src/app/workspace/alarm-mgt/ship-alarm-voice.service';
import { VoiceMsg } from 'src/app/workspace/alarm-mgt/voice-msg.model';
import { TblAlarmrecord } from 'src/app/workspace/workspace-shared/models/tbl_alarmrecord';
import { ProtectShipAlarmComponent } from '../../layer-popup-component/windpower-protect/protect-ship-alarm/protect-ship-alarm.component';
import { MapLayerComponentService } from '../../service/map-layer-component.service';
import { HandleLayerControlService } from '../handle-layer-control.service';
@Component({
  selector: 'app-bottom-left-control',
  templateUrl: './bottom-left-control.component.html',
  styleUrls: ['./bottom-left-control.component.less']
})
export class BottomLeftControlComponent implements OnInit, OnDestroy {
  list: Array<TblAlarmrecord> = []
  componentInstance!: ComponentRef<ProtectShipAlarmComponent>
  dropdownVisible: boolean = false
  // 展开报警列表
  alaramExpanded: boolean = true
  // 音量状态：false 无声， true 有声
  soundStatus: boolean
  voiceMsgList: Array<VoiceMsg> = []
  // 当前选中的报警类型
  currentAlarm: { value: string, text: string, list: Array<TblAlarmrecord> }
  alarmTypeList: Array<{ value: string, text: string, color: string, list: Array<TblAlarmrecord> }> = [
    { value: '0', text: '全部', list: [], color: '#ED1313' },
    { value: '1', text: '一级报警', list: [], color: '#333' },
    { value: '2', text: '二级报警', list: [], color: '#ED1313' },
    { value: '3', text: '三级报警', list: [], color: '#ED1313' }
  ]
  alarmStatusList: Array<{ value: string, text: string }> = [
    {
      text: '报警中', value: '0'
    },
    {
      text: '已解除', value: '1'
    }
  ]

  // private websocketSub: Subscription

  constructor(
    private handleLayerControlService: HandleLayerControlService,
    private mapLayerService: MapLayerComponentService,
    private alarmService: AlarmMgtService,
    private shipAlarmVoiceService: ShipAlarmVoiceService,
    private state: GlobalState,
  ) {
    this.currentAlarm = this.alarmTypeList[0]
    this.soundStatus = this.shipAlarmVoiceService.volumeState
    // 监听报警列表状态
    // this.state.subscribe('ship-alarm-collapsed', () => {
    //   if (this.state) this.removeAlarmComponent();
    // });
    //websocket
    // this.websocketSub = this.alarmService.webSocket
    //   .subscribe(res => {
    //     this.resolveData(res, false)
    //   })
  }
  ngOnDestroy(): void {
    // this.websocketSub.unsubscribe()
  }
  ngOnInit(): void {
    // if (this.alaramExpanded) {
    //   this.addAlarmComponent()
    //   this.getList()
    // }
  }

  /**
   * 获取最新数据
   * @returns 
   */
  private getList() {
    setTimeout(() => {
      if (!this.alaramExpanded) return
      this.alarmService.getAlarming().then(res => {
        this.resolveData(res)
      }).catch(err => {
        this.handleLayerControlService.alarmSearching = { search: false, list: [] }
      })
    });
  }

  /**
   * 根据后台返回的数据进行加工处理
   * 1、计算不同报警类型的条数
   * 2、设置不同报警类型显示的字体颜色
   */
  private resolveData(list: Array<TblAlarmrecord>, search: boolean = true) {
    this.alarmTypeList.forEach(ele => ele.list = [])
    list.forEach(ele => {
      // 全部分类 list
      this.alarmTypeList[0].list.push(ele)
      // 去掉语音播报
      // const index = this.voiceMsgList.findIndex(e => e.id == ele.id)
      // if (index == -1 && ele.ifWebVoice == '0') {
      //   const msg: VoiceMsg = { id: ele.id!, msg: ele.alarmContent! }
      //   // console.log('语音播放', msg);
      //   // 语音播放
      //   this.shipAlarmVoiceService.addMsg(msg)
      //   this.voiceMsgList.push(msg)
      // }
      // 前端根据alarmType 设置其他一 二 三 级分类
      const item = this.alarmTypeList.find(e => e.value == ele.alarmType)
      if (item) {
        item.list.push(ele)
      }
      // 设置报警类型显示的text
      ele.alarmTypeText = item?.text
      // 设置报警状态显示的text
      const alarmStatus = this.alarmStatusList.find(e => e.value == ele.alarmStatus)
      ele.alarmStatusText = alarmStatus?.text
      // 设置颜色：二三级报警全部显示为红色 ,一级报警的报警状态显示为红色
      ele.color = item?.color || '#333'
      ele.shipIcon = this.alarmService.getShipIconByType(ele.shipTypeCode!)
    })
    this.list = this.currentAlarm.list
    if (this.alaramExpanded) {  // 如果展开，推送数据到列表
      this.handleLayerControlService.alarmHeight = 240
      this.handleLayerControlService.alarmSearching = { search, list: this.list }
    }
  }

  /**
   * 打开报警列表
   */
  private addAlarmComponent() {
    this.mapLayerService.addComponent({
      name: 'ProtectShipAlarmComponent',
      type: 'popup',
      data: {
        position: {
          left: 0,
          bottom: -240
        },
        size: {
          width: '100%'
        }
      }
    })
  }

  private removeAlarmComponent() {
    this.alaramExpanded = false
    this.handleLayerControlService.alarmHeight = 0
    this.mapLayerService.removeComponent({
      name: 'ProtectShipAlarmComponent'
    })
  }

  voiceSwitch() {
    this.soundStatus = !this.soundStatus
    this.shipAlarmVoiceService.volumeState = this.soundStatus
    this.shipAlarmVoiceService.play()
  }
  /**
   * 报警列表显示与隐藏切换
   */
  alarmSwitch() {
    this.alaramExpanded = !this.alaramExpanded
    if (this.alaramExpanded) {
      this.addAlarmComponent()
      this.getList()
    } else {
      this.removeAlarmComponent()
    }
  }

  /**
   * 报警类型切换
   * 调用接口返回最新数据
   * @param item 
   */
  alarmTypeChange(item: { value: string, text: string, list: Array<TblAlarmrecord> }) {
    this.currentAlarm = item
    this.getList()
  }
}
