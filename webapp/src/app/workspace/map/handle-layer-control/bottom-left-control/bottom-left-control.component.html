<!-- <div class="handle-bottom-left-container">
    <div class="bottom-left-switcher">
        <a class="item voice-switcher" href="javascript:void(0)" (click)="voiceSwitch()">
            <img src="/assets/img/map/alarm.gif" alt="">
        </a>
        <a class="item select" [(nzVisible)]="dropdownVisible" nz-dropdown [nzDropdownMenu]="menu" nzTrigger="click">
            <span>{{currentAlarm?.text}}（{{currentAlarm?.list?.length}}）</span>
            <i class="sl-caret-{{dropdownVisible?'up':'down'}}-16"></i>
        </a>
        <a class="item alarm-switcher" href="javascript:void(0)" (click)="alarmSwitch()">
            <i class="sl-double-arrow-{{alaramExpanded?'bottom':'up'}}-16"></i>
        </a>
        <a class="item alarm-list-link" title="报警记录" routerLink="/admin/alarm">
            <i class="sl-list-20"></i>
        </a>
    </div>
</div>
<nz-dropdown-menu #menu="nzDropdownMenu">
    <ul nz-menu nzSelectable>
        <li *ngFor="let item of alarmTypeList" [nzSelected]="currentAlarm.value==item.value" nz-menu-item
            (click)="alarmTypeChange(item)">{{item.text}}（{{item.list.length}}）</li>
    </ul>
</nz-dropdown-menu> -->