.handle-bottom-left-container {
  margin-left: -10px;
  margin-bottom: -10px;
  .bottom-left-switcher {
    width: 303px;
    height: 36px;
    background: url("/assets/img/map/alarm-list.png") no-repeat;
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding-right: 70px;
    .item {
      display: flex;
      align-items: center;
    }
    .voice-switcther {
      width: 24px;
      height: 24px;
      img {
        width: 100%;
        max-height: 100%;
      }
    }
    .select {
      color: #333;
      font-size: 15px;
    }
    .alarm-list-link {
      position: relative;
      &::before {
        content: " ";
        display: block;
        width: 1px;
        height: 20px;
        background: #e4e9f3;
        position: absolute;
        left: -8px;
      }
    }
  }
}
