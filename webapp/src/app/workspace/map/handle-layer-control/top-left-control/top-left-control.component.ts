import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { DynamicComponent } from 'src/app/shared/models';
import { MapLayerComponentService } from '../../service/map-layer-component.service';
import { Subscription } from 'rxjs';
import { debounceTime } from 'rxjs/operators';
import { WindpowerShipService } from '../../layer-popup-component/windpower-ship/windpower-ship.service';

@Component({
  selector: 'app-top-left-control',
  templateUrl: './top-left-control.component.html',
  styleUrls: ['./top-left-control.component.less']
})
export class TopLeftControlComponent implements OnInit, OnDestroy {

  currentDynamic: DynamicComponent | undefined

  shipChangeSub: Subscription

  list: Array<{ title: string, menuCode: string, icon: string, dynamic: DynamicComponent }> = [
    {
      title: '基站列表',
      icon: 'sl-protect-18',
      menuCode: '1.1',
      dynamic: {
        name: 'StationListComponent',
        title: '基站列表',
        type: 'popup',
        closeCb: () => {
          this.currentDynamic = undefined
        },
        data: {
          position: {
            left: 10,
            top: 48,
          },
          size: {
            width: 345
          }
        }
      }
    },
    {
      title: '项目列表',
      icon: 'sl-protect-18',
      menuCode: '1.1',
      dynamic: {
        name: 'TestProjectListComponent',
        title: '项目列表',
        type: 'popup',
        closeCb: () => {
          this.currentDynamic = undefined
        },
        data: {
          position: {
            left: 10,
            top: 48,
          },
          size: {
            width: 345
          }
        }
      }
    },
    // {
    //   title: '保护区',
    //   icon: 'sl-protect-18',
    //   menuCode: '1.1',
    //   dynamic: {
    //     name: 'ProtectListComponent',
    //     type: 'popup',
    //     data: {
    //       position: {
    //         left: 10,
    //         top: 48,
    //       },
    //       size: {
    //         width: 345
    //       }
    //     }
    //   }
    // },
    // {
    //   title: '船舶',
    //   icon: 'sl-ship-18',
    //   menuCode: '1.2',
    //   dynamic: {
    //     name: 'ShipListComponent',
    //     type: 'popup',
    //     data: {
    //       position: {
    //         left: 10,
    //         top: 48,
    //       },
    //       size: {
    //         width: 345
    //       }
    //     }
    //   }
    // },
    // {
    //   title: '事故',
    //   icon: 'sl-accident-18',
    //   menuCode: '1.3',
    //   dynamic: {
    //     name: 'AccidentMainComponent',
    //     type: 'popup',
    //     data: {
    //       position: {
    //         left: 10,
    //         top: 48,
    //       },
    //       size: {
    //         width: 345
    //       }
    //     }
    //   }
    // },
  ]

  constructor(private mapLayerService: MapLayerComponentService,
    private windpowerShipService: WindpowerShipService) {
    // 接收船舶区传递过来的组件 c
    this.shipChangeSub = this.windpowerShipService.handleTopLeftChange.pipe(debounceTime(500)).subscribe(c => {
      // 若当前选中的组件是传递过来的组件 刷新组件操作   否则新增组件
      this.currentDynamic?.name == c.name ? this.mapLayerService.refreshComponent(c) : this.addComponent(c)
    })
  }
  ngOnInit(): void {
    this.addComponent(this.list[0].dynamic)
  }
  ngOnDestroy(): void {
    this.shipChangeSub.unsubscribe()
  }

  /**
   * 
   * 添加动态组件
   * @param {DynamicComponent} dynamic
   * @memberof TopLeftControlComponent
   */
  addComponent(dynamic: DynamicComponent) {
    if (this.currentDynamic?.name != dynamic.name) {
      this.mapLayerService.addComponent(dynamic)
      if (this.currentDynamic) {
        this.mapLayerService.removeComponent(this.currentDynamic)
      }
      this.currentDynamic = dynamic
    } else {
      // 若果已经存在，删除当前组件
      dynamic.destroy = true
      this.mapLayerService.removeComponent(dynamic)
      this.currentDynamic = undefined
    }
  }
}
