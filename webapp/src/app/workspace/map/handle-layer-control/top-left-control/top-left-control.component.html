<ul>
    <ng-container *ngFor="let item of list;">
        <!-- *slHasAnyAuthority="item.menuCode" -->
        <li [class.active]="currentDynamic?.name==item.dynamic.name">
            <a href="javascript:void(0)" (click)="addComponent(item.dynamic)">
                <!-- <i style="margin-right: 8px;"
                    [ngClass]="currentDynamic?.name==item.dynamic.name?item.icon+'-active':item.icon"></i> -->
                {{item.title}}
            </a>
        </li>
    </ng-container>
</ul>