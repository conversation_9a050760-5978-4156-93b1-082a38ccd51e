ul {
  list-style: none;
  border-bottom: 1px solid #dee8fd;
  li {
    height: 38px;
    width: 115px;
    // background: #fff;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background: #2f71f5;
    position: relative;
    border-radius: 4px;
    a {
      font-size: 14px;
      // color: #333;
      // margin-left: 8px;
      display: flex;
      width: 80%;
      justify-content: center;
      align-items: center;
      color: #fff;
    }
    // &.active {
    // a {
    //   color: #2f71f5;
    // }
    // &::before {
    //   content: " ";
    //   position: absolute;
    //   display: block;
    //   width: 70px;
    //   height: 2px;
    //   background: #2f71f5;
    //   bottom: 0;
    // }
    // }
    // & + li::after {
    //   content: " ";
    //   position: absolute;
    //   display: block;
    //   height: 24px;
    //   width: 1px;
    //   background: #eceef4;
    //   left: 0;
    // }
  }
}
