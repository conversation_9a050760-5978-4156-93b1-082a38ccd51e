import { NzEmptyModule } from 'ng-zorro-antd/empty';
import { RouterModule } from '@angular/router';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { FormsModule } from '@angular/forms';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { HandleLayerControlComponent } from './handle-layer-control.component';
import { SlDirectivesModule } from 'src/app/shared/directives/directives.module';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { SharedModule } from 'src/app/shared/shared.module';
import { BottomRightControlComponent } from './bottom-right-control/bottom-right-control.component';
import { BottomLeftControlComponent } from './bottom-left-control/bottom-left-control.component';
import { TopLeftControlComponent } from './top-left-control/top-left-control.component';
import { TopRightControlComponent } from './top-right-control/top-right-control.component';
@NgModule({
  declarations: [
    HandleLayerControlComponent,
    BottomRightControlComponent,
    BottomLeftControlComponent,
    TopLeftControlComponent,
    TopRightControlComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    SharedModule,
    NzTabsModule,
    NzSelectModule,
    NzInputModule,
    NzDropDownModule,
    RouterModule,
    NzEmptyModule
  ],
  exports: [HandleLayerControlComponent],
})
export class HandleLayerControlModule { }
