import { BehaviorSubject, Subject } from 'rxjs';
import { Injectable } from '@angular/core';
import { TblAlarmrecord } from '../../workspace-shared/models/tbl_alarmrecord';

@Injectable({
  providedIn: 'root'
})
export class HandleLayerControlService {
  private _alarmHeight: number = 0
  private _alarmExpandChange$: BehaviorSubject<number> = new BehaviorSubject(this._alarmHeight)

  private _alarmList$: Subject<{ search: boolean, list: TblAlarmrecord[] }> = new Subject()

  constructor() { }

  get alarmSearching$() {
    return this._alarmList$.asObservable()
  }

  get alarmChange$() {
    return this._alarmExpandChange$.asObservable()
  }

  set alarmHeight(h: number) {
    this._alarmHeight = h
    this._alarmExpandChange$.next(this._alarmHeight)
  }

  set alarmSearching(data: { search: boolean, list: TblAlarmrecord[] }) {
    this._alarmList$.next(data)
  }
}
