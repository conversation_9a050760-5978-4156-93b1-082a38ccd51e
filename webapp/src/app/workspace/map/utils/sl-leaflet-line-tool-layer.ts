import * as L from 'leaflet'
export class SlLeafletLineToolLayer extends L<PERSON>Layer {
    private _options!: L.LayerOptions
    private tempLatlngs: Array<L.LatLng> = []
    private layerGroup: L.FeatureGroup
    private tempLayerGroup: L.FeatureGroup

    private tempLayerList: Array<any> = []
    private isFinised: boolean = false
    private lastCircleMarker!: L.CircleMarker // 最后一个点
    private tempCircleMakrder: L.CircleMarker | undefined // 临时点

    private realRouteLine: L.Polyline; //  实线
    private tempRouteLine: L.Polyline; // 虚线

    constructor() {
        super()
        this.tempLayerGroup = L.featureGroup()
        this.layerGroup = L.featureGroup()
        this.realRouteLine = this.genPolyline(this.tempLatlngs).addTo(this.layerGroup);
        this.tempRouteLine = this.genPolyline([]).addTo(this.layerGroup);
    }
    get len() {
        return (this.tempLatlngs && this.tempLatlngs.length) || 0;
    }
    onAdd(map: L.Map): this {
        this._map = map
        map.on('mousedown', this._drawSolidLine)
        map.on('mousemove', this._drawDashLine)
        map.on('click', this._executeListeners)
        this.layerGroup.addTo(map)
        this.tempLayerGroup.addTo(map)
        return this
    }

    private _executeListeners(e: L.LeafletMouseEvent) { }

    onRemove(map: L.Map): this {
        map.off('mousedown')
        map.off('mousemove')
        map.off('click')
        return this
    }

    private _drawDashLine(e: L.LeafletMouseEvent) {
        if (this.isFinised) return //this.len < 1 || 
        this.tempLayerGroup.clearLayers()
        const penultLatlng = this.tempLatlngs[this.len - 1];
        const distance = this.getDistance(penultLatlng, e.latlng);
        if (parseFloat(distance) > 0) {
            this.tempRouteLine.setLatLngs([this.tempLatlngs[0], e.latlng])
        }
        const circle = this.genCircleMarker(e.latlng);
        this.tempLayerGroup.addLayer(circle);
    }
    /**
       * 两点之间的距离 单位：海里
       * @param latlng1
       * @param latlng2
       */
    private getDistance(latlng1: L.LatLng, latlng2: L.LatLng): string {
        return Number(latlng1.distanceTo(latlng2) / 1852).toFixed(2);
    }
    private _drawSolidLine(e: L.LeafletMouseEvent) {
        const latlng = e.latlng
        this.removeTempCircle()
        if (this.isNotExist(latlng)) {
            this.tempLatlngs.push(latlng)
            this.lastCircleMarker = this.genCircleMarker(latlng)
            this.layerGroup.addLayer(this.lastCircleMarker)
            if (this.len > 1) {
                this.realRouteLine.setLatLngs(this.tempLatlngs)
            }
        }
    }

    /**
     *
     * 画线
     * @private
     * @param {(L.LatLngExpression[] | L.LatLngExpression[][])} latlngs
     * @param {L.PolylineOptions} [options]
     * @return {*} 
     * @memberof SlLeafletLineToolLayer
     */
    private genPolyline(
        latlngs: L.LatLngExpression[] | L.LatLngExpression[][],
        options?: L.PolylineOptions
    ) {
        let _opt = { weight: 2, color: '#BC9DEE' };
        if (options) {
            _opt = Object.assign({}, _opt, options);
        }
        return L.polyline(latlngs, _opt);
    }


    /**
     *
     * 移除临时circle
     * @private
     * @memberof SlLeafletLineToolLayer
     */
    private removeTempCircle() {
        if (this.tempCircleMakrder) {
            this.layerGroup.removeLayer(this.tempCircleMakrder);
            this.tempCircleMakrder = undefined;
        }
    }

    private removeAllTempLayer() {
        if (this.tempLayerList.length) {
            this.tempLayerList.forEach((ele) => {
                this.layerGroup.removeLayer(ele);
            });
        }
        this.tempLayerList = [];
    }

    /**
     *
     * 坐标点不存在已经绘制的坐标点数组中
     * @param {L.LatLng} latlng
     * @return {*} 
     * @memberof SlLeafletLineToolLayer
     */
    isNotExist(latlng: L.LatLng) {
        return !this.isExist(latlng);
    }

    /**
     * 坐标点是否存在已经绘制的坐标点数组中
     * @param latlng
     */
    isExist(latlng: L.LatLng) {
        return this.tempLatlngs.some(
            (ele) => ele.lat === latlng.lat && ele.lng === latlng.lng
        );
    }


    /**
     *
     * 画小圆点
     * @private
     * @param {L.LatLng} latlng
     * @param {L.CircleMarkerOptions} [options]
     * @return {*} 
     * @memberof SlLeafletLineToolLayer
     */
    private genCircleMarker(latlng: L.LatLng, options?: L.CircleMarkerOptions) {
        let _opt: L.CircleMarkerOptions = {
            color: '#9C3EE1',
            radius: 3,
            className: 'map-tool-circle',
        };
        if (options) _opt = Object.assign({}, _opt, options);
        return L.circleMarker(latlng, _opt);
    }

}