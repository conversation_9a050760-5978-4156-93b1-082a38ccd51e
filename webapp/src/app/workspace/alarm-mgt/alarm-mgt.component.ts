import { Component, OnInit, AfterViewInit } from '@angular/core';
import { Router } from '@angular/router';
import { BListPageModel, DynamicComponent } from 'src/app/shared/models';
import { WindpowerShipService } from '../map/layer-popup-component/windpower-ship/windpower-ship.service';
import { MapLayerComponentService } from '../map/service/map-layer-component.service';
import { iconRule } from './ship-type';
import * as L from 'leaflet';
import { MapStateService } from '../map/service/map-state.service';

@Component({
  selector: 'app-alarm-mgt',
  templateUrl: './alarm-mgt.component.html',
  styleUrls: ['./alarm-mgt.component.less']
})
export class AlarmMgtComponent implements OnInit, AfterViewInit {
  // 外部链接跳转的配置的动态数据,通过Router navigateByUrl 方法 配置 state 属性
  dynamicStateData: any = {}
  map: L.Map;
  //模板配置
  bListPageModel: BListPageModel = {
    title: '报警记录',
    requestUrl: '/api/Alarmrecord/getList',
    immediatelySearch: false,// 不立即发送getList请求
    searchTableItems: [
      [
        {
          label: '船舶名称/MMSI', property: 'keyword', type: 'input'
        },
        { label: '关联保护区', property: 'areaName', type: 'input' },
        {
          label: '报警类型', property: 'alarmType', type: 'select', isMultiple: true, selectOptions: [
            { text: '一级报警', value: '1' },
            { text: '二级报警', value: '2' },
            { text: '三级报警', value: '3' }
          ]
        },
        {
          label: '报警时间', property: 'AlarmTime', startTimeProperty: 'startTime',
          endTimeProperty: 'endTime', type: 'datepickerRange'
        },
      ]
    ],
    listTableItems: [
      { type: 'serial', title: '序号' },
      {
        type: 'link',
        title: '船舶名称',
        className: 'text-left windpower-link-text',
        property: 'shipName', width: 240,
        iconProperty: 'shipTypeCode',
        showTitle: true,
        iconRule: iconRule,
        callCb: (dataItem) => {
          if (dataItem.shipName.trim() == '') {
            return dataItem.mmsi
          } else {
            return dataItem.shipName
          }
        },
        extraCb: (dataItem) => {
          this.router.navigateByUrl('/admin/map');
          setTimeout(() => {
            this.getShipInfo(dataItem.mmsi)
          })
        }
      },
      {
        type: 'text',
        title: 'MMSI',
        property: 'mmsi',
        className: 'text-center',
        width: 140,
        showTitle: true
      },
      {
        type: 'text',
        title: '关联保护区',
        property: 'areaName',
        className: 'text-center',
        showTitle: true
      },
      {
        type: 'text',
        title: '报警类型',
        property: 'alarmType',
        className: 'text-center',
        textRule: [
          { text: '一级报警', value: '1' },
          { text: '二级报警', value: '2' },
          { text: '三级报警', value: '3' }
        ],
        showTitle: true
      },
      {
        type: 'text',
        title: '报警内容',
        property: 'alarmContent',
        className: 'text-center',
        showTitle: true
      },
      {
        type: 'text',
        title: '报警时间',
        property: 'alarmTime',
        className: 'text-center',
        width: 200,
        showTitle: true
      },
      {
        type: 'text',
        title: '报警状态',
        property: 'alarmStatus',
        className: 'text-center',
        width: 120,
        textRule: [
          {
            text: '报警中', value: '0', color: '#ED1313'
          },
          {
            text: '已解除', value: '1', color: '#50699B'
          }
        ],
        showTitle: true
      },
      {
        type: 'text',
        title: '解除时间',
        property: 'removeTime',
        className: 'text-center',
        width: 200,
        showTitle: true
      },
    ],
  };
  constructor(
    private router: Router,
    private mapLayerService: MapLayerComponentService,
    private windpowerShipService: WindpowerShipService,
    private mapState: MapStateService,
  ) {
    this.map = this.mapState.getMapInstance()
    // 动态获取路由的 data 数据
    this.dynamicStateData = this.router.getCurrentNavigation()?.extras.state
  }
  ngAfterViewInit(): void {
    setTimeout(() => {
      if (this.dynamicStateData && this.dynamicStateData instanceof Object) {
        Object.keys(this.dynamicStateData).forEach(key => {
          this.bListPageModel.search![key] = this.dynamicStateData[key]
        })
      }
      if (this.bListPageModel.refreshCb) {
        this.bListPageModel.refreshCb(this.bListPageModel.search)
      }
    });
  }
  ngOnInit(): void { }

  // 获取船舶详细
  private getShipInfo(mmsi: string) {
    this.windpowerShipService.getInfo(mmsi).then(res => {
      const item = res
      const latlng: L.LatLng = L.latLng(item.latitude, item.longitude)
      let shipInfoComponent: DynamicComponent = {
        title: `${item.shipName || item.mmsi}（详细）`,
        name: 'ShipWindpowerInfoComponent',
        type: 'popup',
        titleType: 'primary',
        data: {
          params: item,
          // 使用海图的定位弹出详情
          position: { latlng, offset: [0, 24] }
        }
      }
      this.mapLayerService.addComponent(shipInfoComponent)
      this.map.setView(latlng, 14)
    })
  }

}
