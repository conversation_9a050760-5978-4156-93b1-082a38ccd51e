import { Injectable } from '@angular/core';
import { BaseCURDService } from 'src/app/shared/services/base-curd.service';
import { BaseHttpService } from 'src/app/shared/services/base-http.service';
import { TblAlarmrecord, } from '../workspace-shared/models/tbl_alarmrecord';
import { iconRule } from './ship-type';
import { webSocket, WebSocketSubject } from 'rxjs/webSocket';
@Injectable({
  providedIn: 'root'
})
export class AlarmMgtService extends BaseCURDService<TblAlarmrecord> {
  // private alarmWebSocket: WebSocketSubject<Array<TblAlarmrecord>>;
  constructor(protected http: BaseHttpService) {
    super(http, '/api/Alarmrecord')
    // this.alarmWebSocket = webSocket(`ws://${window.location.host}/websocket`);
  }
  /**
   * 获取报警列表
   */
  getAlarming() {
    return this.http.get<TblAlarmrecord[]>(`/api/Alarmrecord/getAlarming`)
  }

  /**
   * 根据船舶类型获取船舶icon
   * @param shipType 船舶type
   * @returns 
   */
  getShipIconByType(shipType: string) {
    return shipType ? iconRule.find(ele => ele.value == shipType)?.icon : 'sl-other-ship-16'
  }

  /**
   * 连接websocket 
   * @param websocketUrl 
   * @returns 
   */
  // get webSocket() {
  //   return this.alarmWebSocket.asObservable();
  // }

}
