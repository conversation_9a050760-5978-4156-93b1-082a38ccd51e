import { Injectable } from '@angular/core';
import { VoiceMsg } from './voice-msg.model';

@Injectable({
  providedIn: 'root'
})
export class ShipAlarmVoiceService {
  private synth: SpeechSynthesis = window.speechSynthesis
  private voice: SpeechSynthesisVoice
  // 待播放的语音消息
  private toPlayMsgList: Array<VoiceMsg> = []

  // 已经播放的语音消息
  private cachedMsgList: Array<VoiceMsg> = []

  // 音量开关
  private volumeOpened: boolean = false;

  constructor() {
    this.voice = this.getWindowVoice()!
    this.synth.addEventListener('voiceschanged', (ev: Event) => {
      // console.log('ev', ev);
    })

  }
  get volumeState() {
    return this.volumeOpened
  }

  set volumeState(volumeOpened: boolean) {
    this.volumeOpened = volumeOpened
  }

  /**
   *
   * 添加语音消息
   * 如果已经播放，不再进行二次播放
   * 如果非静音状态，那么可以立即播放
   * 如果静音状态，放入到待播放列表中
   * @param {VoiceMsg} msg
   * @memberof ShipAlarmVoiceService
   */
  addMsg(voiceMsg: VoiceMsg) {
    // console.log('addMsg', voiceMsg);
    // console.log(this.cachedMsgList);
    if (this.hasPlayed(voiceMsg)) return
    this.toPlayMsgList.push(voiceMsg)
  }

  /**
   *
   * 逐条播放语音
   * @memberof ShipAlarmVoiceService
   */
  play() {
    if (this.volumeState) {
      this.synth.resume()
      while (this.toPlayMsgList.length > 0) {
        const voiceMsg = this.toPlayMsgList.shift()!
        this.speak(voiceMsg)
      }
      // console.log(this.cachedMsgList);
    } else {
      this.synth.pause()
    }
  }

  /**
   *
   * 已经播放过的语音
   * @param {VoiceMsg} msg
   * @return {*} 
   * @memberof ShipAlarmVoiceService
   */
  private hasPlayed(msg: VoiceMsg) {
    return this.cachedMsgList.findIndex(ele => ele.id == msg.id) > -1
  }


  /**
   *
   * 获取浏览器中语音 (中文 + 本地服务)
   * @private
   * @return {*} 
   * @memberof ShipAlarmVoiceService
   */
  private getWindowVoice() {
    return this.synth.getVoices().find(item => item.localService && item.lang == 'zh-CN')
  }

  /**
   *
   * 语音播放
   * @private
   * @param {VoiceMsg} voiceMsg
   * @memberof ShipAlarmVoiceService
   */
  private speak(voiceMsg: VoiceMsg) {
    const msg = new SpeechSynthesisUtterance(voiceMsg.msg)
    msg.lang = 'zh-CN'
    if (this.voice) {
      msg.voice = this.voice
    }
    this.synth.speak(msg)
    this.cachedMsgList.push(voiceMsg)
    // console.log(voiceMsg);
  }
}
