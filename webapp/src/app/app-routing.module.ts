import { NgModule } from '@angular/core';
import { PreloadAllModules, RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  { path: '', redirectTo: 'admin/map', pathMatch: 'full' },
  {
    path: 'login',
    loadChildren: () =>
      import('src/app/workspace/login/login.module').then((m) => m.LoginModule),
  },
  {
    path: 'admin',
    loadChildren: () =>
      import('./layout/layout.module').then((m) => m.LayoutModule),
  },
  // 错误页面
  {
    path: 'error',
    loadChildren: () =>
      import('src/app/workspace/page-error/page-error.module').then(
        (m) => m.PageErrorModule
      ),
  },
  { path: '**', redirectTo: 'error' },
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, {
      useHash: true,
      preloadingStrategy: PreloadAllModules,
    }),
  ],
  exports: [RouterModule],
})
export class AppRoutingModule { }
