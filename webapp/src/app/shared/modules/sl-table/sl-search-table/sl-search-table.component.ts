import { Component, EventEmitter, Input, OnInit, Output, OnDestroy } from '@angular/core';
import * as moment from 'moment';
import { Subject, Subscription } from 'rxjs';
import { debounceTime } from 'rxjs/operators';
import { HandleTableItem } from 'src/app/shared/models';

@Component({
  selector: 'sl-search-table',
  templateUrl: './sl-search-table.component.html',
  styleUrls: ['./sl-search-table.component.less'],
})
export class SlSearchTableComponent implements OnInit, OnDestroy {
  @Input() ifSearchAll: boolean = true;
  @Input() model: any = {};
  @Input() searchTableItems: Array<Array<HandleTableItem>> = [];
  // 默认输入类型框的长度
  @Input() controlWidth: number = 220; // 统一设置 ，可单独配置 tableItem controlWidth
  @Input() lableWidth: number = 150; // 统一设置，可单独配置 tableItem lableWidth
  @Input() searchButtonWidth?: number;
  @Output() searchChange: EventEmitter<any> = new EventEmitter();
  private clicks = new Subject<any>();
  private subscription!: Subscription;
  formartTime = 'YYYY-MM-DD HH:mm'; // 搜索日期格式

  constructor() {
    this.subscription = this.clicks.pipe(
      debounceTime(500)
    ).subscribe(e => {
      this.searchChange.emit(e)
    });
  }
  ngOnDestroy(): void {
    if (this.subscription) this.subscription.unsubscribe();
  }

  ngOnInit(): void {
    if (this.ifSearchAll == undefined) {
      this.ifSearchAll = true;
    }
  }
  /**
   * 时间类型 change 事件
   * @param $event
   * @param col
   */
  handleTimeChange($event: string, col: HandleTableItem) {
    col.value = $event;
    this.model[col.property!] = $event;
    if ($event != '3') {
      // 本日、本周、本月
      this.doSearchByTime(this.computedTime($event), col);
    }
  }
  /**
   * 输入框单项搜索
   */
  inputSearch(property: string, clear?: boolean) {
    if (clear) {
      this.model[property] = null;
    }
    this.clicks.next({ [property]: this.model[property] })
  }

  /**监听输入变化实时搜索 暂时不用实现 */
  inputKeyupChange(event: Event, property: string) {
    const value = (event.target as HTMLInputElement).value;
    // console.log('ddd',this.model[property])
    // if (isEmpty(value)) {
    //   this.searchTempPkg = undefined;
    // } else {
    //   this.searchValueChange$.next(value);
    // }
  }

  /**下拉框搜索 */
  selectSearch($event: any, property: string) {
    if ($event) {
      this.clicks.next({ [property]: this.model[property] });
    } else {
      this.clicks.next({ [property]: '' });
    }
    // console.log('selectSearch', $event);
  }

  /**
   * 自定义搜索时间
   * @param $event
   * @param col
   */
  customDateChange($event: Date[], col: HandleTableItem) {
    const [start, end] = $event;
    this.doSearchByTime(
      { startTime: start && moment(start), endTime: end && moment(end) },
      col
    );
  }

  /**
   * 计算时间类型
   * @param type
   * @returns
   */
  private computedTime(type: string): any {
    let startTime;
    let endTime;
    switch (type) {
      case '0': // 本日
        startTime = moment().startOf('day');
        endTime = moment().endOf('day');
        break;
      case '1': // 本周
        const weekOfDay: any = moment().format('E'); // 指定日期的周的第几天
        startTime = moment().subtract(weekOfDay - 1, 'days'); //周一日期
        endTime = moment().add(7 - weekOfDay, 'days'); //本周日
        break;
      case '2': // 本月
        startTime = moment().startOf('month');
        endTime = moment().endOf('month');
        break;
    }
    return { startTime, endTime };
  }

  /**通过时间搜索 */
  private doSearchByTime(date: any, col: HandleTableItem) {
    const { startTime, endTime } = date;
    col[`start${col.property}`] =
      (startTime && startTime.format(this.formartTime)) || undefined;
    col[`end${col.property}`] =
      (endTime && endTime.format(this.formartTime)) || undefined;
    const search = {};
    if (startTime && endTime) {
      search[`start${col.property}`] = col[`start${col.property}`];
      search[`end${col.property}`] = col[`end${col.property}`];
    }
    this.clicks.next(search);
  }

  /**单项时间搜索 */
  datepickerChange($event: any, item: HandleTableItem) {
    let value = '';
    if ($event) {
      value = moment($event).format(item.dateFormat || this.formartTime);
    }
    this.model[item.property!] = value;
    this.clicks.next({ [item.property!]: value });
  }

  /**时间范围 开始时间与结束时间 */
  timepickerChange($event: any, item: HandleTableItem) {
    console.log('$event', $event);
    let startTime = '';
    let endTime = '';
    if ($event.length > 0) {
      const [start, end] = $event;
      startTime = moment(start).format(item.dateFormat || this.formartTime);
      endTime = moment(end).format(item.dateFormat || this.formartTime);
    }
    this.model.startTime = startTime;
    this.model.endTime = endTime;
    this.clicks.next({ startTime, endTime });
  }

  /**
   * 重置搜索条件
   */
  resetSearch() {
    this.model = {};
    this.clicks.next(null)
  }

  /**
   * 查询全部条件
   */
  doSearch() {
    // console.log('model', this.model);
    this.clicks.next(this.model);
  }

  isPromise(value: any) {
    return value && value instanceof Promise;
  }
}
