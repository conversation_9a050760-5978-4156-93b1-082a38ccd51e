<div class="sl-table-search-container">
  <div class="sl-table-search__content">
    <table class="sl-table-search">
      <tr class="t-item" *ngFor="let trItem of searchTableItems; index as i">
        <ng-container *ngFor="let tdItem of trItem; index as j">
          <td [ngClass]="['td-label', tdItem.className || '']">
            {{ tdItem.label }}：
          </td>
          <td [ngClass]="['td-content', tdItem.type]" [ngSwitch]="tdItem.type" [attr.colspan]="tdItem.colspan">
            <ng-container *ngSwitchCase="'text'">
              {{ tdItem.staticText || tdItem.value! }}
            </ng-container>
            <ng-container *ngSwitchCase="'input'">
              <nz-input-group [nzSuffix]="suffixIconInput" nzSize="large"
                [style.width.px]="tdItem.controlWidth || controlWidth">
                <input type="text" [name]="tdItem.property!" (keyup)="inputKeyupChange($event,tdItem.property!)"
                  nz-input [(ngModel)]="model[tdItem.property!]"
                  [placeholder]="tdItem.placeholder || '请输入' + tdItem.label!" />
              </nz-input-group>
              <ng-template #suffixIconInput>
                <i class="sl-search input-suffix-icon" style="cursor: pointer;"
                  (click)="inputSearch(tdItem.property!)"></i>
                <i class="sl-close-circle-white input-suffix-icon" style="cursor: pointer;margin-left: 4px;"
                  *ngIf="model[tdItem.property!]" (click)="inputSearch(tdItem.property!,true)"></i>
              </ng-template>
            </ng-container>
            <ng-container *ngSwitchCase="'select'">
              <nz-select [nzClearIcon]="suffixIconSelect" nzSize="large" [name]="tdItem.property!"
                [(ngModel)]="model[tdItem.property!]" (ngModelChange)="selectSearch($event, tdItem.property!)"
                nzAllowClear [style.width.px]="tdItem.controlWidth || controlWidth"
                [nzPlaceHolder]="tdItem.placeholder || '请选择' + tdItem.label!">
                <nz-option *ngFor="
                    let optItem of !isPromise(tdItem.selectOptions)
                      ? tdItem.selectOptions
                      : (tdItem.selectOptions | async)
                  " [nzValue]="optItem.value" [nzLabel]="optItem.text">
                </nz-option>
              </nz-select>
              <ng-template #suffixIconSelect>
                <i class="sl-close-circle-white"></i>
              </ng-template>
            </ng-container>
            <ng-container *ngSwitchCase="'datepickerRange'">
              <div class="datepicker-range-container">
                <nz-radio-group class="sl-radio-group" (ngModelChange)="handleTimeChange($event, tdItem)"
                  [(ngModel)]="model[tdItem.property!]">
                  <label [class.inactive]="model[tdItem.property!] == '3'" nz-radio nzValue="0">本日</label>
                  <label [class.inactive]="model[tdItem.property!] == '3'" nz-radio nzValue="1">本周</label>
                  <label [class.inactive]="model[tdItem.property!] == '3'" nz-radio nzValue="2">本月</label>
                  <label [class.one]="model[tdItem.property!] == '3'" nz-radio nzValue="3">自定义</label>
                </nz-radio-group>
                <div class="datepicker-content" [style.display]="
                    model[tdItem.property!] == '3' ? 'flex' : 'none'
                  ">
                  <nz-range-picker nzSeparator="-" [(ngModel)]="tdItem[tdItem.property!]"
                    (ngModelChange)="customDateChange($event, tdItem)" class="sl-range-picker"></nz-range-picker>
                  <a href="javascript:void(0)" class="time-clear" (click)="handleTimeChange('0', tdItem)">
                    <i class="sl-close-circle-white"></i>
                  </a>
                </div>
              </div>
            </ng-container>
            <ng-container *ngSwitchCase="'datepicker'">
              <nz-date-picker nzSize="large" (ngModelChange)="datepickerChange($event, tdItem)"
                [(ngModel)]="model[tdItem.property! + 'DatePicker']"
                [style.width.px]="tdItem.controlWidth || controlWidth"
                [nzPlaceHolder]="tdItem.placeholder || '请输入' + tdItem.label!"></nz-date-picker>
            </ng-container>

            <ng-container *ngSwitchCase="'timepicker'">
              <nz-range-picker [nzShowTime]="{ nzFormat: 'HH:mm' }" nzFormat="yyyy-MM-dd HH:mm" ngModel
                (ngModelChange)="timepickerChange($event,tdItem)">
              </nz-range-picker>
            </ng-container>
          </td>
        </ng-container>
      </tr>
    </table>
  </div>
  <div [ngStyle]="{ width: searchButtonWidth ? searchButtonWidth! + 'px' : '' }" class="sl-table-search-handel"
    *ngIf="ifSearchAll">
    <button class="sl-primary-btn sl-lg" (click)="doSearch()">查询</button>
    <button class="sl-default-btn sl-lg" (click)="resetSearch()">重置</button>
  </div>
</div>