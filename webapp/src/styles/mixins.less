// 旋转角度
.transformRotate(@deg) {
    transform: rotate(@deg);
    /* IE 9 */
    -ms-transform: rotate(@deg);
    /* Firefox */
    -moz-transform: rotate(@deg);
    /* Safari 和 Chrome */
    -webkit-transform: rotate(@deg);
    -o-transform: rotate(@deg);
}

// 文本不可复制
.user-select(@value :none) {
    /* Firefox私有属性 */
    -moz-user-select: @value;
    /* WebKit内核私有属性 */
    -webkit-user-select: @value;
    /* IE私有属性(IE10及以后) */
    -ms-user-select: @value;
    -khtml-user-select: @value;
    /* KHTML内核私有属性 */
    /* Opera私有属性 */
    -o-user-select: @value;
    /* CSS3属性 */
    user-select: @value;
}

//滚动条的样式
.scrollbars(@size, @foreground-color, @background-color: mix(@foreground-color, white, 50%)) {
    ::-webkit-scrollbar {
        width: @size;
        height: @size;
    }

    ::-webkit-scrollbar-thumb {
        border-radius: @size;
        background: @foreground-color;
        cursor: pointer;
    }

    ::-webkit-scrollbar-track {
        background: @background-color;
        border-radius: 20px;
    }

    // For Internet Explorer
    body {
        scrollbar-face-color: @foreground-color;
        scrollbar-track-color: @background-color;
    }
}

//图标设置
.custom-icon-set(@size) {
    width: @size;
    height: @size;
    display: inline-block;
    vertical-align: middle;
    font-size: inherit;
    text-rendering: auto;
}

// 垂直居中
.translateYCenter() {
    top: 50%;
    transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
}

.translateXCenter() {
    left: 50%;
    transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    -webkit-transform: translateX(-50%);
}


.transform-rotate(@deg) {
    transform: rotate(@deg);
    -ms-transform: rotate(@deg);
    /* Internet Explorer */
    -moz-transform: rotate(@deg);
    /* Firefox */
    -webkit-transform: rotate(@deg);
    /* Safari 和 Chrome */
    -o-transform: rotate(@deg);
    /* Opera */
}