@sl-primary-color: #2d74ef;
@select-single-item-height-lg: 36px;
@input-height-lg: 36px;
@font-size-lg: 14px;
// tabset
@tabs-card-active-color: @sl-primary-color;
@tabs-ink-bar-color: @sl-primary-color;
@tabs-card-tab-active-border-top: 2px solid @sl-primary-color;
// btn
@btn-primary-bg: @sl-primary-color;
// link
@link-color: @sl-primary-color;
// TimePicker
// @picker-basic-cell-hover-color: @sl-primary-color;
// @picker-basic-cell-active-with-range-color: @sl-primary-color;
// @picker-basic-cell-hover-with-range-color: @sl-primary-color;
// Calendar
// @calendar-item-active-bg: @sl-primary-color;
// @calendar-border-color: @sl-primary-color;
// input
@input-border-color:rgba (205,210,218,1);
@input-hover-border-color: @sl-primary-color;
@input-placeholder-color: #999;

// checkbox
@checkbox-size: 14px;
@checkbox-color: @sl-primary-color;
@checkbox-check-color: #fff;
@checkbox-group-item-margin-right: 6px;
// dropdown
@dropdown-selected-color: @sl-primary-color;
@dropdown-menu-submenu-disabled-bg: #eef4fe;

// radio
@radio-size: 14px;
@radio-dot-color: @sl-primary-color;
