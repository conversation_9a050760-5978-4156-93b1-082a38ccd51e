@import "/src/styles/mixins";
@spriteIconUrl: "/assets/img/icon/";
.sprite-base-icon(@width,@height,@iconUrl) {
  display: inline-block;
  width: @width;
  height: @height;
  background: url("@{spriteIconUrl}@{iconUrl}");
  background-repeat: no-repeat;
  vertical-align: middle;
  font-size: inherit;
  text-rendering: auto;
}

/**
* @size 大小
* @row 第几行
* @col 第几列
*/
.sprite-icon-grid(@size,@row,@col) {
  .sprite-base-icon(@size * 1px,@size * 1px,"icon-@{size}.png");
  background-position-x: (@col - 1) * -(@size+1) * 1px;
  background-position-y: (@row - 1) * -(@size+1) * 1px;
}

/**
* @width 宽度
* @height 高度
* @row 第几行
* @col 第几列
*/
.sprite-icon-grid2(@width,@height,@row,@col) {
  .sprite-base-icon(@width * 1px,@height * 1px,"icon-@{width}-@{height}.png");
  background-position-x: (@col - 1) * -(@width+1) * 1px;
  background-position-y: (@row - 1) * -(@height+1) * 1px;
}
