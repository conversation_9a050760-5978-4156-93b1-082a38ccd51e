// 电子围栏
.sl-crawl-16 {
  .sprite-icon-grid(16, 1, 2);
}

// 灯浮
.sl-light-buoy-16 {
  .sprite-icon-grid(16, 1, 3);
}

// 灯桩
.sl-light-beacon-16 {
  .sprite-icon-grid(16, 1, 4);
}

// 登录牌标志
.sl-landing-card-16 {
  .sprite-icon-grid(16, 1, 5);
}

// 实体标
.sl-mark-16 {
  .sprite-icon-grid(16, 1, 6);
}

// 虚虚拟AIS
.sl-virtual-mark-16 {
  .sprite-icon-grid(16, 1, 7);
}

// 风机
.sl-fan-16 {
  .sprite-icon-grid(16, 1, 8);
}

// 海底电缆
.sl-cable-16 {
  .sprite-icon-grid(16, 1, 9);
}

// 船舶区 报警图标
.sl-alarm-16 {
  .sprite-icon-grid(16, 1, 10);
}

// 多边形 激活
.sl-polygon-active-16 {
  .sprite-icon-grid(16, 1, 11);
}

// 矩形 激活
.sl-rect-active-16 {
  .sprite-icon-grid(16, 1, 12);
}

// 圆 激活
.sl-circle-active-16 {
  .sprite-icon-grid(16, 1, 13);
}

// 线 激活
.sl-line-active-16 {
  .sprite-icon-grid(16, 1, 14);
}

// 眼睛 opened
.sl-eye-opened-16 {
  .sprite-icon-grid(16, 1, 15);
}

// 眼睛 closeed
.sl-eye-closed-16 {
  .sprite-icon-grid(16, 2, 1);
}

// 编辑
.sl-edit-16 {
  .sprite-icon-grid(16, 2, 2);
}

// 编辑图标激活状态
.sl-edit-active-16 {
  .sprite-icon-grid(16, 2, 3);
}

// 详情
.sl-info-16 {
  .sprite-icon-grid(16, 2, 4);
}

// 详情图标激活状态
.sl-info-active-16 {
  .sprite-icon-grid(16, 2, 5);
}

// 删除 红色
.sl-close-red-16 {
  .sprite-icon-grid(16, 2, 6);
}

// 删除 灰色
.sl-close-gray-16 {
  .sprite-icon-grid(16, 2, 7);
}

// 货船
.sl-cargo-ship-16 {
  .sprite-icon-grid(16, 2, 8);
}

// 油轮
.sl-oil-ship-16 {
  .sprite-icon-grid(16, 2, 9);
}

// 客船
.sl-passenger-ship-16 {
  .sprite-icon-grid(16, 2, 10);
}

// 渔船
.sl-fisher-ship-16 {
  .sprite-icon-grid(16, 2, 11);
}

// 游艇
.sl-yacht-ship-16 {
  .sprite-icon-grid(16, 2, 12);
}

// 高速船
.sl-high-speed-ship-16 {
  .sprite-icon-grid(16, 2, 13);
}

// 拖船/特种船
.sl-special-ship-16 {
  .sprite-icon-grid(16, 2, 14);
}

// 其他船
.sl-other-ship-16 {
  .sprite-icon-grid(16, 2, 15);
}

// 白名单
.sl-white-list-16 {
  .sprite-icon-grid(16, 3, 1);
}

// 定位图标
.sl-location-16 {
  .sprite-icon-grid(16, 3, 2);
}

// 点 red
.sl-point-red-16 {
  .sprite-icon-grid(16, 3, 3);
}

// 点 green
.sl-point-green-16 {
  .sprite-icon-grid(16, 3, 4);
}

// 点 blue
.sl-point-blue-16 {
  .sprite-icon-grid(16, 3, 5);
}

// 点 紫
.sl-point-purple-16 {
  .sprite-icon-grid(16, 3, 6);
}

// 点 船
.sl-point-ship-16 {
  .sprite-icon-grid(16, 3, 7);
}

// 点 tree
.sl-point-tree-16 {
  .sprite-icon-grid(16, 3, 8);
}

// 点 航标
.sl-point-navmark-16 {
  .sprite-icon-grid(16, 3, 9);
}

// 点 块
.sl-point-block-16 {
  .sprite-icon-grid(16, 3, 10);
}

// 点 灯塔
.sl-point-lighthouse-16 {
  .sprite-icon-grid(16, 3, 11);
}

// 点 停泊标志
.sl-point-park-16 {
  .sprite-icon-grid(16, 3, 12);
}

// 点 大厦
.sl-point-mansion-16 {
  .sprite-icon-grid(16, 3, 13);
}

// 点 锤子
.sl-point-hammer-16 {
  .sprite-icon-grid(16, 3, 14);
}

// 点 锚
.sl-point-anchor-16 {
  .sprite-icon-grid(16, 3, 15);
}

// 点 signB
.sl-point-signB-16 {
  .sprite-icon-grid(16, 4, 1);
}

// 点 货车
.sl-point-truck-16 {
  .sprite-icon-grid(16, 4, 2);
}

// 点 钢琴
.sl-point-piano-16 {
  .sprite-icon-grid(16, 4, 3);
}

// 点 桥梁
.sl-point-bridge-16 {
  .sprite-icon-grid(16, 4, 4);
}

// 返回 图标
.sl-return-back-16 {
  .sprite-icon-grid(16, 4, 5);
}

// 双箭头 下
.sl-double-arrow-bottom-16 {
  .sprite-icon-grid(16, 5, 2);
}

// 双箭头 上
.sl-double-arrow-up-16 {
  .sprite-icon-grid(16, 5, 3);
}

// 箭头 下
.sl-arrow-bottom-16 {
  .sprite-icon-grid(16, 5, 4);
}

// 箭头 右
.sl-arrow-up-16 {
  .sprite-icon-grid(16, 5, 5);
}

// 筛选图标
.sl-filter-16 {
  .sprite-icon-grid(16, 5, 11);
}

// 筛选 激活
.sl-filter-active-16 {
  .sprite-icon-grid(16, 5, 12);
}

// 地图取点
.sl-map-point {
  .sprite-icon-grid(16, 5, 13);
}

// 事故详情事故
.sl-accident-point {
  .sprite-icon-grid(16, 5, 14);
}

// 事故详情追责
.sl-account-point {
  .sprite-icon-grid(16, 5, 15);
}

// 启用
.sl-enable-16 {
  .sprite-icon-grid(16, 6, 2);
}

// 禁用
.sl-disable-16 {
  .sprite-icon-grid(16, 6, 3);
}

// 测距
.sl-ranging-16 {
  .sprite-icon-grid(16, 6, 4);
}

// 测距 激活
.sl-ranging-active-16 {
  .sprite-icon-grid(16, 6, 5);
}

// 标绘
.sl-poltting-16 {
  .sprite-icon-grid(16, 6, 6);
}

// 标绘 激活
.sl-poltting-active-16 {
  .sprite-icon-grid(16, 6, 7);
}

// 图层
.sl-coverage-16 {
  .sprite-icon-grid(16, 6, 8);
}

// 图层 激活
.sl-coverage-active-16 {
  .sprite-icon-grid(16, 6, 9);
}

// 放大
.sl-zoomin-16 {
  .sprite-icon-grid(16, 6, 10);
}

// 缩小
.sl-zoomout-16 {
  .sprite-icon-grid(16, 6, 11);
}

// 全屏
.sl-fullscreen-16 {
  .sprite-icon-grid(16, 6, 12);
}

// 退出全屏
.sl-exit-fullscreen-16 {
  .sprite-icon-grid(16, 6, 13);
}

// input 搜索图标
.sl-input-search-16 {
  .sprite-icon-grid(16, 6, 14);
}

// close
.sl-close-circle-white {
  .sprite-icon-grid(16, 7, 3);
}

// 树形展开三角上
.sl-caret-up-16 {
  .sprite-icon-grid(16, 7, 4);
}

// 树形展开三角下
.sl-caret-down-16 {
  .sprite-icon-grid(16, 7, 5);
}

// 树形展开三角右：将向下箭头逆时针旋转90deg
.sl-caret-right-16 {
  .sprite-icon-grid(16, 7, 5);
  .transformRotate(-90deg);
}

// right
.sl-right-arrow-16 {
  .sprite-icon-grid(16, 7, 6);
}

// left
.sl-left-arrow-16 {
  .sprite-icon-grid(16, 7, 7);
}

// + 号
.sl-plus-16 {
  .sprite-icon-grid(16, 7, 8);
}

// + 号 圆形
.sl-plus-circle-16 {
  .sprite-icon-grid(16, 7, 9);
}

// 事故
.sl-accident-16 {
  .sprite-icon-grid(16, 7, 12);
}
// 灯桩3 white
.sl-light-beacon2-16 {
  .sprite-icon-grid(16, 7, 13);
}
// 灯桩2 yellow
.sl-light-beacon3-16 {
  .sprite-icon-grid(16, 7, 14);
}
