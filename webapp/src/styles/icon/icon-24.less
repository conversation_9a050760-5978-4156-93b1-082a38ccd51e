// close
.sl-cose-24 {
  .sprite-icon-grid(24, 1, 1);
}

// anchor
.sl-anchor-24 {
  .sprite-icon-grid(24, 1, 2);
}

// back 返回
.sl-back-24 {
  .sprite-icon-grid(24, 1, 3);
}

// 视频
.sl-video-24 {
  .sprite-icon-grid(24, 1, 4);
}
// 下载
.sl-download-24 {
  .sprite-icon-grid(24, 1, 5);
}

// 暂停
.sl-pause-24 {
  .sprite-icon-grid(24, 1, 6);
}
// 手机
.sl-phone-24 {
  .sprite-icon-grid(24, 1, 7);
}
// message 短信
.sl-message-24 {
  .sprite-icon-grid(24, 1, 8);
}

//  check 对勾
.sl-circle-check-24 {
  .sprite-icon-grid(24, 1, 9);
}
// 数1
.sl-circle-number1-24 {
  .sprite-icon-grid(24, 1, 10);
}

// 数1
.sl-circle-number1-active-24 {
  .sprite-icon-grid(24, 1, 11);
}

// 数2
.sl-circle-number2-24 {
  .sprite-icon-grid(24, 1, 12);
}
// 数2 激活
.sl-circle-number2-active-24 {
  .sprite-icon-grid(24, 1, 13);
}

// 数3
.sl-circle-number3-24 {
  .sprite-icon-grid(24, 1, 14);
}

// 数3 激活
.sl-circle-number3-active-24 {
  .sprite-icon-grid(24, 1, 15);
}

// 数4
.sl-circle-number4-24 {
  .sprite-icon-grid(24, 2, 1);
}
// 数4 激活
.sl-circle-number4-active-24 {
  .sprite-icon-grid(24, 2, 2);
}

// 播放
.sl-play-24 {
  .sprite-icon-grid(24, 2, 3);
}
