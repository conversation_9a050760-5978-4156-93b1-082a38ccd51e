@sidebarIconUrl: "/assets/img/sidebar/";

.sidebar-base-icon(@iconName) {
  display          : inline-block;
  width            : 16px;
  height           : 16px;
  background       : url("@{sidebarIconUrl}@{iconName}");
  background-repeat: no-repeat;
  vertical-align   : middle;
  font-size        : inherit;
  text-rendering   : auto;
}

.sidebar-base-icon-dynamic(@className, @iconName) {
  .@{className} {
    .sidebar-base-icon("@{iconName}1.png");
  }

  &.selected {
    .@{className} {
      .sidebar-base-icon("@{iconName}2.png");
    }
  }

  &.list-item-selected {
    &>a.al-sidebar-list-link {
      .@{className} {
        .sidebar-base-icon("@{iconName}2.png");
      }
    }

  }

  // &:hover:not(.selected) {
  //   .@{className} {
  //     .sidebar-base-icon("@{iconName}2.png");
  //   }
  // }
}

.sidebar-icon() {
  .sidebar-base-icon-dynamic(warning-menu, "index");
  .sidebar-base-icon-dynamic(database-menu, "database");
  .sidebar-base-icon-dynamic(monitor-menu, "monitor1");
  .sidebar-base-icon-dynamic(menu-query, "query");
  .sidebar-base-icon-dynamic(menu-warn, "warn");
}