@import "../variables";

// 字体颜色
.danger-text {
  color: @danger-text-color;

  a,
  span {
    color: @danger-text-color;
  }

  &:hover {
    color: @danger-text-color;
  }
}

.warn-text {
  color: @warn-text-color;

  a,
  span {
    color: @warn-text-color;
  }

  &:hover {
    color: @warn-text-color;
  }
}

.success-text {
  color: @success-text-color;

  a,
  span {
    color: @success-text-color;
  }

  &:hover {
    color: @success-text-color;
  }
}

.default-text {
  color: @default-text-color;

  a,
  span {
    color: @default-text-color;
  }

  &:hover {
    color: @default-text-color;
  }
}

.secondary-text {
  color: @second-text-color;

  a,
  span {
    color: @second-text-color;
  }

  &:hover {
    color: @second-text-color;
  }
}

.undefault-text {
  color: @gray-text-color;

  a,
  span {
    color: @gray-text-color;
  }
}

.light-text {
  color: @light-text-color;

  a,
  span {
    color: @light-text-color;
  }
}

.primary-text {
  color: @primary-text-color;

  a,
  span {
    color: @primary-text-color;
  }

  &:hover {
    color: @primary-text-color;
  }
}

.windpower-link-text {
  color: @default-text-color;

  a,
  span {
    color: #2D74EF;
    margin-left: 5px;
  }

  &:hover {
    color: #2D74EF;
  }
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}
