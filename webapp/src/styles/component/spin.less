/*

* 加载中

* 样式来自 https://ng.ant.design/components/spin/zh

*/


.sl-spin-nested-loading {

    position: relative;

}



.sl-spin {

    font-family: "Chinese Quote", -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";

    font-size: 14px;

    font-variant: tabular-nums;

    line-height: 1.5;

    box-sizing: border-box;

    margin: 0;

    padding: 0;

    list-style: none;

    color: #1890ff;

    vertical-align: middle;

    text-align: center;

    opacity: 0;

    position: absolute;

    transition: -webkit-transform .3s cubic-bezier(.78, .14, .15, .86);

    transition: transform .3s cubic-bezier(.78, .14, .15, .86);

    transition: transform .3s cubic-bezier(.78, .14, .15, .86), -webkit-transform .3s cubic-bezier(.78, .14, .15, .86);

    display: none;
    // .sl-spin-loading{
    //     background: url('../../assets/img/icon/loading.gif') no-repeat;
    //     background-size: 100% 100%;
    //     width:250px;
    //     height: 200px;
    //     display: block;
    // }
}



.sl-spin-spinning {

    opacity: 1;

    position: static;

    display: inline-block;

}



.sl-spin-nested-loading>div>.sl-spin {

    display: block;

    /* position: absolute; */

    height: 100%;

    max-height: 400px;

    width: 100%;

    z-index: 4;

}



.sl-spin-nested-loading>div>.sl-spin.sl-spin-show-text .sl-spin-dot {

    /* margin-top: -20px; */

}



.sl-spin-nested-loading>div>.sl-spin .sl-spin-dot {

    position: absolute;

    top: 50%;

    left: 50%;

    margin: -10px;

}



.sl-spin-dot-spin {

    -webkit-transform: rotate(45deg);

    transform: rotate(45deg);

    -webkit-animation: 1.2s linear infinite antRotate;

    animation: 1.2s linear infinite antRotate;

}



.sl-spin-dot {

    position: relative;

    display: inline-block;

    font-size: 20px;

    width: 20px;

    height: 20px;

}



.sl-spin-dot i:nth-child(1) {

    left: 0;

    top: 0;

}



.sl-spin-dot i:nth-child(2) {

    right: 0;

    top: 0;

    -webkit-animation-delay: .4s;

    animation-delay: .4s;

}



.sl-spin-dot i:nth-child(3) {

    right: 0;

    bottom: 0;

    -webkit-animation-delay: .8s;

    animation-delay: .8s;

}



.sl-spin-dot i:nth-child(4) {

    left: 0;

    bottom: 0;

    -webkit-animation-delay: 1.2s;

    animation-delay: 1.2s;

}



.sl-spin-dot i {

    width: 9px;

    height: 9px;

    border-radius: 100%;

    background-color: #1890ff;

    -webkit-transform: scale(.75);

    transform: scale(.75);

    display: block;

    position: absolute;

    opacity: .3;

    -webkit-animation: 1s linear infinite alternate antSpinMove;

    animation: 1s linear infinite alternate antSpinMove;

    -webkit-transform-origin: 50% 50%;

    transform-origin: 50% 50%;

}



.sl-spin-nested-loading>div>.sl-spin .sl-spin-text {

    position: absolute;

    top: 50%;

    width: 100%;

    padding-top: 5px;

    text-shadow: 0 1px 2px #fff;

}



.sl-spin.sl-spin-show-text .sl-spin-text {

    display: block;

}



@keyframes antSpinMove {

    to {

        opacity: 1;

    }

}



@keyframes antRotate {

    to {

        transform: rotate(405deg);

    }

}