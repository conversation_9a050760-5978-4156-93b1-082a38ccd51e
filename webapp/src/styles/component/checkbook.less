.sl-ant-checkbox-disabled() {
  .ant-checkbox-disabled {
    cursor: default;

    .ant-checkbox-input {
      cursor: default;
    }

    & + span {
      color: #333;
      cursor: default;
    }
  }

  &.ant-checkbox-wrapper-checked {
    .ant-checkbox-disabled {
      .ant-checkbox-inner {
        border-color: #96b9f7 !important;
        background-color: #96b9f7;

        &::after {
          border-color: #fff;
        }
      }
    }
  }
}

.sl-ant-disabled-checkbox {
  &.ant-checkbox-wrapper {
    .sl-ant-checkbox-disabled();
  }

  // checkbox-group
  .ant-checkbox-wrapper-checked {
    .sl-ant-checkbox-disabled();
  }
}

// 可用状态：但是背景颜色为灰色
.sl-ant-default-checkbox {
  &.ant-checkbox-wrapper {
    .ant-checkbox {
      font-size: 12px;
      margin-bottom: 2px;

      .ant-checkbox-inner {
        width: 14px;
        height: 14px;

        &::after {
          left: 21%;
          top: 42%;
        }
      }

      & + span {
        padding-left: 4px;
        padding-right: 0;
      }

      &.ant-checkbox-checked {
        &::after {
          border-color: #8db4e2;
        }

        .ant-checkbox-inner {
          border-radius: 0;
          background-color: #8db4e2;
          border-color: #8db4e2;
        }
      }
    }
  }
}

// checkbox 组
// 禁用状态：背景颜色为蓝色
.sl-ant-checkbox-group {
  .ant-checkbox-group-item.ant-checkbox-wrapper {
    .ant-checkbox.ant-checkbox-disabled {
      .ant-checkbox-inner {
        background-color: #fff;
      }

      &.ant-checkbox-checked {
        .ant-checkbox-inner {
          background-color: #2d74ef !important;

          &::after {
            border-color: #fff;
            left: 21%;
            top: 42%;
          }
        }
      }

      & + span {
        color: #333;
      }
    }
  }
}
