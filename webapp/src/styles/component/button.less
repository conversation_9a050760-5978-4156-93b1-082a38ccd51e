@primary-btn-bg: #2d74ef;
@danger-btn-bg: #e02020;
@secondary-btn-bg: #0091ff;
@default-btn-bg: #eaecf1;
@default-btn-border-color: #e7e7e7;
@success-btn-bg: #2bb7a6;
@light-btn-bg: #dceaff;
@warn-btn-bg: #E28266;

.sl-btn(@bgColor, @borderColor) {
  position: relative;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  font-weight: 400;
  white-space: nowrap;
  text-align: center;
  background-image: none;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
  touch-action: manipulation;
  height: 28px;
  padding: 0 15px;
  font-size: 14px;
  border-radius: 2px;
  background: @bgColor;
  border-color: @borderColor;
  min-width: 72px;
  color: #fff;

  &.flex {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  i {
    margin-right: 2px;
  }

  &,
  &:focus {
    outline: none;
  }

  &.disabled {
    opacity: 0.5;
    // cursor: not-allowed;
  }

  &.inactive {
    opacity: 0.5;
  }

  &.sl-lg {
    height: 32px;
  }

  &.sl-md {
    height: 28px;
  }

  &.sl-sm {
    height: 24px;
  }
}

.sl-default-btn {
  .sl-btn(@default-btn-bg, @default-btn-border-color);
  color: #666666;
}

.sl-primary-btn {
  .sl-btn(@primary-btn-bg, @primary-btn-bg);

  &.inactive {
    background: #809dc7;
    border: 1px solid #809dc7;
    color: #fff;
  }
}

.sl-secondary-btn {
  .sl-btn(@secondary-btn-bg, @secondary-btn-bg);
}

.sl-danger-btn {
  .sl-btn(@danger-btn-bg, @danger-btn-bg);
}

.sl-success-btn {
  .sl-btn(@success-btn-bg, @success-btn-bg);
}

.sl-warn-btn {
  .sl-btn(@warn-btn-bg, @warn-btn-bg);
}

.sl-primary-light-btn {
  .sl-btn(@light-btn-bg, @light-btn-bg);
}

.sl-drag-btn {
  -webkit-user-modify: read-only;
  margin: 5px;
  background: #71a3ef;
  color: #fff;
  border-radius: 2px;
  padding: 1px 3px;
  border: 1px solid #71a3ef;
}
