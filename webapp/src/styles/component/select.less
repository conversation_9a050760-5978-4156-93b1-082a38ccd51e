@import "/src/styles/variables";
select.ng-valid.required {
  border: 1px solid #42a948 !important;
}

select.ng-invalid.ng-touched {
  border: 1px solid #a94442 !important;
}

.ant-select.ng-valid.required {
  &:not(.ant-select-customize-input) .ant-select-selector {
    border-color: #42a948;
  }
}

.ant-select.ng-invalid.ng-touched {
  &:not(.ant-select-customize-input) .ant-select-selector {
    border-color: #a94442;
  }
}

// ant select mutilple
.sl-ant-mutilple-select {
  &.ant-select-multiple {
    .ant-select-selector {
      flex-wrap: nowrap;
      overflow: hidden;

      .ant-select-selection-search {
        display: none;
      }
    }

    &.ant-select-show-arrow {
      .ant-select-selector {
        padding-right: 6px;
      }
    }
  }
}

.sl-ant-single-select {
  &.ant-select-single.ant-select-open {
    .ant-select-selection-item {
      color: #333;
    }
  }
  .ant-select-arrow {
    color: #405176;
  }
}

// 组合框
.sl-select-primary-bg {
  .ant-select {
    .ant-select-selector {
      border-color: @blue !important;
      background: @blue !important;
      padding: 0 5px !important;
      box-shadow: none !important;
      border-top-left-radius: 4px !important;
      border-bottom-left-radius: 4px !important;
    }

    .ant-select-selection-item {
      color: #fff !important;
      padding-right: 11px !important;
      text-align: center;
    }
    .ant-select-arrow {
      color: #fff;
      right: 5px;
    }
  }
  .ant-input-affix-wrapper {
    border-top-right-radius: 4px !important;
    border-bottom-right-radius: 4px !important;
    background: #eef3fc;
    .ant-input {
      background: #eef3fc;
    }
  }
}
