.sl-info-table-modal {
  .ant-modal-content {
    border-radius: 4px;
    .ant-modal-body {
      padding: 0;
    }
  }
}
.sl-info-table-modal-container {
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  .info-table-modal__title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #0e3add;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    color: #fff;
    height: 36px;
    font-size: 16px;
    padding: 0 20px;
    a {
      display: flex;
    }
  }
  .info-table-modal__body {
    padding: 8px 10px;
    overflow: hidden;
    background: #ffffff;
    &.subtitle {
      padding-top: 0;
      padding-left: 0;
      padding-right: 0;
    }
    .info-table-modal__body__title {
      height: 32px;
      line-height: 32px;
      background-color: #f3f8ff;
      padding: 0 20px;
    }
  }
  .info-table-modal__footer {
    display: flex;
    height: 85px;
    justify-content: center;
    align-items: center;
    button + button {
      margin-left: 28px;
    }
  }
}
