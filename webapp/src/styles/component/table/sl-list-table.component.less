@t-head-bg: #eef4fe;
@t-head-border-color: #d7dbe8;
@t-content-bg: #fff;
@t-content-border-color: #d7dbe8;

.sl-table-list {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;

  tr {
    td {
      box-sizing: border-box;
      padding: 0 6px;
      height: 36px;
      font-size: 14px;
      color: #333;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;

      &.serial {
        width: 50px;
        text-align: center;
      }

      &.text {
        i.hover {
          cursor: pointer;
        }
      }

      &.link {
        a {
          // display      : flex;
          // align-items  : center;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;

          i {
            float: left;
            margin-top: 3px;
          }
        }

        i.hover {
          cursor: pointer;
        }
      }

      &.btns {
        text-align: center;

        a {
          & + a {
            margin-left: 10px;
          }
        }

        .btn-blue-text {
          color: #0f57f6;
        }

        .btn-cyan-text {
          color: #0e788b;
        }

        .btn-violet-text {
          color: #9e11b1;
        }

        .btn-orange-text {
          color: #eb990d;
        }
      }
    }
  }

  .t-head {
    tr {
      background-color: @t-head-bg;

      td {
        text-align: center;
        border-left: 1px solid @t-head-border-color;
        border-top: 1px solid @t-head-border-color;
        border-bottom: 1px solid @t-head-border-color;

        &:last-child {
          border-right: 1px solid @t-head-border-color;
        }
      }
    }
  }

  .t-content {
    tr {
      background-color: @t-content-bg;
      &:not(.empty-container):hover {
        background-color: #ebf3ff;
      }
      &:not(:first-child) {
        border-top: 1px solid @t-content-border-color;
      }

      &:last-child {
        border-bottom: 1px solid @t-content-border-color;
      }

      td {
        border-left: 1px solid @t-content-border-color;

        &:last-child {
          border-right: 1px solid @t-content-border-color;
        }
      }
    }
  }
}
