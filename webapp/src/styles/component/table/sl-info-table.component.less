@td-border-color: #cdd2da;
@td-borer-style: 1px solid @td-border-color;

.sl-table-info {
  width: 100%;
  table-layout: fixed;

  tr {
    td {
      padding: 4px 6px;

      &.textarea {
        textarea {
          float: left;
          resize: none;
          border: 1px solid #cdd2da;
          padding: 2px 4px;

          &,
          &:focus {
            outline: none;
          }
        }
      }

      &.td-label {
        text-align: right;
        font-size: 14px;
        position: relative;
        padding-right: 8px;
        padding-left: 8px;
        color: #666;
        &.required {
          &::before {
            content: "*";
            color: #db3d3d;
            position: absolute;
            font-size: 14px;
            top: 50%;
            margin-top: -8px;
            left: 5px;
          }
        }
      }

      &.td-content {
        text-align: left;
        position: relative;
        color: #333;
        // 不换行
        &.no-wrap {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .td-radio {
          margin: 5px;
        }

        .td-radio:nth-of-type(1) {
          margin-left: 0;
        }
      }
    }
  }

  &.has-border {
    tr {
      td {
        border-left: @td-borer-style;
        border-top: @td-borer-style;
        overflow: hidden;

        &:last-child {
          border-right: @td-borer-style;
        }
      }

      &:last-child {
        td {
          border-bottom: @td-borer-style;
        }
      }
    }
  }

  &.has-label-bg {
    tr {
      td.label {
        background-color: #e7f1ff;
      }
    }
  }

  &.compact {
    tr {
      td {
        padding: 2px 4px;
      }
    }
  }
}
