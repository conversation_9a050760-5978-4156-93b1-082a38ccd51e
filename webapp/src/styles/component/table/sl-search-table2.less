.sl-table-search-container2 {
  display: flex;
  align-items: center;
  .sl-table-search__content {
    .row-container {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      margin-top: 10px;
      .colum-item {
        display: inline-flex;
        align-items: center;
        margin-bottom: 10px;
        height: 36px;
        .label {
          min-width: 120px;
          text-align: right;
        }
      }
    }
  }

  .sl-table-search-handle {
    // flex-grow: 0;
    display: flex;
    flex-wrap: nowrap;
    margin-bottom: 8px;
    padding: 20px 0 15px 0;
    margin-left: 100px;
    button + button {
      margin-left: 20px;
    }
  }
  .sl-table-btn-handle {
    position: absolute;
    right: 0;
  }
}
@media screen and (min-width: @screen-md-min) and (max-width: 1500px) {
  .sl-table-search-container2 {
    .sl-table-search__content {
      .row-container {
        .colum-item {
          min-width: 50%;
        }
      }
    }
  }
}
