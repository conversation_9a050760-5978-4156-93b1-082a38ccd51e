.sl-handle-table-modal-container {
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  .handle-table-modal__title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #0e3add;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    color: #fff;
    height: 36px;
    font-size: 16px;
    padding: 0 20px;
    a {
      display: flex;
    }
  }
  .handle-table-modal__body {
    padding: 8px 10px;
    overflow: hidden;
    background: #ffffff;
    &.subtitle {
      padding-top: 0;
    }
    .handle-table-modal__body__title {
      height: 40px;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid #e9ecf1;
      span {
        display: flex;
        align-items: center;
        b {
          display: flex;
          width: 14px;
          height: 14px;
          margin-right: 4px;
          font-weight: 500;
          background-color: #0e3add;
          align-items: center;
          justify-content: center;
          color: #fff;
        }
      }
      a {
        display: flex;
        align-items: center;
        color: #0e3add;
        font-size: 14px;
        width: 48px;
        justify-content: space-between;
      }
    }
    .handle-table-modal__body__content {
      padding-top: 8px;
    }
  }
  .handle-table-modal__footer {
    display: flex;
    height: 85px;
    justify-content: center;
    align-items: center;
    button + button {
      margin-left: 28px;
    }
  }
}
