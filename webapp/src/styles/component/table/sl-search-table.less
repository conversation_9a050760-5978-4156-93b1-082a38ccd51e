.sl-table-search-container {
  display: flex;
  flex-direction: row;
  border-bottom: 1px solid #d8dcdf;
  margin-left: -10px;
  margin-right: -10px;
  .sl-table-search__content {
    flex-grow: 1;
    .sl-table-search {
      width: 100%;
      tr {
        td {
          padding: 6px 6px;
          &.td-label {
            text-align: right;
            font-size: 14px;
            position: relative;
            padding-right: 12px;
            padding-left: 14px;
          }
          &.td-cotnent {
            text-align: left;
          }
          &.datepickerRange {
            .datepicker-range-container {
              display: flex;
              flex-direction: row;
              flex-wrap: nowrap;
              align-items: center;
              .sl-radio-group {
                padding: 5px 0;
                .ant-radio-wrapper {
                  margin-right: 0;
                  &:not(.one) {
                    margin-left: 10px;
                  }
                  &:first-child {
                    margin-left: 0;
                  }
                  &.inactive {
                    display: none;
                  }
                }
              }
              .datepicker-content {
                display: flex;
                flex-direction: row;
                flex-wrap: nowrap;
                align-items: center;
                .time-clear {
                  margin-left: 4px;
                }
              }
            }
          }
        }
      }
    }
  }

  .sl-table-search-handel {
    display: flex;
    width: 180px;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    button {
      margin: 0 10px;
    }
  }
}
