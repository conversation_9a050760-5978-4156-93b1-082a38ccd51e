.sl-handle-table-modal {
  .ant-modal-content {
    border-radius: 4px;

    .ant-modal-body {
      padding: 0;
    }
  }
}

@td-border-color: #cdd2da;
@td-borer-style: 1px solid @td-border-color;

.sl-table-handle {
  width: 100%;

  tr {
    td {
      padding: 4px 6px;

      &.textarea {
        textarea {
          float: left;
          resize: none;
          border: 1px solid #cdd2da;
          padding: 2px 4px;

          &,
          &:focus {
            outline: none;
          }
        }
      }

      &.label {
        text-align: right;
        font-size: 14px;
        position: relative;
        padding-right: 12px;
        padding-left: 14px;
        &.required {
          span {
            position: relative;
          }

          span::before {
            content: "*";
            color: #db3d3d;
            position: absolute;
            font-size: 14px;
            top: 50%;
            margin-top: -8px;
            left: -10px;
          }
        }
      }

      &.content {
        text-align: left;
        position: relative;

        .control {
          width: 200px;
        }

        input[type="number"] {
          &::-webkit-outer-spin-button,
          &::-webkit-inner-spin-button {
            -webkit-appearance: none;
          }

          -moz-appearance: textfield;

          &.d,
          &.m {
            width: 40px;
          }

          &.s {
            width: 50px;
          }

          & + sup {
            margin: 0;
          }
        }
      }

      .unit {
        margin-left: 4px;
      }
    }
  }

  &.has-border {
    tr {
      td {
        border-left: @td-borer-style;
        border-top: @td-borer-style;
        overflow: hidden;

        &:last-child {
          border-right: @td-borer-style;
        }
      }

      &:last-child {
        td {
          border-bottom: @td-borer-style;
        }
      }
    }
  }

  &.has-label-bg {
    tr {
      td.label {
        background-color: #e7f1ff;
      }
    }
  }
}
