{"name": "zbear-admin-angular", "version": "0.0.0", "scripts": {"开发模式（开发人员）：后端未加密 npm start ": "npm start", "start": "ng serve", "18号机测试模式（测试人员）：后端加密": "npm run test18", "starttest": "ng serve -c test18", "18号机测试 aot 模式（测试人员:）：后端加密": "npm run test18prod", "test18prod": "ng serve -c test18prod", "本地主机测试模式（临时测试使用）": "npm run testLocal", "testLocal": "ng serve -c testlocal", "pullandcommit": "git pull origin && git add ../ && git commit -m '.' && git push origin master ", "svnupdate": "git pull origin", "前端生产模式打包": "", "fprod": " npm run svnupdate && ng build --prod", "前端开发模式打包": "", "fdev": "npm run svnupdate && ng build --prod", "后端生产模式打包": "", "bprod": "gradle clean build -Pprofile=prod", "后端开发模式打包": "", "bdev": "gradle clean build -Pprofile=dev", "后端proddev模式 部署在weblogic下的dev模式 ": "", "bproddev": "gradle clean build -Pprofile=proddev", "上传12服务器并部署": "", "upload": "gradle upload", "上传生产服务器并署": "", "upload2Prod": "cd .. && gradle upload2Prod", "默认生产模式打包": "", "package": " cd .. && gradle clean build -Pprofile=prod && removelibs.cmd", "生产模式打包并上传到正式环境部署": "", "packageAndUpload": "npm run package && cd .. && gradle upload2Prod", "开发模式打包": "", "packagedev": "npm run fdev && npm run bproddev", "proddev模式打包": "", "packageproddev": "npm run fprod && npm run bproddev", "proddev模式部署": "", "deploy": "npm run packagedev && npm run upload", "后端打包 上传 并部署": "", "deployb": "npm run bproddev && npm run upload", "deploybtest": " cd .. && gradle clean build -Pprofile=test && gradle upload", "deploytest": "npm run fprod   && cd .. && gradle clean build -Pprofile=test && gradle upload", "test": " cd .. && gradle clean build -Pprofile=test "}, "private": true, "dependencies": {"@angular/animations": "~11.2.14", "@angular/common": "~11.2.14", "@angular/compiler": "~11.2.14", "@angular/core": "~11.2.14", "@angular/forms": "~11.2.14", "@angular/platform-browser": "~11.2.14", "@angular/platform-browser-dynamic": "~11.2.14", "@angular/router": "~11.2.14", "@antv/g2": "^4.1.21", "@supermap/iclient-leaflet": "^10.2.1", "@turf/turf": "^6.5.0", "classlist.js": "^1.1.20150312", "crypto-js": "^3.1.9-1", "echarts": "^5.1.2", "jquery": "^3.6.0", "leaflet": "^1.7.1", "leaflet-polylinedecorator": "^1.6.0", "leaflet-textpath": "^1.2.3", "lodash": "^4.17.21", "moment": "^2.29.1", "ng-zorro-antd": "^11.4.2", "ngx-color-picker": "^11.0.0", "rbush": "^3.0.1", "rxjs": "~6.6.0", "tslib": "^2.0.0", "web-animations-js": "^2.3.2", "xlsx": "^0.17.1", "zone.js": "~0.11.3"}, "devDependencies": {"@angular-devkit/build-angular": "~0.1102.13", "@angular/cli": "~11.2.14", "@angular/compiler-cli": "~11.2.14", "@types/crypto-js": "^3.1.43", "@types/jasmine": "~3.6.0", "@types/jquery": "^3.5.5", "@types/leaflet": "^1.7.3", "@types/lodash": "^4.14.170", "@types/node": "^12.11.1", "codelyzer": "^6.0.0", "jasmine-core": "~3.6.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.1.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.0.3", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.5.0", "protractor": "~7.0.0", "ts-node": "~8.3.0", "tslint": "~6.1.0", "typescript": "~4.1.5"}}