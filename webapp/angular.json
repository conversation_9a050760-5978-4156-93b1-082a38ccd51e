{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": "2d6cc1d6-f1d9-48f2-97e1-7952561a9c7b"}, "version": 1, "newProjectRoot": "projects", "projects": {"zbear-admin-angular": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "less", "skipTests": true}, "@schematics/angular:application": {"strict": true}, "@schematics/angular:directive": {"skipTests": true}, "@schematics/angular:module": {"routing": true}, "@schematics/angular:service": {"skipTests": true}, "@schematics/angular:class": {"skipTests": true}, "@schematics/angular:pipe": {"skipTests": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "../www", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "aot": true, "allowedCommonJsDependencies": ["lodash", "j<PERSON>y", "leaflet", "leaflet-polylinedecorator", "crypto-js", "xlsx"], "assets": ["src/favicon.ico", "src/assets", {"glob": "**/*", "input": "./node_modules/@ant-design/icons-angular/src/inline-svg/", "output": "/assets/"}], "styles": ["src/styles.less"], "scripts": []}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "8mb"}, {"type": "anyComponentStyle", "maximumWarning": "15kb", "maximumError": "15kb"}]}, "test18": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.test18.ts"}]}, "testlocal": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.local.ts"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "zbear-admin-angular:build", "proxyConfig": "proxy.conf.json", "port": 4200}, "configurations": {"production": {"browserTarget": "zbear-admin-angular:build:production"}, "test18": {"browserTarget": "zbear-admin-angular:build:test18", "proxyConfig": "proxy.conf.test18.json", "port": 4226, "host": "************", "disableHostCheck": true}, "test18prod": {"browserTarget": "zbear-admin-angular:build:test18", "proxyConfig": "proxy.conf.test18.json", "port": 4227, "host": "************", "disableHostCheck": true, "aot": true}, "testlocal": {"browserTarget": "zbear-admin-angular:build:testlocal", "proxyConfig": "proxy.conf.json", "port": 4228, "host": "*************", "disableHostCheck": true}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "zbear-admin-angular:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.less"], "scripts": []}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["tsconfig.app.json", "tsconfig.spec.json", "e2e/tsconfig.json"], "exclude": ["**/node_modules/**"]}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "zbear-admin-angular:serve"}, "configurations": {"production": {"devServerTarget": "zbear-admin-angular:serve:production"}}}}}}, "defaultProject": "zbear-admin-angular"}